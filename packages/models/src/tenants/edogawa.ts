import type { TenantInput } from './tenant';

export const edogawa = {
  id: 'edogawa',
  name: '江戸川区放置自転車対策課',
  shortName: '江戸川区',
  postalCode: '132-8501',
  address: '東京都江戸川区中央一丁目4番1号',
  prefecture: '東京都',
  city: '江戸川区',
  latlng: [35.707222, 139.868056],
  subdomain: 'edogawa',
  patrolFlow: 'find',
  announcementFlow: 'each',
  allowEnsureAbandoned: true,
  generateBicycleSamples: true,
  landmarks: [
    {
      name: '平井駅',
      address: '東京都江戸川区平井３丁目３０',
      lat: 35.706684414328336,
      lng: 139.84266905941968,
    },
    {
      name: '小岩駅',
      address: '東京都江戸川区南小岩7丁目',
      lat: 35.7335441011642,
      lng: 139.88187217660257,
    },
    {
      name: '西葛西駅',
      address: '東京都江戸川区西葛西６丁目１４−１',
      lat: 35.66475999216988,
      lng: 139.85926757917684,
    },
    {
      name: '葛西駅',
      address: '東京都江戸川区中葛西５丁目４３',
      lat: 35.66375736297544,
      lng: 139.87264217804858,
    },
    {
      name: '葛西臨海公園駅',
      address: '東京都江戸川区臨海町６丁目３',
      lat: 35.64454748966588,
      lng: 139.86156677465746,
    },
    {
      name: '船堀駅',
      address: '東京都江戸川区船堀３丁目６',
      lat: 35.68401190520752,
      lng: 139.8641705916418,
    },
    {
      name: '一之江駅',
      address: '東京都江戸川区一之江８丁目１４',
      lat: 35.686262603673846,
      lng: 139.88322752162185,
    },
    {
      name: '瑞江駅',
      address: '東京都江戸川区瑞江２丁目２−１',
      lat: 35.69345936013343,
      lng: 139.89771603328848,
    },
    {
      name: '篠崎駅',
      address: '東京都江戸川区篠崎町７丁目２７−１',
      lat: 35.706179348879864,
      lng: 139.90371979687595,
    },
    {
      name: '京成小岩駅',
      address: '東京都江戸川区北小岩２丁目１０',
      lat: 35.742252507757414,
      lng: 139.8839011766528,
    },
    {
      name: '江戸川駅',
      address: '東京都江戸川区北小岩３丁目２４',
      lat: 35.737845524416464,
      lng: 139.89614887918933,
    },
  ],
  storages: [
    {
      name: '江戸川区放置自転車保管所（仮設）',
      address: '東京都江戸川区中央1丁目',
      lat: 35.707,
      lng: 139.868,
    },
  ],
  labels: [],
} as const satisfies TenantInput;
