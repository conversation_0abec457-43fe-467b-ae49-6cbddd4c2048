import { oecIps } from 'common';
import type { TenantInput } from './tenant';

export const oec = {
  id: 'oec',
  name: 'OEC放置自転車対策チーム',
  shortName: 'OEC',
  postalCode: '103-0012',
  address: '東京都中央区日本橋堀留町1丁目9-8 人形町PREX8階',
  prefecture: '東京都',
  city: '中央区',
  latlng: [34.68150853505846, 135.50988577814468],
  subdomain: 'oec',
  ips: oecIps,
  patrolFlow: 'find',
  announcementFlow: 'monthly',
  allowEnsureAbandoned: true,
  generateBicycleSamples: true,
  landmarks: [
    {
      name: '銀座',
      address: '東京都中央区銀座4丁目1-2',
      lat: 35.671727,
      lng: 139.759292,
    },
  ],
  storages: [
    {
      name: '勝どき撤去自転車保管所',
      address: '東京都中央区勝どき6丁目3-1',
      lat: 35.65518,
      lng: 139.77275,
    },
  ],
  labels: [],
  storageSettings: {
    ips: oecIps,
    users: [{ id: 'oec-test', name: 'OECテストユーザー' }],
  },
} as const satisfies TenantInput;
