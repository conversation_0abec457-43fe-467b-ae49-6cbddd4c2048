#!/usr/bin/env tsx

import 'zx/globals';

// カラー出力のための設定
$.verbose = false;

type ErrorSummary = {
  file: string;
  line: number;
  errors: number;
};

/**
 * TypeScript型チェックを実行し、エラー情報を簡潔に表示する
 */
export async function typeCheckSummary(): Promise<void> {
  try {
    console.log(chalk.blue('🔍 Running TypeScript type check...'));

    // npm run typeを実行
    await $`npm run type`;

    console.log(chalk.green('✅ No type errors found!'));
  } catch (processOutput) {
    if (processOutput && typeof processOutput === 'object') {
      const output = processOutput as any;
      const stdout = output.stdout || '';

      if (stdout) {
        const errorSummary = parseTypeScriptErrors(stdout);
        displayErrorSummary(errorSummary);
      } else {
        console.log(chalk.red('❌ Failed to get TypeScript output'));
      }
    }
  }
}

/**
 * TypeScriptエラー出力を解析してファイルごとのエラー数を集計
 */
function parseTypeScriptErrors(output: string): ErrorSummary[] {
  const lines = output.split('\n');
  const fileErrors = new Map<string, ErrorSummary>();

  for (const line of lines) {
    // エラー行のパターン: src/path/to/file.tsx(123,45): error TS...
    const match = line.match(/^([^(]+)\((\d+),\d+\): error TS/);

    if (match) {
      const [, filePath, lineNumber] = match;

      const existing = fileErrors.get(filePath);
      if (existing) {
        existing.errors++;
      } else {
        fileErrors.set(filePath, {
          file: filePath,
          line: Number.parseInt(lineNumber, 10),
          errors: 1,
        });
      }
    }
  }

  // 元の順序を保持（Mapは挿入順序を保持）
  return Array.from(fileErrors.values());
}

/**
 * エラーサマリーを表示
 */
function displayErrorSummary(errorSummary: ErrorSummary[]): void {
  if (errorSummary.length === 0) {
    console.log(chalk.green('✅ No type errors found!'));
    return;
  }

  const totalErrors = errorSummary.reduce((sum, item) => sum + item.errors, 0);
  const totalFiles = errorSummary.length;

  console.log(`\nFound ${totalErrors} errors in ${totalFiles} files.\n`);
  console.log('Errors  Files\n');

  for (const { file, line, errors } of errorSummary) {
    const errorCount = errors.toString().padStart(6);
    console.log(`${errorCount}  ${file}:${line}`);
  }
}

// スクリプトが直接実行された場合
if (import.meta.url === `file://${process.argv[1]}`) {
  typeCheckSummary().catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}
