import * as cdk from 'aws-cdk-lib';

import { domainNameFromContext } from '../../util/funcs';

export const createPublicHostedZone = (scope: cdk.Stack) => {
  const hostDomainName = domainNameFromContext(scope);
  const hostedZone = new cdk.aws_route53.PublicHostedZone(scope, 'public-hosted-zone', {
    zoneName: hostDomainName,
  });
  new cdk.CfnOutput(scope, 'hostedZoneId', { value: hostedZone.hostedZoneId });
};
