import * as cdk from 'aws-cdk-lib';

import { s3TenantsPrefix } from 'common';

import type { EnvStackProps } from '../../../../util/type';
import { createLambda } from '../../utils/create-lambda';

type Props = EnvStackProps & {
  buckets: cdk.aws_s3.Bucket[];
};

export const createTransitLambda = (scope: cdk.Stack, props: Props) => {
  const { buckets } = props;

  const policies: cdk.aws_iam.PolicyStatement[] = buckets.map(
    (bucket) =>
      new cdk.aws_iam.PolicyStatement({
        effect: cdk.aws_iam.Effect.ALLOW,
        resources: [`${bucket.bucketArn}/*`],
        actions: ['s3:GetObject', 's3:PutObject'],
      }),
  );

  createLambda(scope, 'transit', { ...props, vpc: true, policies });
};
