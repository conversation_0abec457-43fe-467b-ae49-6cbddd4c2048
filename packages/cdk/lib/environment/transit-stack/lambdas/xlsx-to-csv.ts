import * as path from 'node:path';
import * as cdk from 'aws-cdk-lib';
import { Platform } from 'aws-cdk-lib/aws-ecr-assets';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import type { Construct } from 'constructs';

import { xlsxToCsvLambdaName } from 'common';

import { rootDir } from '../../../../util/funcs';
import type { EnvStackProps } from '../../../../util/type';
import { setNotifier } from '../../utils/set-notifier';

type XlsxToCsvProps = EnvStackProps & {
  buckets: cdk.aws_s3.Bucket[];
};

/**
 * Excel(xlsx)ファイルをCSVに変換するLambda関数を作成
 */
export const createXlsxToCsv = (scope: Construct, props: XlsxToCsvProps): lambda.Function => {
  const { envKey, buckets } = props;
  const name = 'xlsx-to-csv';
  const functionName = xlsxToCsvLambdaName(envKey);
  const policies: cdk.aws_iam.PolicyStatement[] = buckets.map(
    (bucket) =>
      new cdk.aws_iam.PolicyStatement({
        effect: cdk.aws_iam.Effect.ALLOW,
        resources: [`${bucket.bucketArn}/*`],
        actions: ['s3:GetObject', 's3:PutObject'],
      }),
  );

  // Lambda関数の作成
  const func = new lambda.DockerImageFunction(scope, `lambda-${name}`, {
    functionName,
    code: lambda.DockerImageCode.fromImageAsset(path.resolve(rootDir, 'lambdas/py/xlsx-to-csv'), {
      platform: Platform.LINUX_AMD64, // x86_64アーキテクチャを明示的に指定
    }),
    architecture: lambda.Architecture.X86_64,
    memorySize: 1024,
    timeout: cdk.Duration.seconds(60),
    initialPolicy: policies,
    environment: {
      ENV: envKey,
    },
    description: 'Excelファイル(xlsx)をCSV形式に変換するLambda関数 (コンテナ)',
  });
  // Python のエラーがサブスクリプションフィルターにマッチしない可能性あり。
  setNotifier(scope, name, { ...props, logGroupName: func.logGroup.logGroupName });
  return func;
};
