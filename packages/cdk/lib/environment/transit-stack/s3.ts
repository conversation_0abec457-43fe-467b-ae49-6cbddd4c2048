import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';

import { transitBucketName } from 'common';
import type { TenantInput } from 'models';

import type { EnvStackProps } from '../../../util/type';

type Props = EnvStackProps & {
  tenant: TenantInput;
};

export const createS3 = (scope: cdk.Stack, props: Props) => {
  const { envKey, tenant } = props;
  const bucket = new s3.Bucket(scope, `s3-${tenant.id}`, {
    bucketName: transitBucketName(envKey, tenant.id),
    publicReadAccess: false,
    blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
    removalPolicy: cdk.RemovalPolicy.DESTROY,
    autoDeleteObjects: true,
  });

  return bucket;
};
