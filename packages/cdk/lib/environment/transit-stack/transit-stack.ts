import * as cdk from 'aws-cdk-lib';
import type { Construct } from 'constructs';

import { tenants } from 'models';

import type { EnvStackProps } from '../../../util/type';
import { createTransitLambda } from './lambdas/transit';
import { createXlsxToCsv } from './lambdas/xlsx-to-csv';
import { createS3 } from './s3';

type Props = EnvStackProps;
export class TransitStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: Props) {
    super(scope, id, props);

    const buckets = tenants
      .filter((t) => t.transit)
      .map((t) => createS3(this, { ...props, tenant: t }));
    createTransitLambda(this, { ...props, buckets });
    createXlsxToCsv(this, { ...props, buckets });
  }
}
