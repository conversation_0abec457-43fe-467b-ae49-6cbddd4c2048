import * as cdk from 'aws-cdk-lib';
import { RemoteOutputs } from 'cdk-remote-stack';
import type { Construct } from 'constructs';

import { naming } from 'common';

import { certificateArnOutputId, envCertificateArnParamName } from '@/util/funcs';
import type { EnvStackProps } from '@/util/type';

type Props = EnvStackProps & {
  acmStack: cdk.Stack;
};

export class AcmWriterStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: Props) {
    super(scope, id, props);

    const outputs = new RemoteOutputs(this, naming(props.envKey, 'amc-writer'), {
      stack: props.acmStack as any,
      alwaysUpdate: true,
    });

    const acmArn = outputs.get(certificateArnOutputId);
    new cdk.CfnOutput(this, certificateArnOutputId, { value: acmArn });
    new cdk.aws_ssm.StringParameter(this, 'certificate-arn', {
      parameterName: envCertificateArnParamName(props.envKey),
      stringValue: acmArn,
    });
  }
}
