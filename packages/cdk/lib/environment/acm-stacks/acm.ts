import * as cdk from 'aws-cdk-lib';
import * as cert from 'aws-cdk-lib/aws-certificatemanager';
import type { Construct } from 'constructs';

import { naming, objectFromEntries } from 'common';

import {
  certificateArnOutputId,
  createDomainName,
  domainNameFromContext,
} from '../../../util/funcs';
import type { EnvStackProps } from '../../../util/type';

// ACM制限による：https://docs.aws.amazon.com/acm/latest/userguide/acm-limits.html
// １つの資格情報で最大10個のドメイン名だけを指定できますので、１つ環境が１つの資格情報を使うようにします。
// ドメイン名とワイルドカードの組み合わせは1つの資格情報で複数サブドメインをカバーできます。
// 今後サブドメインが増える場合は、新しい資格を作成する必要がありません。
export class AcmStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: EnvStackProps) {
    super(scope, id, props);

    const { envKey } = props;

    const hostDomainName = domainNameFromContext(this);
    const hostedZone = cdk.aws_route53.HostedZone.fromLookup(this, 'hosted-zone', {
      domainName: hostDomainName,
    });
    new cdk.CfnOutput(this, 'hostedZoneId', { value: hostedZone.hostedZoneId });

    const domainName = createDomainName(this, envKey);
    const domainNames = [domainName, `*.${domainName}`];
    const hostedZones = objectFromEntries(domainNames.map((name) => [name, hostedZone]));

    const certificate = new cert.Certificate(this, naming(envKey, 'certificate'), {
      certificateName: naming(envKey, 'certificate'),
      domainName,
      subjectAlternativeNames: domainNames,
      validation: cert.CertificateValidation.fromDnsMultiZone(hostedZones),
    });
    new cdk.CfnOutput(this, certificateArnOutputId, { value: certificate.certificateArn });
  }
}
