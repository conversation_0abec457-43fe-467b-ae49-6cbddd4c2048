import * as cdk from 'aws-cdk-lib';

import {
  createDomainNames,
  domainNameFromContext,
  envCertificateArnParamName,
} from '../../../util/funcs';
import type { EnvStackProps } from '../../../util/type';

type Props = EnvStackProps & {
  tenantSubdomains: string[];
};

export const acmReader = (scope: cdk.Stack, props: Props) => {
  const { envKey, tenantSubdomains } = props;

  // TODO: Cloudfront alternative domains limit 100 domains per distribution
  // https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/cloudfront-limits.html#limits-web-distributions
  const domainNames = createDomainNames(scope, envKey, tenantSubdomains);

  const { valueForStringParameter } = cdk.aws_ssm.StringParameter;

  const hostDomainName = domainNameFromContext(scope);
  const hostedZone = cdk.aws_route53.HostedZone.fromLookup(scope, 'hosted-zone', {
    domainName: hostDomainName,
  });

  const certificateArn = valueForStringParameter(scope, envCertificateArnParamName(envKey));
  new cdk.CfnOutput(scope, 'certificateArn', { value: certificateArn });

  const certificate = cdk.aws_certificatemanager.Certificate.fromCertificateArn(
    scope,
    'certificate',
    certificateArn,
  );

  return { domainNames, hostedZone, certificate };
};
