import * as cdk from 'aws-cdk-lib';
import { RemoteOutputs } from 'cdk-remote-stack';
import type { Construct } from 'constructs';

import { naming } from 'common';

import { envWebAclArnParamName, webAclArnOutputId } from '@/util/funcs';
import type { EnvStackProps } from '@/util/type';

type Props = EnvStackProps & {
  wafStack: cdk.Stack;
};

export class WafWriterStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: Props) {
    super(scope, id, props);

    const outputs = new RemoteOutputs(this, naming(props.envKey, 'waf-writer'), {
      stack: props.wafStack as any,
      alwaysUpdate: true,
    });

    const webAclArn = outputs.get(webAclArnOutputId);
    new cdk.CfnOutput(this, webAclArnOutputId, { value: webAclArn });
    new cdk.aws_ssm.StringParameter(this, 'web-acl-arn', {
      parameterName: envWebAclArnParamName(props.envKey),
      stringValue: webAclArn,
    });
  }
}
