import * as cdk from 'aws-cdk-lib';

import { type EnvKey, memberEnvKeys } from 'common';
import { envWebAclArnParamName } from '../../../util/funcs';

export const wafReader = (scope: cdk.Stack, envKey: EnvKey) => {
  if (memberEnvKeys.includes(envKey)) return undefined;
  const { valueForStringParameter } = cdk.aws_ssm.StringParameter;
  const webAclArn = valueForStringParameter(scope, envWebAclArnParamName(envKey));
  new cdk.CfnOutput(scope, 'webAclArn', { value: webAclArn });

  return webAclArn;
};
