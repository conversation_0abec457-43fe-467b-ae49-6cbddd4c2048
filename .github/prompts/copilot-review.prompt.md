# Copilotのレビュー方針

- 関数型プログラミングとReactのベストプラクティスとアンチパターンを考慮して指摘してください。
- 可能であればベストプラクティスに基づいた改善案の提示してください。（コードブロックで囲む）
- 関数型プログラミングを推奨しますが`Array.prototype.reduce()`の乱用を禁止します。代わりに`for .. of`を使用することを推奨します。

## `Array.prototype.reduce()`の乱用を禁止について

`reduce()` の初期値は、`0`,`''`,`[]` などのシンプルなデータに限定してください。
初期値にオブジェクトをセットして複雑な構造を持つデータを組み立てようとするコードは書かないでください。

そういうケースでは`let`+`for .. of`で書かれたコードの方が可読性に優れています。
そのようなケースにおいて、`let`,`for`,`if` といった関数型プログラミングでは避けられているパターンを許可します。
