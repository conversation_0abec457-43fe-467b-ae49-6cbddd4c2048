# テナント設定ファイルの作成

テナント設定ファイルの作成をお願いします。

## 基本設定

### テナントIDの設定
「テナントIDは <tenantId> です。」というテキストから、`tenantId`を以下の場所に適用してください。

- ファイルを `packages/models/src/tenants/<tenantId>.ts` に作成してください。
- 他の `packages/models/src/tenants/*.ts` ファイルと同様の形式で、`<tenantId>` を変数としてエクスポートしてください。
- id, subdomain は `<tenantId>` としてください。

e.g. 「テナントIDは taitou です。」
```ts
export const taitou = {
  id: 'taitou',
  subdomain: 'taitou',
  // 他の必要なプロパティをここに追加
}
```

### テナント名称の設定
「テナント名称は `<tenantName>` です。」というテキストから、`tenantName`を以下の場所に適用してください。
- name: `<tenantName>放置自転車対策課` としてください。
- shortName: `<tenantName>` としてください。

### 基本情報の収集
正確なテナント情報を知るために、`<tenantName>`を検索して、以下の情報を埋めてください。

- postalCode
- address
- prefecture

## 駅情報の収集

`<tenantName>` に含まれる駅を正確に把握してください。

## 駅の把握手順

### 1. MapFanから駅一覧を取得
https://mapfan.com/genres/1 から該当する都道府県・市区町村を選択し、駅一覧と詳細住所を取得してください。

例：杉並区の場合
- https://mapfan.com/genres/1/13（東京都）
- https://mapfan.com/genres/1/13/115（杉並区）

### 2. 高精度な緯度経度の取得
各駅の正確な座標は、以下のJSONデータソースから取得してください：
```bash
curl -s "https://raw.githubusercontent.com/piuccio/open-data-jp-railway-stations/refs/heads/master/stations.json" | jq '.[] | select(.name_kanji != null and (.name_kanji | contains("駅名1") or contains("駅名2"))) | {name: .name_kanji, lat: .stations[0].lat, lng: .stations[0].lon}'
```

**重要な注意点：**
- 駅名は「ケ」と「ヶ」の表記の違いに注意（例：「阿佐ケ谷」vs「阿佐ヶ谷」）
- 駅名から「駅」は省略されている
- 都道府県コードでフィルタリング可能（東京都=13）

### 3. Google Mapsの制限について
Google Mapsから直接的に高精度座標を自動取得することは困難です。
表示される座標は地図の中心であり、正確な位置ではありません。

### 4. 区役所などの施設について
駅以外の施設（区役所等）の座標は、上記のJSONに含まれないため、手動での調査が必要です。
