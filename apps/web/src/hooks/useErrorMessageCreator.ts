import { useAppContext } from '@/contexts/app-context';
import { type ErrorCode, isErrorCode, isNullish } from 'common';

export const useErrorMessageCreator = () => {
  const { labels } = useAppContext();
  return (code: ErrorCode | (string & {}) | null | undefined): string | undefined => {
    if (isNullish(code)) return undefined;
    if (!isErrorCode(code)) return 'サーバーエラーが発生しました。管理者へお問い合わせください。';
    return labels.error[code];
  };
};
