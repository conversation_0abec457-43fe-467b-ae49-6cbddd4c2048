import { useAppContext } from '@/contexts/app-context';
import { PriceSuggestionCreatePage } from '@/features/settings/price-suggestions/create/_PriceSuggestionsCreate';
import { PriceSuggestionDetailsPage } from '@/features/settings/price-suggestions/details/_PriceSuggestionDetailsPage';
import { PriceSuggestionEditPage } from '@/features/settings/price-suggestions/edit/_PriceSuggestionEditPage';
import { GradesSettingsPage } from '@/features/settings/price-suggestions/list/_PriceSuggestionsPage';

import { createRoutes } from '@/router/core/funcs';
import { StarHalf } from '@mui/icons-material';

export const priceSuggestionRouters = createRoutes({
  path: '/settings/price-suggestions',
  Icon: StarHalf,
  useTitle: () => `${useAppContext().labels.assessment.price}補完候補`,
  useCount: undefined,
  components: {
    list: GradesSettingsPage,
    details: PriceSuggestionDetailsPage,
    create: PriceSuggestionCreatePage,
    edit: PriceSuggestionEditPage,
  },
});
