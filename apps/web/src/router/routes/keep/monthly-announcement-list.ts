import { Announcement } from '@mui/icons-material';

import { useAppContext } from '@/contexts/app-context';
import { MonthlyAnnouncementListsPage } from '@/features/bicycles/monthly-announcement-lists/list/_MonthlyAnnouncementListsPage';
import {
  // createCreateRoute,
  createListRoute,
  // createRouteWithMeta,
} from '@/router/core/funcs';

const args = {
  path: '/monthly-announcement-list',
  Icon: Announcement,
  useTitle: () => {
    const { labels } = useAppContext();
    return `${labels.domain.announcementSheet}` as const;
  },
  useCount: undefined,
} as const;

export const monthlyAnnouncementLists = createListRoute({
  ...args,
  component: MonthlyAnnouncementListsPage,
});
// export const announcementCreate = createCreateRoute({
//   ...args,
//   component: AnnouncementCreatePage,
// });
// export const announcementEdit = createRouteWithMeta({
//   ...args,
//   path: `${args.path}/edit`,
//   useTitle: () => `${args.useTitle()}編集` as const,
//   component: AnnouncementEditPage,
// });
