import { Print } from '@mui/icons-material';
import { createRoute } from '@tanstack/react-router';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { SerialTagPrintPage } from '@/features/serial-tags/print/_SerialTagPrintPage';
import { appRoute } from '../../core/app';
import type { RouteMeta } from '../../core/types';

export const serialTagPrintMeta = {
  type: 'item',
  path: '/serial-tags/print',
  Icon: Print,
  useTitle: () => {
    const { labels } = useAppContext();
    return `${labels.p.serialTag}${labels.action.print}` as const;
  },
  useCount: () => {
    const { data = [] } = trpc.bicycles.list.useQuery({ type: 'print' });
    return data.filter((b) => b.events.every((e) => e.type !== 'printSerialTag')).length;
  },
} as const satisfies RouteMeta;

export const serialTagPrintRoute = createRoute({
  path: serialTagPrintMeta.path,
  getParentRoute: () => appRoute,
  component: SerialTagPrintPage,
});
