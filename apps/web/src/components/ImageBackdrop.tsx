import { styled } from '@mui/material';

type Props = {
  type: 'add' | 'edit';
};

export default function ImageBackdrop({ type }: Props) {
  const CustomSpan = styled('span')(({ theme }) => ({
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: theme.palette.common.black,
    opacity: type === 'add' ? 0.1 : 0,
    transition: theme.transitions.create('opacity'),
  }));

  return <CustomSpan className="MuiImageBackdrop-root" />;
}
