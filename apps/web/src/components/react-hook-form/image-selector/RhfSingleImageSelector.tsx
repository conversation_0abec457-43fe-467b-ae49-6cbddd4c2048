import { imageGetter } from '@/funcs/image';
import { RhfSingleImageSelector as ExRhfSingleImageSelector } from 'mui-ex';
import type React from 'react';
import type { StrictOmit } from 'ts-essentials';

type Props = StrictOmit<React.ComponentProps<typeof ExRhfSingleImageSelector>, 'imageGetter'>;

export const RhfSingleImageSelector = (props: Props) => {
  return <ExRhfSingleImageSelector {...props} imageGetter={imageGetter} />;
};
