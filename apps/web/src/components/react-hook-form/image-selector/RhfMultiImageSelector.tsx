import { imageGetter } from '@/funcs/image';
import { RhfMultiImageSelector as ExRhfMultiImageSelector } from 'mui-ex';
import type React from 'react';
import type { StrictOmit } from 'ts-essentials';

type Props = StrictOmit<React.ComponentProps<typeof ExRhfMultiImageSelector>, 'imageGetter'>;

export const RhfMultiImageSelector = (props: Props) => {
  return <ExRhfMultiImageSelector {...props} imageGetter={imageGetter} />;
};
