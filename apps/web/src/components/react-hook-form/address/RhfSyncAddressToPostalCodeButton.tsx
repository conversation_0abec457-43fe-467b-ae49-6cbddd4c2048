import { IconButton, type IconButtonProps, Tooltip } from '@mui/material';
import { type Control, type UseFormWatch, useController } from 'react-hook-form';
import type { StrictOmit } from 'ts-essentials';

import { isNullish } from 'common';
import { extractPrefecture } from 'models';

import { addressToPostalCodes } from '@/utils/address';
import { useAddressList } from './api';

type Props = StrictOmit<IconButtonProps, 'name'> & {
  control: Control<any>;
  watch: UseFormWatch<any>;
  name: {
    address: string;
    postalCode: string;
  };
};

export const RhfSyncAddressToPostalCodeButton = (props: Props) => {
  const { control, watch, name, ...buttonProps } = props;
  const address: string | null | undefined = watch(name.address);
  const prefecture = extractPrefecture(address);
  const { data = [] } = useAddressList(prefecture);
  const codes = addressToPostalCodes(address, prefecture, data);

  const code = codes?.length === 1 ? codes[0].code : undefined;

  const {
    field: { onChange },
  } = useController({ control, name: name.postalCode });

  const handleClick = () => onChange(code);

  return (
    <Tooltip title="住所を郵便番号に変換" placement="bottom">
      <IconButton {...buttonProps} onClick={handleClick} disabled={isNullish(code)} />
    </Tooltip>
  );
};
