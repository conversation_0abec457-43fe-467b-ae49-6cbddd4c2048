import { IconButton, type IconButtonProps, Tooltip } from '@mui/material';
import { type Control, type UseFormWatch, useController } from 'react-hook-form';
import type { StrictOmit } from 'ts-essentials';

import { isNullish } from 'common';

import { usePostalCode } from './api';

type Props = StrictOmit<IconButtonProps, 'name'> & {
  control: Control<any>;
  watch: UseFormWatch<any>;
  name: {
    postalCode: string;
    address: string;
  };
};

export const RhfSyncPostalCodeToAddressButton = (props: Props) => {
  const { control, watch, name, ...buttonProps } = props;
  const postalCode: string | null | undefined = watch(name.postalCode);
  const {
    field: { onChange },
  } = useController({ control, name: name.address });
  const { data } = usePostalCode(postalCode);

  let next = `${data?.prefecture}${data?.city}`;
  if (data?.townArea !== '以下に掲載がない場合') next += data?.townArea;

  const handleClick = () => onChange(next);

  return (
    <Tooltip title="郵便番号を住所に変換" placement="bottom">
      <IconButton {...buttonProps} onClick={handleClick} disabled={isNullish(data)} />
    </Tooltip>
  );
};
