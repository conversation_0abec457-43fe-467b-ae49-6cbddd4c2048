import { Grid } from '@mui/material';
import { type Address, type AddressState, extractPrefecture } from 'models';
import type { Control, UseFormWatch } from 'react-hook-form';

import { PrefectureSchema } from 'common';
import { RhfSingleFreeSolo, RhfSingleSelect, RhfTextField } from 'mui-ex';
import { useAddressList } from './api';

export const defaultLocationState: AddressState = {
  prefecture: '',
  city: '',
  town: '',
  chome: '',
  furtherAddress: '',
};

export const addressToState = (address: Address | null | undefined): AddressState => {
  return {
    prefecture: address?.prefecture ?? '',
    city: address?.city ?? '',
    town: address?.town ?? '',
    chome: address?.chome ?? '',
    furtherAddress: address?.furtherAddress ?? '',
  };
};

type CommonProps = { readOnly?: boolean };
type State = AddressState;
type Props<T extends State> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<State>;
  watch: UseFormWatch<State>;
};

export const RhfComboAddress = <T extends State>(props: Props<T>) => {
  const { control, watch, readOnly } = props as unknown as InnerProps;
  const prefecture = watch('prefecture');
  const city = watch('city');
  const town = watch('town');

  const { data = [], isLoading } = useAddressList(extractPrefecture(prefecture));
  const groups = Map.groupBy(data, (d) => d.city);
  const cityOptions = groups.keys().toArray();
  const towns = groups.get(city);
  const townOptions =
    towns?.map((d) => d.townArea).filter((t) => t !== '以下に掲載がない場合') ?? [];
  const foundTown = towns?.find((d) => d.townArea === town);
  const chomeOptions = foundTown?.chomeList.map((c) => `${c}丁目`) ?? [];
  return (
    <Grid container spacing={2}>
      <Grid size={{ xs: 12, sm: 4, md: 3 }}>
        <RhfSingleSelect
          control={control}
          name="prefecture"
          label="都道府県"
          options={PrefectureSchema.options.map((p) => ({ label: p, value: p }))}
          readOnly={readOnly}
          loading={isLoading}
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 4, md: 3 }}>
        <RhfSingleFreeSolo
          control={control}
          name="city"
          label="市区町村"
          options={cityOptions}
          readOnly={readOnly}
          loading={isLoading}
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 4, md: 3 }}>
        <RhfSingleFreeSolo
          control={control}
          name="town"
          label="町名"
          options={townOptions}
          readOnly={readOnly}
          loading={isLoading}
        />
      </Grid>
      {foundTown?.hasChome && (
        <Grid size={{ xs: 12, sm: 4, md: 3 }}>
          <RhfSingleFreeSolo
            control={control}
            name="chome"
            label="丁目"
            options={chomeOptions}
            readOnly={readOnly}
            loading={isLoading}
          />
        </Grid>
      )}
      <Grid size={{ xs: 12, sm: 4, md: 3 }}>
        <RhfTextField
          control={control}
          name="furtherAddress"
          label="番地・建物名"
          readOnly={readOnly}
        />
      </Grid>
    </Grid>
  );
};
