import { trpc, trpcClient } from '@/api';
import { useQuery } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import { type Prefecture, isNullish } from 'common';

export const usePostalCode = (postalCode: string | null | undefined) =>
  useQuery({
    queryKey: getQueryKey(trpc.postalCodes.get, { code: postalCode ?? '' }),
    queryFn: async () => {
      if (isNullish(postalCode)) return null;
      if (!/^\d{7}$/.test(postalCode)) return null;
      return trpcClient.postalCodes.get.query({ code: postalCode });
    },
  });

export const useAddressList = (prefecture: Prefecture | undefined) =>
  useQuery({
    queryKey: getQueryKey(trpc.address.list, { prefecture: prefecture as any }),
    queryFn: async () => {
      if (prefecture === undefined) return [];
      return trpcClient.address.list.query({ prefecture });
    },
  });
