import { FormControl, InputLabel, MenuItem, Select, type SelectChangeEvent } from '@mui/material';
import { type Prefecture, PrefectureSchema } from 'common';

type Props = {
  prefecture: Prefecture;
  setPrefecture: React.Dispatch<React.SetStateAction<Prefecture>>;
};
export const PrefectureSelect = ({ prefecture, setPrefecture }: Props) => {
  const handleSelectPrefecture = (event: SelectChangeEvent<Prefecture>) => {
    setPrefecture(event.target.value);
  };

  return (
    <FormControl sx={{ width: 200 }}>
      <InputLabel>都道府県</InputLabel>
      <Select value={prefecture} onChange={handleSelectPrefecture}>
        {PrefectureSchema.options.map((p) => (
          <MenuItem key={p} value={p}>
            {p}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};
