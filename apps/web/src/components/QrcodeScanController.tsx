import { Box, Container, Paper, Stack, Typography } from '@mui/material';

import { useDevicesContext } from '@/contexts/camera-context';
import { QrcodeReader } from '@/libs/react-qr-scanner/QrcodeReader/QrcodeReader';
import { NoCameraMessages } from './NoCameraMessages';

type Props = {
  title: string;
  description: string;
  onScan: (decodedText: string) => void;
  BackButton?: React.JSX.Element;
};

export const QrcodeScanController = ({ title, description, onScan, BackButton }: Props) => {
  const { devices } = useDevicesContext();

  if (devices.length === 0) return <NoCameraMessages />;

  return (
    <Container
      component={Stack}
      maxWidth="md"
      sx={{
        height: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Paper
        component={Stack}
        sx={{
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Box sx={{ p: 2, width: 1 }}>
          <Typography variant="h5" fontWeight="Bold">
            {title}
          </Typography>
          <Typography>{description}</Typography>
        </Box>
        <QrcodeReader onSuccess={onScan} />
        <Box sx={{ p: 2 }}>{BackButton}</Box>
      </Paper>
    </Container>
  );
};
