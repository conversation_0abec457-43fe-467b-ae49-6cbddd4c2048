import React from 'react';

import { CameraAlt, Cameraswitch } from '@mui/icons-material';
import { Box, Fab, Menu, MenuItem, Toolbar, Typography } from '@mui/material';
import { Layer, Rect, Stage } from 'react-konva';
import Webcam from 'react-webcam';

import { isNullish } from 'common';

import { useDevicesContext } from '@/contexts/camera-context';
import { useResizeObserver } from '@/hooks/useResizeObserver';
import { useImageConstraints } from '@/stores/camera-constraints';
import { NoCameraMessages } from './NoCameraMessages';

const headerHeight = 64;
const footerHeight = 64;

const calcWidth = (rect: DOMRect, videoRect: { width: number; height: number }) => {
  const rectRatio = rect.width / rect.height;
  const videoRatio = videoRect.width / videoRect.height;
  if (rectRatio > videoRatio) return rect.width / videoRatio;
  if (rect.width > rect.height) return rect.height;
  return rect.width;
};

const calcHeight = (rect: DOMRect, videoRect: { width: number; height: number }) => {
  const rectRatio = rect.height / rect.width;
  const videoRatio = videoRect.height / videoRect.width;
  if (rectRatio > videoRatio) return rect.height / videoRatio;
  if (rect.height > rect.width) return rect.width;
  return rect.height;
};

interface Props {
  description: string;
  onCapture: (imageSrc: string) => void;
  CloseButton?: React.JSX.Element;
}

export const CameraCapture = ({ description, onCapture, CloseButton }: Props) => {
  const { ref, rect } = useResizeObserver();
  const webcamRef = React.useRef<Webcam | null>(null);
  const [videoRect, setVideoRect] = React.useState({ width: 1, height: 1 });
  const { devices } = useDevicesContext();
  const { constraints, select } = useImageConstraints();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) =>
    setAnchorEl(event.currentTarget);
  const handleSelect =
    (...args: Parameters<typeof select>) =>
    () => {
      select(...args);
      setAnchorEl(null);
    };

  React.useEffect(() => {
    let timerId: number | undefined = window.setInterval(() => {
      if (webcamRef.current === null) return;
      const element = webcamRef.current.video;
      if (isNullish(element)) return;
      ref(element);
      if (element.videoWidth !== 0 && element.videoHeight !== 0) {
        setVideoRect({ width: element.videoWidth, height: element.videoHeight });
        window.clearInterval(timerId);
        timerId = undefined;
      }
    });
    return () => {
      if (timerId !== undefined) {
        window.clearInterval(timerId);
      }
    };
  }, [ref]);

  const capture = () => {
    if (webcamRef.current === null) return;
    const imageSrc = webcamRef.current.getScreenshot();
    if (imageSrc) onCapture(imageSrc);
  };

  const width = calcWidth(rect, videoRect);
  const height = calcHeight(rect, videoRect);

  if (devices.length === 0) return <NoCameraMessages />;

  return (
    <Box
      sx={{
        position: 'relative',
        width: 1,
        height: 1,
        bgcolor: (t) => t.palette.background.darkGrey,
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: 1,
          height: headerHeight,
        }}
      >
        <Toolbar sx={{ height: headerHeight }}>
          {CloseButton}
          <Typography color="#fff" sx={{ ml: 2 }}>
            {description}
          </Typography>
        </Toolbar>
      </Box>
      <Box
        sx={{
          position: 'absolute',
          left: 0,
          top: headerHeight,
          width: '100%',
          height: `calc(100% - ${headerHeight}px - ${footerHeight}px)`,
        }}
      >
        <Box
          sx={{
            position: 'relative',
            width: 1,
            height: 1,
          }}
        >
          <Webcam
            ref={webcamRef}
            videoConstraints={constraints}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
            screenshotFormat="image/jpeg"
          />
          <Stage
            width={rect.width}
            height={rect.height}
            style={{ position: 'absolute', left: 0, top: 0 }}
          >
            <Layer>
              <Rect
                x={(rect.width - width) * 0.5}
                y={(rect.height - height) * 0.5}
                width={width}
                height={height}
                fill="#000f"
              />
              <Rect
                x={0}
                y={0}
                width={rect.width}
                height={rect.height}
                fill="#0008"
                globalCompositeOperation="source-out"
              />
            </Layer>
          </Stage>
        </Box>
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: rect.width * 0.5,
            width: 'max-content',
            height: 'max-content',
            transform: 'translate3d(-50%, 40%, 0)',
          }}
        >
          <Fab
            color="primary"
            sx={{ p: (t) => `${t.typography.fontSize * 2}px` }}
            onClick={capture}
          >
            <CameraAlt />
          </Fab>
        </Box>

        <Box
          sx={{
            position: 'absolute',
            right: (t) => t.spacing(1),
            top: (t) => t.spacing(1),
          }}
        >
          <Fab size="small" onClick={handleClick}>
            <Cameraswitch />
          </Fab>
          <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={() => setAnchorEl(null)}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            transformOrigin={{ vertical: 'top', horizontal: 'right' }}
          >
            <MenuItem onClick={handleSelect('environment')}>リアカメラ</MenuItem>
            <MenuItem onClick={handleSelect('user')}>フロントカメラ</MenuItem>
            {devices.map((device) => (
              <MenuItem key={device.deviceId} onClick={handleSelect('deviceId', device.deviceId)}>
                {device.label}
              </MenuItem>
            ))}
          </Menu>
        </Box>
      </Box>
    </Box>
  );
};
