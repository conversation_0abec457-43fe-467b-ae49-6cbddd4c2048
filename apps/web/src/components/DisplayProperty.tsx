import type React from 'react';

import { Edit } from '@mui/icons-material';
import { Box, IconButton, Typography } from '@mui/material';

type BoxProps = React.ComponentProps<typeof Box>;
type Props = {
  label?: string;
  value: string | null | undefined;
  onEdit?: () => void;
} & Omit<BoxProps, 'children'>;

export default function DisplayProperty({ label, value = '-', onEdit, ...props }: Props) {
  return (
    <Box
      {...props}
      sx={{
        ...props.sx,
        p: 1,
        mt: 1,
        pt: label !== undefined ? 0 : 1,
        borderRadius: 1,
        bgcolor: (theme) => theme.palette.background.innerGrey,
        width: '100%',
      }}
    >
      <Typography variant="caption">{label}</Typography>
      <Box sx={{ display: 'flex', alignItems: 'end' }}>
        <Typography sx={{ flexGrow: 1 }}> {value}</Typography>
        {onEdit && (
          <IconButton size="small" onClick={onEdit}>
            <Edit fontSize="inherit" />
          </IconButton>
        )}
      </Box>
    </Box>
  );
}
