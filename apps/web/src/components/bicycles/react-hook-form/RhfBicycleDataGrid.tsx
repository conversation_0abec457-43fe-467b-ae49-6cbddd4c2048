import { FormHelperText, type SxProps, type Theme } from '@mui/material';
import { type Control, useController } from 'react-hook-form';
import { z } from 'zod';

import { DataGrid, type DataGridProps } from 'mui-ex';

import type { GridRowParams } from '@mui/x-data-grid';
import type { Bicycles } from 'lambda-api';

export const BicycleDataGridStateSchema = z.object({
  bicycleIds: z.array(z.string()),
});
export type BicycleDataGridState = z.infer<typeof BicycleDataGridStateSchema>;

type CommonProps = {
  columns: DataGridProps['columns'];
  rows: Bicycles;
  loading?: boolean;
  readOnly?: boolean;
  slots?: DataGridProps['slots'];
  hideFooterSelectedRowCount?: boolean;
  isRowSelectable?: (params: GridRowParams<Bicycles[number]>) => boolean;
  sx?: SxProps<Theme>;
};
type Props<T extends BicycleDataGridState> = CommonProps & {
  control: Control<T>;
};
type InnerProps = CommonProps & {
  control: Control<BicycleDataGridState>;
};

export const RhfBicycleDataGrid = <T extends BicycleDataGridState>(props: Props<T>) => {
  const {
    control,
    columns,
    rows,
    loading,
    readOnly,
    slots,
    hideFooterSelectedRowCount,
    isRowSelectable,
    sx = [],
  } = props as unknown as InnerProps;

  const {
    field: { value, onChange },
    fieldState: { error },
  } = useController({ control, name: 'bicycleIds' });

  const editableProps: Pick<
    DataGridProps,
    'checkboxSelection' | 'rowSelectionModel' | 'onRowSelectionModelChange'
  > = !readOnly
    ? {
        checkboxSelection: true,
        rowSelectionModel: { type: 'include', ids: new Set(value) },
        onRowSelectionModelChange: ({ ids }) => onChange([...ids]),
      }
    : {};

  return (
    <>
      <DataGrid
        columns={columns}
        rows={rows.filter((r) => !readOnly || value.includes(r.id))}
        {...editableProps}
        disableRowSelectionOnClick
        hideFooterSelectedRowCount={hideFooterSelectedRowCount}
        isRowSelectable={isRowSelectable}
        slots={slots}
        loading={loading}
        sx={[
          {
            // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
            '& .MuiDataGrid-columnHeader:focus-within': { outline: 'none' },
            '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
            '& .MuiTablePagination-select': { mr: 1 },
          },
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
      />
      {error && <FormHelperText error>{error.message}</FormHelperText>}
    </>
  );
};
