import { useColorFromValue, useColorOptions } from '@/models/color';
import { Box, Chip, Stack } from '@mui/material';
import { RhfMultiAutocomplete } from 'mui-ex';
import type { FieldValues } from 'react-hook-form';
import type { StrictOmit } from 'ts-essentials';
import { ColorTile } from '../ColorTile';

type BaseProps<T extends FieldValues = FieldValues> = StrictOmit<
  React.ComponentProps<typeof RhfMultiAutocomplete<T>>,
  'options'
>;

const TagComponent: BaseProps['TagComponent'] = ({ option, ...props }) => {
  const color = useColorFromValue(option.value);
  const label = (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <ColorTile colorCode={color.code} size="small" sx={{ mr: 0.5 }} />
      {option.label}
    </Box>
  );
  return <Chip {...props} label={label} size="small" sx={{ m: 0 }} />;
};

const RenderOptionComponent: BaseProps['RenderOptionComponent'] = ({ label, value }) => {
  const color = useColorFromValue(value);
  return (
    <Stack direction="row" alignItems="center">
      <ColorTile colorCode={color.code} sx={{ mr: 1 }} />
      {label}
    </Stack>
  );
};

type Props<T extends FieldValues> = Omit<BaseProps<T>, 'TagComponent' | 'RenderOptionComponent'>;

export const RhfMultiColorAutocomplete = <T extends FieldValues>(props: Props<T>) => {
  const options = useColorOptions();
  return (
    <RhfMultiAutocomplete
      {...props}
      options={options}
      TagComponent={TagComponent}
      RenderOptionComponent={RenderOptionComponent}
    />
  );
};
