import { Chip, Stack } from '@mui/material';

import { BicycleTypeSchema, isNullish } from 'common';
import type { Bicycles } from 'lambda-api';
import { bicycleOwnerStatuses, toAddress } from 'models';
import { isAnyOfOperator } from 'mui-ex';

import { dateFormatter, datetimeFormat, datetimeFormatter } from '@/funcs/date';
import { type ColDefFactory, createUseColumns } from '@/hooks/useColumns';
import { bicycleToImage } from '@/models/bicycle';
import { BASE_URL } from '@/types/vite-env';
import type { Paths } from 'ts-essentials';
import { match } from 'ts-pattern';
import { createImageColProps } from '../ImageAvatar';
import { BicycleStatusChip } from './BicycleStatusChip';
import { ColorTile } from './ColorTile';

type Bicycle = Bicycles[number];
export type BicycleColFactory = ColDefFactory<Bicycle>;
export type BicycleColField = Paths<Bicycle>;

/** 日時 */
export const createdAtCol: BicycleColFactory = ({ labels, fit }) => ({
  field: 'createdAt',
  type: 'dateTime',
  headerName: labels.p.createdAt,
  width: fit(datetimeFormat),
  valueGetter: (_, row) => new Date(row.createdAt),
  valueFormatter: (value: Date) => datetimeFormatter(value),
});

export const bicycleToImageSrc = (bicycle: Bicycle) => {
  const key = bicycleToImage(bicycle)?.key;
  if (key === undefined) return null;
  return `${BASE_URL}/${key}`;
};

/** 写真 */
export const imagesCol: BicycleColFactory = () =>
  ({
    field: 'imageKey',
    disableExport: true,
    ...createImageColProps<Bicycle>((row) => {
      if (row.imageKey === undefined) return null;
      return `${BASE_URL}/${row.imageKey}`;
    }),
  }) as const;

/** ステータス */
export const statusCol: BicycleColFactory = ({ labels }) => ({
  field: 'status',
  headerName: labels.system.status,
  width: 200,
  valueGetter: (_, bicycle) => labels.et[bicycle.status],
  renderCell: ({ row: bicycle }) => <BicycleStatusChip status={bicycle.status} />,
});

/* 公示ステータス */
export const notificationStatusCol: BicycleColFactory = ({ labels, fit, rows }) => {
  const bicycles = rows as Bicycles;
  const groups = Map.groupBy(bicycles, (b) => b.notificationStatus);
  const valueOptions = Array.from(groups.entries()).map(([status, bicycles]) => {
    const text = labels.notificationStatus[status];
    return {
      label: `${text} (${bicycles.length})`,
      value: text,
    };
  });

  return {
    field: 'notificationStatus',
    type: 'singleSelect',
    headerName: labels.p.notificationStatus,
    width: fit(labels.p.notificationStatus),
    valueGetter: (_, row) => labels.notificationStatus[row.notificationStatus],
    valueOptions,
    filterOperators: [
      {
        label: '...のいずれか',
        value: isAnyOfOperator,
        defaultValue: [],
        getApplyFilterFn: () => null,
      },
    ],
  };
};

/* 公示ステータス */
export const announcementStatusCol: BicycleColFactory = ({ labels, fit, rows }) => {
  const bicycles = rows as Bicycles;
  const groups = Map.groupBy(bicycles, (b) => b.announcementStatus);
  const valueOptions = Array.from(groups.entries()).map(([status, bicycles]) => {
    const text = labels.announcementStatus[status];
    return {
      label: `${text} (${bicycles.length})`,
      value: text,
    };
  });

  return {
    field: 'announcementStatus',
    type: 'singleSelect',
    headerName: labels.p.announcementStatus,
    width: fit(labels.p.announcementStatus),
    valueGetter: (_, row) => labels.announcementStatus[row.announcementStatus],
    valueOptions,
    filterOperators: [
      {
        label: '...のいずれか',
        value: isAnyOfOperator,
        defaultValue: [],
        getApplyFilterFn: () => null,
      },
    ],
  };
};

/* 公示日 */
export const announcementStartCol: BicycleColFactory = ({ labels }) => {
  return {
    field: 'announcement.last.start',
    type: 'date',
    headerName: labels.p.announcementStart,
    valueGetter: (_, row) => row.announcement?.last?.start,
    valueFormatter: (value: Date | undefined) => dateFormatter(value),
  };
};

/* 期限ステータス */
export const deadlineStatusCol: BicycleColFactory = ({ labels, fit, rows }) => {
  const bicycles = rows as Bicycles;
  const groups = Map.groupBy(bicycles, (b) => b.deadlineStatus);
  const valueOptions = Array.from(groups.entries()).map(([status, bicycles]) => {
    const text = labels.deadlineStatus[status];
    return {
      label: `${text} (${bicycles.length})`,
      value: text,
    };
  });

  return {
    field: 'deadlineStatus',
    type: 'singleSelect',
    headerName: labels.p.deadlineStatus,
    width: fit(labels.p.deadlineStatus),
    valueGetter: (_, row) => labels.deadlineStatus[row.deadlineStatus],
    valueOptions,
    filterOperators: [
      {
        label: '...のいずれか',
        value: isAnyOfOperator,
        defaultValue: [],
        getApplyFilterFn: () => null,
      },
    ],
  };
};

export const notificationDateCol: BicycleColFactory = ({ labels }) => {
  return {
    field: 'notification.last.date',
    type: 'date',
    headerName: labels.p.notificationDate,
    valueGetter: (_, row) => row.notification?.last?.date,
    valueFormatter: (value: Date | undefined) => dateFormatter(value),
  };
};

/* 期限日 */
export const deadlineDateCol: BicycleColFactory = ({ labels }) => {
  return {
    field: 'deadline.last.date',
    type: 'date',
    headerName: labels.p.deadlineDate,
    valueGetter: (_, row) => row.deadline?.last?.date,
    valueFormatter: (value: Date | undefined) => dateFormatter(value),
  };
};

/* 照会ステータス */
export const referenceStatusCol: BicycleColFactory = ({ labels, fit, rows }) => {
  const bicycles = rows as Bicycles;
  const groups = Map.groupBy(bicycles, (b) => b.referenceStatus);
  const valueOptions = Array.from(groups.entries()).map(([status, bicycles]) => {
    const text = labels.bicycleReferenceStatus[status];
    return {
      label: `${text} (${bicycles.length})`,
      value: text,
    };
  });

  return {
    field: 'referenceStatus',
    type: 'singleSelect',
    headerName: labels.p.referenceStatus,
    width: fit(labels.p.referenceStatus),
    valueGetter: (_, row) => labels.bicycleReferenceStatus[row.referenceStatus],
    valueOptions,
    filterOperators: [
      {
        label: '...のいずれか',
        value: isAnyOfOperator,
        defaultValue: [],
        getApplyFilterFn: () => null,
      },
    ],
  };
};

/* 所有者ステータス */
export const ownerStatusCol: BicycleColFactory = ({ labels, fit, rows }) => {
  const bicycles = rows as Bicycles;
  const valueOptions = bicycleOwnerStatuses.map((status) => {
    const count = bicycles.filter((b) => b.ownerStatus === status).length;
    const text = labels.ownerStatus[status];
    return {
      label: `${text} (${count})`,
      value: text,
    };
  });

  return {
    field: 'ownerStatus',
    type: 'singleSelect',
    headerName: labels.p.ownerStatus,
    width: fit(labels.p.ownerStatus),
    valueGetter: (_, row) => labels.ownerStatus[row.ownerStatus],
    valueOptions,
    filterOperators: [
      {
        label: '...のいずれか',
        value: isAnyOfOperator,
        defaultValue: [],
        getApplyFilterFn: () => null,
      },
    ],
  };
};

/* 処分ステータス */
export const releaseStatusCol: BicycleColFactory = ({ labels, fit, rows }) => {
  const bicycles = rows as Bicycles;
  const groups = Map.groupBy(bicycles, (b) => b.releaseContractStatus);
  const valueOptions = Array.from(groups.entries()).map(([status, bicycles]) => {
    const text = labels.releaseContractStatus[status];
    return {
      label: `${text} (${bicycles.length})`,
      value: text,
    };
  });

  return {
    field: 'releaseContract',
    type: 'singleSelect',
    headerName: labels.p.releaseContract,
    width: fit(labels.p.releaseContract),
    valueGetter: (_, row) => labels.releaseContractStatus[row.releaseContractStatus],
    valueOptions,
    filterOperators: [
      {
        label: '...のいずれか',
        value: isAnyOfOperator,
        defaultValue: [],
        getApplyFilterFn: () => null,
      },
    ],
  };
};

/* 処分日付 */
export const releaseAtCol: BicycleColFactory = ({ labels, fit }) => ({
  headerName: labels.p.releaseDate,
  field: 'releaseContract.date',
  type: 'dateTime',
  width: fit(datetimeFormat),
  valueGetter: (_, row) => {
    const releaseType = ['sell', 'dispose', 'transfer'];
    const event = row.events.find((e) => releaseType.includes(e.type));
    if (event === undefined) return undefined;
    return match(event.type)
      .with('sell', () => event.sellContract?.date)
      .with('dispose', () => event.disposeContract?.date)
      .with('transfer', () => event.transferContract?.date)
      .otherwise(() => undefined);
  },
  valueFormatter: (value: Date | undefined) => datetimeFormatter(value),
});

/* 放置された場所住所 */
export const abandonedAddressCol: BicycleColFactory = () => ({
  field: 'location.last.prefecture',
  headerName: '放置された場所',
  width: 300,
  valueGetter: (_, row) => toAddress(row.location?.last),
});

/* 所有者名 */
export const ownerNameCol: BicycleColFactory = ({ labels }) => ({
  field: 'owner.last.name',
  headerName: labels.p.ownerName,
  width: 200,
  valueGetter: (_, row) => row.owner?.last?.name,
});

/* 所有者郵便番号 */
export const ownerPostalCodeCol: BicycleColFactory = ({ labels, fit }) => ({
  field: 'owner.last.postalCode',
  headerName: labels.p.ownerPostalCode,
  width: fit(labels.p.ownerPostalCode),
  valueGetter: (_, row) => row.owner?.last?.postalCode,
});

/* 所有者住所 */
export const ownerAddressCol: BicycleColFactory = ({ labels }) => ({
  field: 'owner.last.address',
  headerName: labels.p.ownerAddress,
  width: 300,
  valueGetter: (_, row) => row.owner?.last?.address,
});

/* ランドマーク */
export const landmarkCol: BicycleColFactory = ({ labels, fit, rows }) => {
  const bicycles = rows as Bicycles;
  const empty = 'empty';
  const groups = Map.groupBy(bicycles, (b) => b.location?.last?.landmark?.name ?? empty);
  const valueOptions = Array.from(groups.entries()).map(([name, group]) => {
    const text = name !== empty ? name : 'データ無し';
    return {
      label: `${text} (${group.length})`,
      value: text,
    };
  });

  return {
    field: 'location.last.landmark.name',
    type: 'singleSelect',
    headerName: labels.domain.landmark,
    width: fit('●●●●＠●●●'),
    valueGetter: (_, row) => row.location?.last?.landmark?.name,
    valueOptions,
    filterOperators: [
      {
        label: '...のいずれか',
        value: isAnyOfOperator,
        defaultValue: [],
        getApplyFilterFn: () => null,
      },
    ],
  };
};

/* 車両区分 */
export const bicycleTypeCol: BicycleColFactory = ({ labels, fit, rows }) => {
  const bicycles = rows as Bicycles;
  const valueOptions = BicycleTypeSchema.options.map((t) => {
    const count = bicycles.filter((b) => b.type === t).length;
    const text = labels.bicycleType[t];
    return {
      label: `${text} (${count})`,
      value: text,
    };
  });
  return {
    field: 'body.last.type',
    type: 'singleSelect',
    headerName: labels.body.type,
    width: fit(labels.body.type),
    valueGetter: (_, row) => {
      if (isNullish(row.type)) return '';
      return labels.bicycleType[row.type];
    },
    valueOptions,
    filterOperators: [
      {
        label: '...のいずれか',
        value: isAnyOfOperator,
        defaultValue: [],
        getApplyFilterFn: () => null,
      },
    ],
  };
};

/* 防犯登録番号 */
export const registrationNumberCol: BicycleColFactory = ({ labels, fit }) => ({
  field: 'body.last.registrationNumber',
  headerName: labels.body.registrationNumber,
  width: Math.max(fit(labels.body.registrationNumber), fit('●● xx-xxx-x-xxxxx')),
  valueGetter: (_, row) => row.body?.last?.registrationNumber,
});

/* 車体番号 */
export const bodySerialNumberCol: BicycleColFactory = ({ labels, fit }) => ({
  field: 'body.last.serialNumber',
  headerName: labels.body.serialNumber,
  width: Math.max(fit(labels.body.serialNumber), fit('xx-xxx-x-xxxxx')),
  valueGetter: (_, row) => row.body?.last?.serialNumber,
});

/* ナンバープレートのナンバー */
export const numberPlateCol: BicycleColFactory = ({ labels, fit }) => ({
  field: 'body.last.numberPlate',
  headerName: labels.body.numberPlate,
  width: fit(labels.body.numberPlate),
  valueGetter: (_, row) => row.body?.last?.numberPlate,
});

/* 放置禁止区域 */
export const isNoParkingAreaCol: BicycleColFactory = ({ labels, fit }) => ({
  field: 'location.last.isNoParkingArea',
  headerName: labels.domain.noParkingArea,
  width: fit(labels.domain.noParkingArea),
  type: 'boolean',
  valueGetter: (_, row) => row.location?.last?.isNoParkingArea,
});

/* かごの有無 */
export const hasBasketCol: BicycleColFactory = ({ labels, fit }) => ({
  field: 'body.last.hasBasket',
  headerName: labels.p.hasBasket,
  width: fit(labels.p.hasBasket),
  type: 'boolean',
  valueGetter: (_, row) => row.body?.last?.hasBasket,
});

/* 施錠の有無 */
export const isLockedCol: BicycleColFactory = ({ labels }) => ({
  headerName: labels.p.isLocked,
  field: 'body.last.isLocked',
  type: 'boolean',
  valueGetter: (_, row) => row.body?.last?.isLocked,
});

/* 保管料受領済み */
export const isCollectedStorageFeeCol: BicycleColFactory = ({ labels }) => ({
  headerName: labels.p.isCollectedStorageFee,
  field: 'isCollectedStorageFee',
  type: 'boolean',
  valueGetter: (_, row) => row.isCollectedStorageFee,
});

/* 色 */
export const colorCol: BicycleColFactory = ({ labels, colors, rows }) => {
  const bicycles = rows as Bicycles;
  const valueOptions = colors.map((c) => {
    const count = bicycles.filter((b) => {
      const lastColors = b.body?.last?.colors;
      return Array.isArray(lastColors) && lastColors.some((color) => color.colorId === c.id);
    }).length;
    return {
      label: `${c.name} (${count})`,
      value: c.name,
    };
  });
  return {
    field: 'body.last.colors',
    type: 'singleSelect',
    headerName: labels.body.color,
    width: 300,
    valueGetter: (_, row) => {
      const lastColors = row.body?.last?.colors ?? [];
      return lastColors.map((c) => c.color.name);
    },
    valueOptions,
    filterOperators: [
      {
        label: '...のいずれか',
        value: isAnyOfOperator,
        defaultValue: [],
        getApplyFilterFn: () => null,
      },
    ],
    renderCell: (params) => {
      const colors = params.row.body?.last?.colors;
      if (isNullish(colors)) return;
      return (
        <>
          {colors.map(({ color }) => (
            <Chip
              key={color.id}
              label={
                <Stack direction="row" alignItems="center">
                  <ColorTile colorCode={color.code} sx={{ mr: 1 }} />
                  {color.name}
                </Stack>
              }
              sx={{ m: 0.25 }}
            />
          ))}
        </>
      );
    },
  };
};

/* 車両状態 */
export const conditionCol: BicycleColFactory = ({ labels, conditions, rows }) => {
  const bicycles = rows as Bicycles;
  const valueOptions = conditions.map((c) => {
    const count = bicycles.filter((b) => {
      const lastConditions = b.body?.last?.conditions;
      return (
        Array.isArray(lastConditions) && lastConditions.some((cond) => cond.conditionId === c.id)
      );
    }).length;
    return {
      label: `${c.name} (${count})`,
      value: c.name,
    };
  });
  return {
    field: 'body.last.conditions',
    type: 'singleSelect',
    headerName: labels.body.conditions,
    width: 200,
    valueGetter: (_, row) => {
      const conditions = row.body?.last?.conditions ?? [];
      return conditions.map((c) => c.condition.name);
    },
    valueOptions,
    filterOperators: [
      {
        label: '...のいずれか',
        value: isAnyOfOperator,
        defaultValue: [],
        getApplyFilterFn: () => null,
      },
    ],
    renderCell: (params) => {
      const conditions = params.row.body?.last?.conditions ?? [];
      if (isNullish(conditions)) return;
      return (
        <>
          {conditions.map(({ condition }) => (
            <Chip key={condition.name} label={condition.name} sx={{ m: 0.25 }} />
          ))}
        </>
      );
    },
  };
};

export const removedAtCol: BicycleColFactory = ({ labels, fit }) => ({
  headerName: labels.p.removedAt,
  field: 'removedEvent.date',
  type: 'dateTime',
  width: fit(datetimeFormat),
  valueGetter: (_, row) => {
    const event = row.events.find((e) => e.type === 'remove');
    if (event === undefined) return undefined;
    return new Date(event.date);
  },
  valueFormatter: (value: Date | undefined) => datetimeFormatter(value),
});

export const numberedAtCol: BicycleColFactory = () => ({
  field: 'numberingEvent.date',
  type: 'dateTime',
  headerName: '採番日時',
  width: 200,
  valueGetter: (_, bicycle) => {
    const event = bicycle.events.find((e) => e.type === 'numbering');
    if (event === undefined) return undefined;
    return new Date(event.date);
  },
  valueFormatter: (value: Date | undefined) => datetimeFormatter(value),
});

export const serialNoCol: BicycleColFactory = () => ({
  field: 'serialTag.last.serialNo',
  headerName: '整理番号',
  width: 200,
  valueGetter: (_, bicycle) => {
    return bicycle.serialTag?.last?.serialNo ?? '-';
  },
});

export const serialTagPrintedAtCol: BicycleColFactory = ({ fit }) => ({
  field: 'printedEvent.date',
  headerName: '最新印刷日時',
  width: fit(datetimeFormat),
  valueGetter: (_, bicycle) => {
    const event = bicycle.events.findLast((e) => e.type === 'printSerialTag');
    if (event === undefined) return undefined;
    return event.date;
  },
  valueFormatter: (value: Date | undefined) => datetimeFormatter(value),
});

/* 保管場名 */
export const storageNameCol: BicycleColFactory = ({ labels, storages, rows }) => {
  const bicycles = rows as Bicycles;
  const valueOptions = storages.map((storage) => {
    const count = bicycles.filter(
      (b) => b.storageLocation?.last?.storage?.id === storage.id,
    ).length;
    return {
      label: `${storage.name} (${count})`,
      value: storage.name,
    };
  });
  return {
    field: 'storageLocation.last.storage.name',
    type: 'singleSelect',
    headerName: labels.domain.storage,
    width: 200,
    valueGetter: (_, row) => {
      return row.storageLocation?.last?.storage?.name;
    },
    valueOptions,
    filterOperators: [
      {
        label: '...のいずれか',
        value: isAnyOfOperator,
        defaultValue: [],
        getApplyFilterFn: () => null,
      },
    ],
  };
};

export const useBaseBicycleColumns = createUseColumns([imagesCol, serialNoCol, statusCol]);
