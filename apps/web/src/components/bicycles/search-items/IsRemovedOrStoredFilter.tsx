import { useAppContext } from '@/contexts/app-context';
import { FormControlLabel, Switch } from '@mui/material';
import { useSwitchAnyOfFilter } from 'mui-ex';
import type { BicycleColField } from '../columns';

/**
 * '撤去' または '保管' のステータスを持つ自転車のみを表示するか、すべての自転車を表示するかを切り替える
 * スイッチがONのとき: '撤去' または '保管' のステータスを持つ自転車のみを表示する
 * スイッチがOFFのとき: フィルターを完全に削除する
 */
export const IsRemovedOrStoredFilter = () => {
  const { labels } = useAppContext();
  const { checked, setChecked } = useSwitchAnyOfFilter<BicycleColField>('status', [
    labels.et.remove,
    labels.et.store,
  ]);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked(event.target.checked);
  };

  return (
    <FormControlLabel
      control={<Switch checked={checked} onChange={handleChange} />}
      label={labels.searchField.isRemovedOrStored}
    />
  );
};
