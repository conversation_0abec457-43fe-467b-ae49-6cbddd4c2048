import { useAppContext } from '@/contexts/app-context';
import { TextField } from '@mui/material';

import { useStringFilter } from 'mui-ex';

export const SerialNoFilter = () => {
  const { labels } = useAppContext();
  const { value, setValue } = useStringFilter('serialNo', 'contains');
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => setValue(e.target.value);
  return (
    <TextField
      label={labels.searchField.serialNo}
      value={value}
      onChange={handleChange}
      fullWidth
    />
  );
};
