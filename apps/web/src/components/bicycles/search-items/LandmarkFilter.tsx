import type React from 'react';

import { Autocomplete, TextField } from '@mui/material';

import { useAppContext } from '@/contexts/app-context';
import type { Option } from 'common';
import { useSelectFilter } from 'mui-ex';
import type { BicycleColField } from '../columns';

export const LandmarkFilter = () => {
  const { labels } = useAppContext();
  const { options, value, setValue } = useSelectFilter<BicycleColField>(
    'location.last.landmark.name',
  );
  const handleChange = (_: React.SyntheticEvent<Element, Event>, next: (Option | string)[]) => {
    setValue(next.map((v) => (typeof v === 'string' ? v : v.value)));
  };

  return (
    <Autocomplete
      multiple
      options={options}
      getOptionLabel={(option) => option.label}
      getOptionDisabled={(option) => value.some((v) => v.value === option.value)}
      value={value}
      onChange={handleChange}
      renderInput={(params) => <TextField {...params} label={labels.domain.landmark} />}
      slotProps={{ chip: { size: 'small' } }}
    />
  );
};
