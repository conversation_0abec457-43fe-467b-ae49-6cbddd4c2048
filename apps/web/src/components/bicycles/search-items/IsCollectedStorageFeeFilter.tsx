import { useAppContext } from '@/contexts/app-context';
import { FormControlLabel, Switch } from '@mui/material';
import { useBooleanFilter } from 'mui-ex';
import type { BicycleColField } from '../columns';

/**
 * 保管料支払いの有無でフィルタリングする
 * スイッチがONのとき: 保管料支払いがある自転車のみを表示する
 * スイッチがOFFのとき: フィルターを完全に削除する
 */
export const IsCollectedStorageFeeFilter = () => {
  const { labels } = useAppContext();
  const { value, setValue } = useBooleanFilter<BicycleColField>('isCollectedStorageFee');
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValue(event.target.checked ? 'true' : undefined);
  };

  return (
    <FormControlLabel
      control={<Switch checked={value === 'true'} onChange={handleChange} />}
      label={labels.searchField.isCollectedStorageFee}
    />
  );
};
