import { useAppContext } from '@/contexts/app-context';
import { TextField } from '@mui/material';

import { useStringFilter } from 'mui-ex';

export const NumberPlateFilter = () => {
  const { labels } = useAppContext();
  const { value, setValue } = useStringFilter('dltb.numberPlate', 'contains');
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => setValue(e.target.value);
  return (
    <TextField
      label={labels.searchField.numberPlate}
      value={value}
      onChange={handleChange}
      fullWidth
    />
  );
};
