import { useAppContext } from '@/contexts/app-context';
import { FormControlLabel, MenuItem, Switch, TextField } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import type { SearchField, SearchItemType } from 'common';
import { match } from 'ts-pattern';
import { AddressFilter } from './AddressFilter';
import { AnnouncementStatusFilter } from './AnnouncementStatusFilter';
import { BicycleTypeFilter } from './BicycleTypeFilter';
import { ColorFilter } from './ColorFilter';
import { ConditionsFilter } from './ConditionsFilter';
import { HasBasketFilter } from './HasBasketFilter';
import { IsCollectedStorageFeeFilter } from './IsCollectedStorageFeeFilter';
import { IsLockedFilter } from './IsLockedFilter';
import { IsRemovedOrStoredFilter } from './IsRemovedOrStoredFilter';
import { LandmarkFilter } from './LandmarkFilter';
import { NoParkingAreaFilter } from './NoParkingAreaFilter';
import { NumberPlateFilter } from './NumberPlateFilter';
import { OwnerAddressFilter } from './OwnerAddressFilter';
import { OwnerNameFilter } from './OwnerNameFilter';
import { OwnerPostalCodeFilter } from './OwnerPostalCodeFilter';
import { OwnerStatusFilter } from './OwnerStatusFilter';
import { ReferenceStatusFilter } from './ReferenceStatusFilter';
import { RegistrationNumberFilter } from './RegistrationNumberFilter';
import { ReleaseStatusFilter } from './ReleaseStatusFilter';
import { SerialNoFilter } from './SerialNoFilter';
import { SerialNumberFilter } from './SerialNumberFilter';
import { StorageFilter } from './StorageFilter';

type Props = {
  field: SearchField;
  type: SearchItemType;
};

export const SearchItemForm = ({ field, type }: Props) => {
  const { labels } = useAppContext();

  if (field === 'colors') return <ColorFilter />;
  if (field === 'registrationNumber') return <RegistrationNumberFilter />;
  if (field === 'serialNumber') return <SerialNumberFilter />;
  if (field === 'numberPlate') return <NumberPlateFilter />;
  if (field === 'bicycleType') return <BicycleTypeFilter />;
  if (field === 'hasBasket') return <HasBasketFilter />;
  if (field === 'isLocked') return <IsLockedFilter />;
  if (field === 'conditions') return <ConditionsFilter />;
  if (field === 'landmark') return <LandmarkFilter />;
  if (field === 'isNoParkingArea') return <NoParkingAreaFilter />;
  if (field === 'address') return <AddressFilter />;
  if (field === 'ownerName') return <OwnerNameFilter />;
  if (field === 'ownerPostalCode') return <OwnerPostalCodeFilter />;
  if (field === 'ownerAddress') return <OwnerAddressFilter />;
  if (field === 'isRemovedOrStored') return <IsRemovedOrStoredFilter />;
  if (field === 'isCollectedStorageFee') return <IsCollectedStorageFeeFilter />;
  if (field === 'referenceStatus') return <ReferenceStatusFilter />;
  if (field === 'announceStatus') return <AnnouncementStatusFilter />;
  if (field === 'storage') return <StorageFilter />;
  if (field === 'ownerStatus') return <OwnerStatusFilter />;
  if (field === 'releaseStatus') return <ReleaseStatusFilter />;
  if (field === 'serialNo') return <SerialNoFilter />;

  const label = labels.searchField[field];
  const select = (
    <TextField label={label} select value="" fullWidth>
      <MenuItem value="">未選択</MenuItem>
    </TextField>
  );
  const datePicker = <DatePicker label={label} sx={{ width: 1 }} />;
  return match(type)
    .with('text', () => <TextField label={label} fullWidth />)
    .with('switch', () => <FormControlLabel control={<Switch checked />} label={label} />)
    .with('selectSingle', () => select)
    .with('selectMultiple', () => select)
    .with('date', () => datePicker)
    .with('dateAfter', () => datePicker)
    .with('dateBefore', () => datePicker)
    .exhaustive();
};
