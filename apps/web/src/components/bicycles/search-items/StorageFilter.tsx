import type React from 'react';

import { Autocomplete, TextField } from '@mui/material';

import type { Option } from 'common';

import { useSelectFilter } from 'mui-ex';

import { useAppContext } from '@/contexts/app-context';
import type { BicycleColField } from '../columns';

export const StorageFilter = () => {
  const { labels } = useAppContext();

  const { options, value, setValue } = useSelectFilter<BicycleColField>(
    'storageLocation.last.storage.name',
  );

  const handleChange = (_: React.SyntheticEvent<Element, Event>, next: (Option | string)[]) => {
    setValue(next.map((v) => (typeof v === 'string' ? v : v.value)));
  };

  return (
    <Autocomplete
      multiple
      options={options}
      getOptionLabel={(option) => option.label}
      getOptionDisabled={(option) => value.some((v) => v.value === option.value)}
      value={value}
      onChange={handleChange}
      renderInput={(params) => <TextField {...params} label={labels.domain.storage} />}
      slotProps={{ chip: { size: 'small' } }}
    />
  );
};
