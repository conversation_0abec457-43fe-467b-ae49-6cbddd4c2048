import type React from 'react';

import { Autocomplete, Box, Chip, TextField } from '@mui/material';

import type { Option } from 'common';
import { useSelectFilter } from 'mui-ex';

import { useAppContext } from '@/contexts/app-context';
import { ColorTile } from '../ColorTile';
import type { BicycleColField } from '../columns';

export const ColorFilter = () => {
  const { labels, colors } = useAppContext();

  /**
   * filter.optionsは以下の内容です。
   * ```ts
   * {
   *   label: `${c.name} (${count})`,
   *   value: c.name,
   * }
   * ```
   */
  const filter = useSelectFilter<BicycleColField>('body.last.colors');
  const handleChange = (_: React.SyntheticEvent<Element, Event>, next: (Option | string)[]) => {
    filter.setValue(next.map((v) => (typeof v === 'string' ? v : v.value)));
  };

  return (
    <Autocomplete
      multiple
      options={filter.options}
      getOptionLabel={(option) => option.label}
      getOptionDisabled={(option) => filter.value.some((v) => v.value === option.value)}
      value={filter.value}
      onChange={handleChange}
      renderInput={(params) => <TextField {...params} label={labels.body.color} />}
      renderValue={(value, getItemProps) => {
        return value.map((option, index) => {
          const { key, ...itemProps } = getItemProps({ index });
          const color = colors.find((c) => c.name === option.value);
          if (!color) throw new Error();
          const label = (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ColorTile colorCode={color.code} sx={{ mr: 1 }} />
              {option.label}
            </Box>
          );
          return <Chip key={key} {...itemProps} label={label} size="small" />;
        });
      }}
      renderOption={(props, option) => {
        const { key, ...optionProps } = props;
        const color = colors.find((c) => c.name === option.value);
        if (!color) throw new Error();
        return (
          <Box
            key={key}
            component="li"
            sx={{ display: 'flex', alignItems: 'center' }}
            {...optionProps}
          >
            <ColorTile colorCode={color.code} sx={{ mr: 1, mb: 0.5 }} />
            {option.label}
          </Box>
        );
      }}
    />
  );
};
