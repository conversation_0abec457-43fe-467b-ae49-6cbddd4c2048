import { useAppContext } from '@/contexts/app-context';
import { TextField } from '@mui/material';

import { useStringFilter } from 'mui-ex';

export const AddressFilter = () => {
  const { labels } = useAppContext();
  const { value, setValue } = useStringFilter('location.last.address', 'contains');
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => setValue(e.target.value);
  return (
    <TextField label={labels.searchField.address} value={value} onChange={handleChange} fullWidth />
  );
};
