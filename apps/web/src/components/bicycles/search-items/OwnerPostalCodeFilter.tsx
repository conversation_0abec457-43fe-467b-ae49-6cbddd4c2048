import { useAppContext } from '@/contexts/app-context';
import { TextField } from '@mui/material';

import { useStringFilter } from 'mui-ex';

export const OwnerPostalCodeFilter = () => {
  const { labels } = useAppContext();
  const { value, setValue } = useStringFilter('owner.last.postalCode', 'contains');
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => setValue(e.target.value);
  return (
    <TextField
      label={labels.searchField.ownerPostalCode}
      value={value}
      onChange={handleChange}
      fullWidth
    />
  );
};
