import { Autocomplete, TextField } from '@mui/material';

import { trpc } from '@/api';

import { useAppContext } from '@/contexts/app-context';
import { isNullish, objectEntries } from 'common';
import type { Bicycles } from 'lambda-api';
import { type BooleanItemValue, useBooleanFilter } from 'mui-ex';
import type { BicycleColField } from '../columns';

type Option = { label: string; value: string };

const useOptions = (bicycles: Bicycles): Option[] => {
  const { labels } = useAppContext();
  return objectEntries({
    true: {
      text: labels.p.isNoParkingAreaTrue,
      count: bicycles.filter((b) => b.location?.last?.isNoParkingArea === true).length,
    },
    false: {
      text: labels.p.isNoParkingAreaFalse,
      count: bicycles.filter((b) => b.location?.last?.isNoParkingArea === false).length,
    },
    nullish: {
      text: labels.p.isNoParkingAreaNull,
      count: bicycles.filter((b) => isNullish(b.location?.last?.isNoParkingArea)).length,
    },
  } satisfies Record<BooleanItemValue, { text: string; count: number }>).map(
    ([value, { text, count }]) => ({ label: `${text} (${count})`, value }),
  );
};

export const NoParkingAreaFilter = () => {
  const { labels } = useAppContext();
  const { data: bicycles = [] } = trpc.bicycles.list.useQuery();
  const options = useOptions(bicycles);

  const { value, setValue } = useBooleanFilter<BicycleColField>('location.last.isNoParkingArea');

  const handleChange = (_: React.SyntheticEvent<Element, Event>, next: Option | null) => {
    setValue(next?.value);
  };

  const selected = options.find((o) => o.value === value) ?? null;

  return (
    <Autocomplete
      options={options}
      getOptionLabel={(option) => option.label}
      value={selected}
      onChange={handleChange}
      isOptionEqualToValue={(option, value) => option.value === value?.value}
      renderInput={(params) => (
        <TextField {...params} label={labels.domain.noParkingArea} placeholder="選択" fullWidth />
      )}
    />
  );
};
