import { useAppContext } from '@/contexts/app-context';
import { TextField } from '@mui/material';

import { useStringFilter } from 'mui-ex';

export const SerialNumberFilter = () => {
  const { labels } = useAppContext();
  const { value, setValue } = useStringFilter('police.serialNumber', 'contains');
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => setValue(e.target.value);

  return (
    <TextField
      label={labels.searchField.serialNumber}
      value={value}
      onChange={handleChange}
      fullWidth
    />
  );
};
