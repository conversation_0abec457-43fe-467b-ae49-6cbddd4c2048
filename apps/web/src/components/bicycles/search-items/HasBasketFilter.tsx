import type React from 'react';

import { Autocomplete, TextField } from '@mui/material';

import { isNullish, objectEntries } from 'common';
import type { Bicycles } from 'lambda-api';
import { type BooleanItemValue, useBooleanFilter } from 'mui-ex';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import type { BicycleColField } from '../columns';

type Option = { label: string; value: string };

const useOptions = (bicycles: Bicycles): Option[] => {
  const { labels } = useAppContext();
  return objectEntries({
    true: {
      text: labels.p.hasBasketTrue,
      count: bicycles.filter((b) => b.body?.last?.hasBasket === true).length,
    },
    false: {
      text: labels.p.hasBasketFalse,
      count: bicycles.filter((b) => b.body?.last?.hasBasket === false).length,
    },
    nullish: {
      text: labels.p.hasBasketNull,
      count: bicycles.filter((b) => isNullish(b.body?.last?.hasBasket)).length,
    },
  } satisfies Record<BooleanItemValue, { text: string; count: number }>).map(
    ([value, { text, count }]) => ({ label: `${text} (${count})`, value }),
  );
};

export const HasBasketFilter = () => {
  const { labels } = useAppContext();
  const { data: bicycles = [] } = trpc.bicycles.list.useQuery();
  const options = useOptions(bicycles);

  const { value, setValue } = useBooleanFilter<BicycleColField>('body.last.hasBasket');

  const handleChange = (_: React.SyntheticEvent<Element, Event>, next: Option | null) =>
    setValue(next?.value);

  const selected = options.find((option) => option.value === value) ?? null;

  return (
    <Autocomplete
      options={options}
      getOptionLabel={(option) => option.label}
      value={selected}
      onChange={handleChange}
      isOptionEqualToValue={(option, value) => option.value === value?.value}
      renderInput={(params) => <TextField {...params} label={labels.p.hasBasket} />}
    />
  );
};
