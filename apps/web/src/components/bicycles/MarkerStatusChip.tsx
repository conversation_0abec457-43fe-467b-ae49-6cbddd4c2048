import { Chip, type ChipProps } from '@mui/material';

import { useAppContext } from '@/contexts/app-context';
import { markerStatusToColor, markerStatusToIcon } from '@/models/marker';
import type { BicycleMarkerStatus } from 'common';

type Props = ChipProps & {
  status: BicycleMarkerStatus;
};

export const MarkerStatusChip = ({ status, ...props }: Props) => {
  const { labels } = useAppContext();
  const color = markerStatusToColor(status);
  const Icon = markerStatusToIcon(status);
  return (
    <Chip
      {...props}
      label={labels.markerStatus[status]}
      icon={
        <Icon
          style={{ color: color['300'] }}
          sx={{ p: props.size === 'small' ? undefined : 0.4 }}
        />
      }
      sx={{ ...props.sx, backgroundColor: color['50'] }}
    />
  );
};
