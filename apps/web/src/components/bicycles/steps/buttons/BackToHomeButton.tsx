import { NavigateBefore } from '@mui/icons-material';
import { Button, type ButtonProps } from '@mui/material';
import { useNavigate } from '@tanstack/react-router';

import { renderButtonIcon } from '@/components/render-button-icon';
import { homeMeta } from '@/router/routes/home';

type Props = Pick<ButtonProps, 'variant' | 'fullWidth'>;

export const BackToHomeButton = ({ variant = 'outlined', fullWidth = true }: Props) => {
  const navigate = useNavigate();
  const backTo = homeMeta.useTitle();
  const backLabel = `${backTo}に戻る`;
  return (
    <Button
      variant={variant}
      fullWidth={fullWidth}
      onClick={() => navigate({ to: '/' })}
      startIcon={renderButtonIcon(NavigateBefore)}
    >
      {backLabel}
    </Button>
  );
};
