import { NavigateBefore } from '@mui/icons-material';
import { Button, type ButtonProps } from '@mui/material';

import { renderButtonIcon } from '@/components/render-button-icon';

type Props = Pick<ButtonProps, 'variant' | 'fullWidth' | 'onClick' | 'children'>;

export const PrevStepButton = ({
  variant = 'outlined',
  fullWidth = true,
  onClick,
  children = '戻る',
}: Props) => (
  <Button
    variant={variant}
    fullWidth={fullWidth}
    onClick={onClick}
    startIcon={renderButtonIcon(NavigateBefore)}
  >
    {children}
  </Button>
);
