import type React from 'react';

import { Box, Paper, type SxProps } from '@mui/material';

import { isNonNullish } from 'common';

type Props = {
  colorCode: string | null | undefined;
  size?: 'small' | 'medium';
  sx?: SxProps;
};

export const ColorTile: React.FC<Props> = ({ colorCode, sx, size }) => {
  const bgSize = 5;
  const grey = '#CCC';
  const paperSx: SxProps = isNonNullish(colorCode)
    ? { backgroundColor: colorCode }
    : {
        // 参考: https://qiita.com/7note/items/328761825801b82ef585
        backgroundImage:
          /* 市松模様になるようグラデーションで単色を入れる */
          `linear-gradient(45deg, ${grey} 25%, transparent 25%, transparent 75%, ${grey} 75%),
           linear-gradient(45deg, ${grey} 25%, transparent 25%, transparent 75%, ${grey} 75%)`,
        backgroundPosition: `0 0, ${bgSize}px ${bgSize}px` /* 「0 0」と1マス（1色）の大きさを指定 */,
        backgroundSize: `${bgSize * 2}px ${bgSize * 2}px` /* 1マス分の倍の大きさを指定 */,
        backgroundColor: '#FFF',
      };
  return (
    <Box
      sx={{
        height: (t) => t.typography.fontSize * (size === 'small' ? 0.8 : 1),
        aspectRatio: '1/1',
        ...sx,
      }}
    >
      {/* Paperの影のために下にずれて見えてしまう分、マイナスマージンで調整しています */}
      <Paper sx={{ width: '100%', height: '100%', mt: '-0.5px', ...paperSx }} />
    </Box>
  );
};
