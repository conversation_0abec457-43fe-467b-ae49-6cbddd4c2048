import { useAppContext } from '@/contexts/app-context';
import type { Bicycles } from 'lambda-api';
import { RouterButton } from '../RouterButton';

type Props = { bicycle: Bicycles[number] };
export const UsedQrcodeActions = ({ bicycle }: Props) => {
  const { labels } = useAppContext();
  return (
    <>
      {bicycle.nextEvents.includes('ensureAbandoned') && (
        <RouterButton to="/bicycles/ensure-abandoned" search={{ bicycleId: bicycle.id }}>
          {labels.ed.ensureAbandoned}
        </RouterButton>
      )}
      {bicycle.nextEvents.includes('remove') && (
        <RouterButton to="/bicycles/remove" search={{ bicycleId: bicycle.id }}>
          {labels.ed.remove}
        </RouterButton>
      )}
      {bicycle.nextEvents.includes('numbering') && (
        <RouterButton to="/serial-tags/numbering">{labels.ed.numbering}</RouterButton>
      )}
      {bicycle.nextEvents.includes('store') && (
        <RouterButton to="/bicycles/store" search={{ bicycleId: bicycle.id }}>
          {labels.ed.store}
        </RouterButton>
      )}
      <RouterButton variant="outlined" to="/bicycles/$id" params={{ id: bicycle.id }}>
        詳細ページを開く
      </RouterButton>
    </>
  );
};
