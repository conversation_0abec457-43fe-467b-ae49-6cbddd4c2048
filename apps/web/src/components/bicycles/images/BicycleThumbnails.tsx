import type { MarkOptional } from 'ts-essentials';

import { Thumbnails } from '@/components/images/Thumbnails';
import type { BicycleImage } from 'lambda-api';
import { BicycleEventTypeChip } from '../BicycleEventTypeChip';

type Image = MarkOptional<BicycleImage, 'event' | 'eventId'>;

type Props = React.ComponentProps<typeof Thumbnails<Image>> & {
  chip?: boolean;
};

export const BicycleThumbnails = ({ chip, ...props }: Props) => {
  const createChip = chip
    ? (image: Image | undefined) => image?.event && <BicycleEventTypeChip type={image.event.type} />
    : undefined;
  return <Thumbnails {...props} createChip={createChip} />;
};
