import { useAppContext } from '@/contexts/app-context';
import { RouterButton } from '../RouterButton';

type Props = { bicycleId: string };
export const UnusedQrcodeActions = ({ bicycleId }: Props) => {
  const { tenant, labels } = useAppContext();
  if (tenant.patrol.flow === 'find')
    return (
      <RouterButton variant="contained" to="/bicycles/find" search={{ bicycleId }}>
        {labels.ed.find}
      </RouterButton>
    );

  return (
    <RouterButton variant="contained" to="/bicycles/remove" search={{ bicycleId }}>
      {labels.ed.remove}
    </RouterButton>
  );
};
