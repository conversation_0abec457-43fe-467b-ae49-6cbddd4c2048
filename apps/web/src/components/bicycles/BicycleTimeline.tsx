import { useAppContext } from '@/contexts/app-context';
import { datetimeFormatter } from '@/funcs/date';
import { eventTypeToColor, eventTypeToIcon } from '@/models/bicycle';
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
  timelineItemClasses,
} from '@mui/lab';
import { Box, CardActionArea, ListItemText } from '@mui/material';
import { stringOrNull } from 'common';
import type { BicycleEvent } from 'lambda-api';
import { Multiline } from '../Multiline';

type Props = {
  events: BicycleEvent[];
  memo?: boolean;
  activeEventId?: string;
  onClick?: (eventId: string) => void;
};

export const BicycleTimeline = ({ events, activeEventId, onClick }: Props) => {
  const { labels } = useAppContext();

  return (
    <Timeline
      sx={{
        m: 0,
        p: 0,
        // 参考: https://mui.com/material-ui/react-timeline/#left-aligned-with-no-opposite-content
        [`& .${timelineItemClasses.missingOppositeContent}:before`]: { flex: 0, p: 0, pr: 2 },
      }}
    >
      {events.map((event, i) => {
        const color = eventTypeToColor(event.type);
        const Icon = eventTypeToIcon(event.type);
        const isActive = event.id === activeEventId;
        const memo = stringOrNull(event.memo);
        const timelineItem = (
          <TimelineItem key={event.id}>
            <TimelineSeparator>
              <TimelineDot sx={{ backgroundColor: color['300'] }}>
                <Icon />
              </TimelineDot>
              {i !== events.length - 1 && <TimelineConnector />}
            </TimelineSeparator>
            <TimelineContent>
              <ListItemText
                primary={labels.et[event.type]}
                primaryTypographyProps={{ color: isActive ? 'primary' : 'initial' }}
                secondary={datetimeFormatter(event.date)}
                secondaryTypographyProps={{ color: isActive ? 'primary' : 'initial' }}
              />
              {memo && (
                <Box sx={{ borderRadius: 2, bgcolor: (t) => t.palette.background.innerGrey, p: 1 }}>
                  <Multiline text={memo} />
                </Box>
              )}
            </TimelineContent>
          </TimelineItem>
        );
        if (onClick) {
          return (
            <CardActionArea
              key={event.id}
              onClick={() => onClick(event.id)}
              sx={{ borderRadius: 2 }}
            >
              {timelineItem}
            </CardActionArea>
          );
        }
        return timelineItem;
      })}
    </Timeline>
  );
};
