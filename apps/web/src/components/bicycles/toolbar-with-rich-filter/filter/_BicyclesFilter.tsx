import { ExpandMore, Search } from '@mui/icons-material';
import {
  Box,
  Button,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Stack,
  type Theme,
  Typography,
  useMediaQuery,
} from '@mui/material';
import React from 'react';
import { BicycleFilterChips } from './BicycleFilterChips';

import { Swiper, SwiperSlide } from 'swiper/react';
import type { Swiper as SwiperType } from 'swiper/types';

import { useAppContext } from '@/contexts/app-context';
import { usePersistentState } from 'mui-ex';
import { BicycleSearchSet } from './search-set/BicycleSearchSet';
import { BicycleSearchSetNavigation } from './search-set/BicycleSearchSetNavigation';

type Props = { persistent?: string };

export const BicyclesFilter = ({ persistent }: Props) => {
  const { searchSets } = useAppContext();
  const swiperRef = React.useRef<SwiperType | null>(null);
  const [open, setOpen] = usePersistentState(persistent ? `${persistent}-open` : undefined, true);
  const [index, setIndex] = usePersistentState(persistent ? `${persistent}-index` : undefined, 0);
  const handlePrev = () => swiperRef.current?.slidePrev();
  const handleNext = () => swiperRef.current?.slideNext();

  const lgDown = useMediaQuery<Theme>((theme) => theme.breakpoints.down('lg'));

  const navigation = (
    <BicycleSearchSetNavigation index={index} onPrev={handlePrev} onNext={handleNext} />
  );
  const swiper = (
    <Swiper
      spaceBetween={16}
      loop
      initialSlide={index}
      onSwiper={(swiper) => {
        swiperRef.current = swiper;
      }}
      onRealIndexChange={(swiper) => setIndex(swiper.realIndex % searchSets.length)}
      style={{ width: '100%' }}
    >
      {searchSets.map((searchSet) => (
        <SwiperSlide key={searchSet.id}>
          <BicycleSearchSet searchSet={searchSet} />
        </SwiperSlide>
      ))}
    </Swiper>
  );

  return (
    <Box>
      <Stack direction="row" alignItems="center" spacing={1} sx={{ p: 1 }}>
        <Stack direction="row" sx={{ flexGrow: 1, flexWrap: 'wrap', m: -0.5 }}>
          <BicycleFilterChips />
        </Stack>
        <IconButton onClick={() => setOpen((prev) => !prev)}>
          {!lgDown ? <ExpandMore /> : <Search />}
        </IconButton>
      </Stack>
      {!lgDown && (
        <Collapse in={open}>
          <Divider />
          <Stack sx={{ p: 1, pb: 0 }}>{navigation}</Stack>
          {swiper}
        </Collapse>
      )}
      {lgDown && (
        <Dialog
          open={open}
          onClose={() => setOpen(false)}
          maxWidth={false}
          fullWidth
          scroll="paper"
        >
          <DialogTitle sx={{ p: 1 }}>
            <Typography variant="body2" color="textSecondary">
              検索条件設定
            </Typography>
            {navigation}
          </DialogTitle>
          <DialogContent dividers sx={{ p: 0 }}>
            {swiper}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpen(false)}>閉じる</Button>
          </DialogActions>
        </Dialog>
      )}
    </Box>
  );
};
