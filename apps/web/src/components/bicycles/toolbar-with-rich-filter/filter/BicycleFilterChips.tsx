import { Chip } from '@mui/material';
import { useChips, useFilterModelContext } from 'mui-ex';

export const BicycleFilterChips = () => {
  const { rows } = useFilterModelContext();
  const chips = useChips();
  return (
    <>
      <Chip label={`すべてのデータ: ${rows.length}件`} sx={{ bgcolor: 'transparent', m: 0.25 }} />
      {chips.map(({ key, ...chip }) => (
        <Chip key={key} color="primary" variant="outlined" sx={{ m: 0.25 }} {...chip} />
      ))}
    </>
  );
};
