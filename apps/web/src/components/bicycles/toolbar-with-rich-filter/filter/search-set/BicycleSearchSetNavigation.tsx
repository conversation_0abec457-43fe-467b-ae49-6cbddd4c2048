import { useAppContext } from '@/contexts/app-context';
import { useTextToPx } from '@/hooks/useTextToPx';
import { KeyboardArrowLeft, KeyboardArrowRight } from '@mui/icons-material';
import { Box, IconButton, Stack } from '@mui/material';

type Props = {
  index: number;
  onPrev: () => void;
  onNext: () => void;
};

export const BicycleSearchSetNavigation = ({ index, onNext, onPrev }: Props) => {
  const toPx = useTextToPx();
  const { searchSets } = useAppContext();
  const searchSet = searchSets.at(index);
  if (!searchSet) return;
  const { name } = searchSet;
  const searchSetNamePx = Math.max(...searchSets.map((s) => toPx(s.name)));
  return (
    <Stack direction="row" alignItems="center" spacing={1}>
      <IconButton color="primary" size="small" onClick={onPrev}>
        <KeyboardArrowLeft />
      </IconButton>
      <Box
        sx={{
          width: { xs: searchSetNamePx * 1.5, lg: searchSetNamePx },
          flexGrow: { xs: 1, sm: 0 },
          textAlign: 'center',
        }}
      >
        {name}
      </Box>
      <IconButton color="primary" size="small" onClick={onNext}>
        <KeyboardArrowRight />
      </IconButton>
    </Stack>
  );
};
