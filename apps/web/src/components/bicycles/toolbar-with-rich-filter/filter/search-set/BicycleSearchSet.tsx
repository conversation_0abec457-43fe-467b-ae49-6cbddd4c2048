import { SearchItemForm } from '@/components/bicycles/search-items/_SearchItemForm';
import { Box, Grid } from '@mui/material';
import type { Tenant } from 'lambda-api';

type Props = {
  searchSet: Tenant['searchSets'][number];
};

export const BicycleSearchSet = ({ searchSet: { items } }: Props) => {
  return (
    <Box sx={{ p: 1 }}>
      <Box sx={{ p: 1, bgcolor: (t) => t.palette.background.innerBlue, borderRadius: 2 }}>
        <Grid container spacing={1}>
          {items.map((item) => (
            <Grid key={item.id} size={{ xs: 12, sm: 6, lg: 4, xl: 3 }}>
              <SearchItemForm {...item} />
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};
