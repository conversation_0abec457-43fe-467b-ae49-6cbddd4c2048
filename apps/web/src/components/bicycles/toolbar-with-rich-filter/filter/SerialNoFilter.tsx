import { TextField } from '@mui/material';
import type { GridFilterItem } from '@mui/x-data-grid';
import { useFilterModelContext } from 'mui-ex';

const field = 'serialNo';
const operator = 'contains';
const isSerialNo = (item: GridFilterItem) => {
  if (item.field !== field) return false;
  if (item.operator !== operator) return false;
  return true;
};

export const SerialNoFilter = () => {
  const {
    filterModel: { items },
    setFilterItems,
  } = useFilterModelContext();
  const handleChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const value = e.target.value;
    if (value === '') return setFilterItems(items.filter((item) => !isSerialNo(item)));

    const found = items.find((item) => isSerialNo(item));
    if (found === undefined) return setFilterItems([...items, { field, operator, value }]);

    return setFilterItems(items.map((item) => (isSerialNo(item) ? { ...item, value } : item)));
  };

  return (
    <TextField
      label="整理番号"
      value={items.find(isSerialNo)?.value ?? ''}
      onChange={handleChange}
    />
  );
};
