import { useState } from 'react';

import Export, { type ExportFormatter } from '@/libs/mui-x/grid/Export';
import ToolbarAddButton from '@/libs/mui-x/grid/ToolbarAddButton';
import ToolbarReloadButton from '@/libs/mui-x/grid/ToolbarReloadButton';
import type { To } from '@/router/core/types';
import { MoreVert } from '@mui/icons-material';
import { Box, Divider, IconButton, Menu, type Theme, useMediaQuery } from '@mui/material';
import { Toolbar } from '@mui/x-data-grid';
import type { QueryKey } from '@tanstack/react-query';
import { ToolbarColumnsButton, ToolbarDensitySelector } from 'mui-ex';
import { BicyclesFilter } from './filter/_BicyclesFilter';

type Props = {
  to?: To;
  queryKey: QueryKey;
  persistent?: string;
  exportFormatter?: ExportFormatter;
};

export const ToolbarWithRichFilter = ({ to, persistent, exportFormatter, queryKey }: Props) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const mdDown = useMediaQuery<Theme>((theme) => theme.breakpoints.down('md'));

  if (mdDown) {
    return (
      <>
        <Toolbar>
          {to && <ToolbarAddButton to={to} label="新規" />}
          <ToolbarReloadButton queryKey={queryKey} />
          <IconButton aria-label="more options" onClick={handleOpen} sx={{ ml: 'auto' }}>
            <MoreVert />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            transformOrigin={{ vertical: 'top', horizontal: 'right' }}
          >
            {exportFormatter && <Export formatter={exportFormatter} />}
            <ToolbarColumnsButton type="menu-item" />
            <ToolbarDensitySelector type="menu-item" />
          </Menu>
        </Toolbar>
        <BicyclesFilter persistent={persistent} />
        <Divider />
      </>
    );
  }
  return (
    <>
      <Toolbar>
        {to && <ToolbarAddButton to={to} />}
        <ToolbarReloadButton queryKey={queryKey} />
        <Box sx={{ flexGrow: 1 }} />
        {exportFormatter && <Export formatter={exportFormatter} />}
        <ToolbarColumnsButton type="button" />
        <ToolbarDensitySelector type="button" />
      </Toolbar>
      <BicyclesFilter persistent={persistent} />
      <Divider />
    </>
  );
};
