import { Chip, type ChipProps } from '@mui/material';

import type { BicycleEventType } from 'common';

import { useAppContext } from '@/contexts/app-context';
import { eventTypeToColor, eventTypeToIcon } from '@/models/bicycle';

type Props = ChipProps & {
  type: BicycleEventType;
  helperText?: string | number;
};

export const BicycleEventTypeChip = ({ type, helperText, ...props }: Props) => {
  const { labels } = useAppContext();
  const color = eventTypeToColor(type);
  const Icon = eventTypeToIcon(type);
  const label = `${labels.et[type]}${helperText !== undefined ? ` (${helperText})` : ''}`;
  return (
    <Chip
      {...props}
      label={label}
      icon={
        <Icon
          style={{ color: color['300'] }}
          sx={{ p: props.size === 'small' ? undefined : 0.4 }}
        />
      }
      sx={{ ...props.sx, bgcolor: color['50'] }}
    />
  );
};
