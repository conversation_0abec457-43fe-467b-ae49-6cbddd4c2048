import type { SvgIconComponent } from '@mui/icons-material';
import { ListItemIcon, ListItemText, MenuItem } from '@mui/material';
import Button from '@mui/material/Button';

type Props = {
  type: 'button' | 'menu-item';
  Icon: SvgIconComponent;
  label: string;
  disabled?: boolean;
  onClick?: () => void;
};

export default function ClickableItem({ type, Icon, label, disabled, onClick }: Props) {
  const handleClick = () => {
    if (onClick) onClick();
  };
  if (type === 'menu-item')
    return (
      <MenuItem onClick={handleClick} disabled={disabled}>
        <ListItemIcon>
          <Icon fontSize="small" />
        </ListItemIcon>
        <ListItemText>{label}</ListItemText>
      </MenuItem>
    );

  return (
    <Button startIcon={<Icon />} onClick={handleClick} disabled={disabled}>
      {label}
    </Button>
  );
}
