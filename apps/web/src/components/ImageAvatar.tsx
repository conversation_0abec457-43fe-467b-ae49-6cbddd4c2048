import { Avatar, type AvatarProps, Box } from '@mui/material';

import NO_IMAGE from '@/assets/img/no-image-placeholder.svg';
import type { GridColDef, GridValidRowModel } from '@mui/x-data-grid';
import { isNullish } from 'common';
import type { StrictOmit } from 'ts-essentials';

type Props = {
  alt: string;
  src?: string;
  variant?: AvatarProps['variant'];
  noImage?: boolean;
};

export const ImageAvatar = ({ src, alt, variant, noImage }: Props) => {
  if (src !== undefined) {
    if (noImage) return <Avatar variant={variant} alt={alt} src={src ?? NO_IMAGE} />;
    return;
  }
  return (
    <Avatar variant={variant} sx={{ bgcolor: (t) => t.palette.primary.main }}>
      {alt[0]}
    </Avatar>
  );
};

export const createImageColProps = <T extends GridValidRowModel>(
  getter: (row: T) => string | null | undefined,
) =>
  ({
    headerName: '',
    width: 60,
    filterable: false,
    sortable: false,
    renderCell: (params) => {
      const url = getter(params.row);
      if (isNullish(url)) return;
      return (
        <Box sx={{ height: 1, display: 'flex', alignItems: 'center' }}>
          <ImageAvatar alt="" src={url} variant="rounded" noImage />
        </Box>
      );
    },
  }) as const satisfies StrictOmit<GridColDef<T>, 'field'>;
