import type React from 'react';

import { Navigate } from '@tanstack/react-router';

import { useAuth } from '@/stores/auth';
import { LoadingSpinner } from './LoadingSpinner';

type Props = {
  children: React.ReactNode;
};

export const Authentication = ({ children }: Props) => {
  const auth = useAuth();
  if (auth === undefined) return <LoadingSpinner height="100dvh" message="認証中..." />;
  if (auth === false) return <Navigate to="/login" replace />;
  return children;
};
