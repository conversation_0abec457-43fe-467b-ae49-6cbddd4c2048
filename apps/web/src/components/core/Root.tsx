import { type FC, Suspense } from 'react';

import { Provider as Jo<PERSON><PERSON>rovider } from 'jotai';

import { CssBaseline } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { jaJP } from '@mui/x-date-pickers/locales';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Outlet } from '@tanstack/react-router';
import { ja } from 'date-fns/locale/ja';
import { DevTools as JotaiDevTools } from 'jotai-devtools';
import { SnackbarProvider } from 'notistack';
import { ErrorBoundary } from 'react-error-boundary';

import { queryClient, trpc, trpcReactClient } from '@/api';
import { ErrorPage } from '@/pages/error/index.page';
import { store } from '@/store';
import { LoadingSpinner } from './LoadingSpinner';
import { ThemeProvider } from './ThemeProvider';
import UserPoolProvider from './UserPoolProvider';

export const Root: FC = () => {
  return (
    <trpc.Provider client={trpcReactClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        <ReactQueryDevtools />
        <JotaiProvider store={store}>
          <JotaiDevTools store={store} />
          <ThemeProvider>
            <>
              <CssBaseline />
              <LocalizationProvider
                dateAdapter={AdapterDateFns}
                adapterLocale={ja}
                localeText={jaJP.components.MuiLocalizationProvider.defaultProps.localeText}
              >
                <SnackbarProvider anchorOrigin={{ vertical: 'top', horizontal: 'center' }} />
                <Suspense fallback={<LoadingSpinner />}>
                  <ErrorBoundary FallbackComponent={ErrorPage}>
                    <UserPoolProvider>
                      <Outlet />
                    </UserPoolProvider>
                  </ErrorBoundary>
                </Suspense>
              </LocalizationProvider>
            </>
          </ThemeProvider>
        </JotaiProvider>
      </QueryClientProvider>
    </trpc.Provider>
  );
};
