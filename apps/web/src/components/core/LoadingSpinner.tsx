import { CircularProgress, Typography } from '@mui/material';
import { Stack } from '@mui/system';

type Props = { height?: number | string; message?: string };

export const LoadingSpinner = ({ height, message }: Props) => (
  <Stack spacing={2} justifyContent="center" alignItems="center" height={height ?? '100%'}>
    <Typography variant="body2" color="text.secondary">
      {message}
    </Typography>
    <CircularProgress />
  </Stack>
);
