import React from 'react';

import { AppContext, useAppContextValue } from '@/contexts/app-context';

import { DevicesProvider } from '@/contexts/camera-context';
import { type Theme, ThemeProvider } from '@mui/material';
import { APIProvider } from '@vis.gl/react-google-maps';
import { LoadingSpinner } from './LoadingSpinner';

type Props = React.PropsWithChildren;

// https://mui.com/material-ui/customization/palette/#custom-colors
const calcColor = (t: Theme, color: string) => t.palette.augmentColor({ color: { main: color } });

export const AppProviders: React.FC<Props> = ({ children }) => {
  const appContext = useAppContextValue();
  const [loading, setLoading] = React.useState(true);
  const handleLoad = () => setLoading(false);

  if (appContext === undefined) return <LoadingSpinner height="100dvh" message="データ取得中..." />;
  const { map } = appContext;
  const { primaryColor, secondaryColor } = appContext.tenant;

  return (
    <AppContext.Provider value={appContext}>
      <ThemeProvider<Theme>
        theme={(t) => ({
          ...t,
          palette: {
            ...t.palette,
            primary: calcColor(t, primaryColor),
            secondary: calcColor(t, secondaryColor),
          },
        })}
      >
        <APIProvider apiKey={map.apiKey} onLoad={handleLoad}>
          {loading && <LoadingSpinner height="100dvh" message="ライブラリー読込中..." />}
          {!loading && <DevicesProvider>{children}</DevicesProvider>}
        </APIProvider>
      </ThemeProvider>
    </AppContext.Provider>
  );
};
