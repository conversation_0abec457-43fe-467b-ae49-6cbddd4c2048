import type React from 'react';

import { useAppContext } from '@/contexts/app-context';
import type { AdditionalFeature } from 'models';
type Props = {
  feature: AdditionalFeature;
  children: React.ReactNode;
};

const Authorization: React.FC<Props> = ({ feature, children }) => {
  const { user } = useAppContext();
  if (!user.role.features.includes(feature)) return;
  return children;
};

export default Authorization;
