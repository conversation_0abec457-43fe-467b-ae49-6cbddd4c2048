import { useAtom } from 'jotai';

import { <PERSON>u as <PERSON>uIcon } from '@mui/icons-material';
import {
  Box,
  IconButton,
  MenuItem,
  AppBar as MuiA<PERSON>Bar,
  TextField,
  Toolbar,
  inputBaseClasses,
  outlinedInputClasses,
  svgIconClasses,
} from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';

import { trpc } from '@/api';
import { useAppBarResizeObserver } from '@/stores/app-bar-rect';
import { sidebarAtom } from '@/stores/sidebar-open';

import { NotificationButton } from '../../../features/notifications/NotificationButton';

import { useAppContext } from '@/contexts/app-context';
import { BASE_URL } from '@/types/vite-env';
import AccountMenu from './AccountMenu';
import { GpsStrength } from './GpsStrength';
import { calcSx } from './constants';
import UISettings from './ui-settings/UISettingsButton';

// AppBar固定のプラクティス
// https://mui.com/material-ui/react-app-bar/#fixed-placement

export default function AppBar() {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const {
    user,
    tenant: { logoKey, roles },
  } = useAppContext();

  const { mutate } = trpc.roles.select.useMutation({
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: [['users', 'get'], { type: 'query' }] });
      navigate({ to: '/' });
    },
  });

  const ref = useAppBarResizeObserver();

  const [sidebarOpen, setSidebarOpen] = useAtom(sidebarAtom);
  const handleSidebarToggle = () => setSidebarOpen(!sidebarOpen);
  const handleSelect = (e: React.ChangeEvent<HTMLInputElement>) =>
    mutate({ roleId: e.target.value });

  return (
    <MuiAppBar ref={ref} position="fixed" sx={calcSx(sidebarOpen)}>
      <Toolbar>
        <IconButton
          edge="start"
          color="inherit"
          aria-label="open sidebar"
          onClick={handleSidebarToggle}
          sx={{
            mr: 1,
            ...(sidebarOpen && { display: 'none' }),
          }}
        >
          <MenuIcon />
        </IconButton>
        <Box
          component="img"
          src={(logoKey ? `${BASE_URL}/${logoKey}` : undefined) ?? '/logo.png'}
          alt="logo"
          sx={{
            height: (t) => `calc(${t.mixins.toolbar.minHeight ?? 48}px - ${t.spacing(1)})`,
            my: 0.5,
            borderRadius: 2,
            cursor: 'pointer',
            display: { xs: 'none', sm: 'block' },
            ...(sidebarOpen && { display: 'none' }),
          }}
          onClick={() => navigate({ to: '/' })}
        />
        <Box flexGrow={1} />
        {roles && (
          <TextField
            variant="standard"
            select
            value={user?.role.id || ''}
            onChange={handleSelect}
            sx={{
              width: 200,
              p: 1,
              [`& .${inputBaseClasses.root}`]: { color: '#fff' },
              [`& .${outlinedInputClasses.notchedOutline}`]: { borderColor: '#fff' },
              [`& .${svgIconClasses.root}`]: { color: '#fff' },
            }}
          >
            {roles.map((role) => (
              <MenuItem key={role.id} value={role.id}>
                {role.name}
              </MenuItem>
            ))}
          </TextField>
        )}
        <GpsStrength />
        <UISettings />
        <NotificationButton />
        <AccountMenu />
      </Toolbar>
    </MuiAppBar>
  );
}
