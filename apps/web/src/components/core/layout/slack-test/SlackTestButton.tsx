import { Announcement } from '@mui/icons-material';
import { ListItemIcon, ListItemText, MenuItem } from '@mui/material';
import { useSetSlackTest } from './store';

type Props = { onClick: () => void };

export const SlackTestButton = ({ onClick }: Props) => {
  const set = useSetSlackTest();
  return (
    <MenuItem
      onClick={() => {
        set(true);
        onClick();
      }}
    >
      <ListItemIcon>
        <Announcement />
      </ListItemIcon>
      <ListItemText primary="Slack通知テスト" />
    </MenuItem>
  );
};
