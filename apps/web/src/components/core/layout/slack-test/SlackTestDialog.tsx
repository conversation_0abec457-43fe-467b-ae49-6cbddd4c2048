import { trpc } from '@/api';
import { Loading<PERSON><PERSON>on } from '@mui/lab';
import { Button, Dialog, DialogActions, DialogContent, DialogTitle, Stack } from '@mui/material';
import type { Equal } from 'common';
import type { TRPC_ERROR_CODES } from 'lambda-api';
import { RhfSingleSelect, RhfStepNumber, RhfTextField } from 'mui-ex';
import { useForm } from 'react-hook-form';
import { useSetSlackTest, useSlackTest } from './store';

const codes = [
  'PARSE_ERROR',
  'BAD_REQUEST',
  'INTERNAL_SERVER_ERROR',
  'NOT_IMPLEMENTED',
  'BAD_GATEWAY',
  'SERVICE_UNAVAILABLE',
  'GATEWAY_TIMEOUT',
  'UNAUTHORIZED',
  'FORBIDDEN',
  'NOT_FOUND',
  'METHOD_NOT_SUPPORTED',
  'TIMEOUT',
  'CONFLICT',
  'PRECONDITION_FAILED',
  'PAYLOAD_TOO_LARGE',
  'UNSUPPORTED_MEDIA_TYPE',
  'UNPROCESSABLE_CONTENT',
  'TOO_MANY_REQUESTS',
  'CLIENT_CLOSED_REQUEST',
] as const;

export const errorCodesTest: Equal<(typeof codes)[number], (typeof TRPC_ERROR_CODES)[number]> =
  true;

type State = { message: string; code: (typeof TRPC_ERROR_CODES)[number] | 'none'; sec: number };

export const SlackTestDialog = () => {
  const open = useSlackTest();
  const setOpen = useSetSlackTest();
  const { control, handleSubmit, reset, formState } = useForm<State>({
    defaultValues: { message: '', code: 'none', sec: 0 } satisfies State,
  });
  const log = trpc.console.log.useMutation({ onSuccess: () => reset() });
  const err = trpc.console.err.useMutation({ onSuccess: () => reset() });
  const sleep = trpc.sleep.useMutation({ onSuccess: () => reset() });
  const submit = async (state: State) => {
    if (state.code !== 'none') return err.mutate({ code: state.code, message: state.message });
    if (state.sec > 0) return sleep.mutate({ sec: state.sec });
    return log.mutate({ message: state.message });
  };
  return (
    <Dialog
      open={open}
      onClose={() => setOpen(false)}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        component: 'form',
        onSubmit: handleSubmit(submit),
      }}
    >
      <DialogTitle>Slack通知テスト</DialogTitle>
      <DialogContent>
        <Stack spacing={1}>
          <RhfTextField
            control={control}
            name="message"
            label="文字列"
            placeholder="サーバーへ送信したい文字列を入力してください"
            multiline
            rows={4}
          />
          <RhfSingleSelect
            control={control}
            name="code"
            label="エラーコード"
            options={['none', ...codes].map((c) => ({
              label: c === 'none' ? 'エラーコードを送信しない' : c,
              value: c,
            }))}
          />
          <RhfStepNumber
            control={control}
            name="sec"
            label="タイムアウトテスト"
            min={0}
            max={60}
            helperText="0以外の値を設定するとサーバー側で指定の秒数待機します"
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button variant="text" onClick={() => setOpen(false)}>
          キャンセル
        </Button>
        <LoadingButton type="submit" loading={formState.isLoading}>
          送信
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
};
