import React from 'react';

import { ExpandLess, ExpandMore } from '@mui/icons-material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import {
  Badge,
  Box,
  Collapse,
  Divider,
  IconButton,
  List,
  type SxProps,
  type Theme,
  Toolbar,
  styled,
} from '@mui/material';
import MuiDrawer from '@mui/material/Drawer';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import { createLink, useMatchRoute, useNavigate } from '@tanstack/react-router';

import { useAppContext } from '@/contexts/app-context';
import { routeItems } from '@/router/core/meta';
import { type RouteGroupMeta, type RouteItemMeta, type To, useVisible } from '@/router/core/types';
import { useAppBarRect } from '@/stores/app-bar-rect';

import {
  useSidebar,
  useSidebarResizeCloser,
  useSidebarVariant,
} from '../../../stores/sidebar-open';

import { BASE_URL } from '@/types/vite-env';
import { SIDEBAR_WIDTH } from './constants';

const CustomDrawer = styled(MuiDrawer)(({ theme, open }) => ({
  '& .MuiDrawer-paper': {
    whiteSpace: 'nowrap', // 開閉の際に文字が折り返されないようにする
    width: SIDEBAR_WIDTH,
    transform: 'translate3d(0, 0, 0)',
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    ...(!open && {
      width: 0,
      transform: 'translate3d(-1px, 0, 0)',
      transition: theme.transitions.create('width', {
        easing: theme.transitions.easing.sharp,
        duration: theme.transitions.duration.leavingScreen,
      }),
    }),
  },
}));

const useIsMatchPath = () => {
  const matchRoute = useMatchRoute();
  return React.useCallback((to: To) => Boolean(matchRoute({ to })), [matchRoute]);
};

const useFilterRoute = (meta: RouteItemMeta | RouteGroupMeta): boolean => {
  if (meta.type === 'item') return useVisible(meta);
  return meta.children.some((item) => useVisible(item));
};

type RouterListItemProps = {
  item: RouteItemMeta;
  selected: boolean;
  useCount: (() => number) | undefined;
  onClick: () => void;
  sx?: SxProps<Theme>;
};

const RouterListItemButton = createLink(ListItemButton);
const NavigationItem = ({
  item: { path, Icon, useTitle },
  selected,
  onClick,
  useCount,
  sx,
}: RouterListItemProps) => {
  const title = useTitle();
  const count = useCount?.();
  const icon = count ? (
    <Badge badgeContent={count} color="primary">
      <Icon />
    </Badge>
  ) : (
    <Icon />
  );
  return (
    <RouterListItemButton key={path} to={path} selected={selected} onClick={onClick} sx={sx}>
      <ListItemIcon>{icon}</ListItemIcon>
      <ListItemText primary={title} />
    </RouterListItemButton>
  );
};

export const Sidebar = () => {
  const { tenant, labels } = useAppContext();
  const [sidebarOpen, setSidebarOpen] = useSidebar();
  const variant = useSidebarVariant();
  useSidebarResizeCloser();

  const handleDrawerToggle = () => setSidebarOpen(!sidebarOpen);
  const closeDrawerOnMobile = () => {
    if (variant === 'temporary') setSidebarOpen(false);
  };
  const appBarRect = useAppBarRect();
  const navigate = useNavigate();

  const isMatchPath = useIsMatchPath();
  const [subOpenItems, setSubOpenItems] = React.useState<string[]>([]);

  return (
    <CustomDrawer
      variant={variant}
      open={sidebarOpen}
      onClose={handleDrawerToggle}
      ModalProps={{ keepMounted: true }}
    >
      <Toolbar
        sx={{
          height: appBarRect.height - 1, // Dividerの分を引く
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          px: 1,
        }}
        disableGutters
      >
        <Box
          component="img"
          src={(tenant.logoKey ? `${BASE_URL}/${tenant.logoKey}` : undefined) ?? '/logo.png'}
          alt="logo"
          sx={{
            height: (t) => `calc(${t.mixins.toolbar.minHeight ?? 48}px - ${t.spacing(1)})`,
            my: 0.5,
            borderRadius: 2,
            cursor: 'pointer',
          }}
          onClick={() => navigate({ to: '/' })}
        />
        <IconButton onClick={handleDrawerToggle}>
          <ChevronLeftIcon />
        </IconButton>
      </Toolbar>
      <Divider />
      <List
        component="nav"
        sx={{ height: `calc(100dvh - ${appBarRect.height}px)`, overflow: 'auto' }}
      >
        {routeItems
          .filter((item) => useFilterRoute(item))
          .map((item) => {
            if (item.type === 'item')
              return (
                <NavigationItem
                  key={item.path}
                  item={item}
                  selected={isMatchPath(item.path)}
                  useCount={item.useCount}
                  onClick={closeDrawerOnMobile}
                />
              );
            const children = item.children.filter((c) => useFilterRoute(c));
            const open = subOpenItems.includes(item.key);
            const selected = !open && children.some((c) => isMatchPath(c.path));
            const handleIconClick: React.MouseEventHandler<HTMLButtonElement> = (e) => {
              e.stopPropagation();
              e.preventDefault();
              if (open) setSubOpenItems(subOpenItems.filter((group) => group !== item.key));
              else setSubOpenItems([...subOpenItems, item.key]);
            };
            return (
              <React.Fragment key={item.key}>
                <RouterListItemButton
                  to={item.menuPath}
                  selected={selected}
                  onClick={closeDrawerOnMobile}
                >
                  <ListItemIcon>
                    <item.Icon />
                  </ListItemIcon>
                  <ListItemText primary={labels.navigation[item.key]} />
                  <IconButton onClick={handleIconClick} sx={{ my: -1 }}>
                    {open ? <ExpandLess /> : <ExpandMore />}
                  </IconButton>
                </RouterListItemButton>
                <Collapse in={open} timeout="auto">
                  <List component="div" disablePadding>
                    {children.map((item) => (
                      <NavigationItem
                        key={item.path}
                        item={item}
                        selected={isMatchPath(item.path)}
                        useCount={item.useCount}
                        onClick={closeDrawerOnMobile}
                        sx={{ pl: 4 }}
                      />
                    ))}
                  </List>
                </Collapse>
              </React.Fragment>
            );
          })}
      </List>
    </CustomDrawer>
  );
};
