import React from 'react';

import { useAtom } from 'jotai';

import { Info } from '@mui/icons-material';
import {
  FormControl,
  FormControlLabel,
  FormLabel,
  IconButton,
  Popover,
  Radio,
  RadioGroup,
  Stack,
  Typography,
} from '@mui/material';
import { match } from 'ts-pattern';

import { mapTypeAtom, mapTypeSchema, mapTypes, useSetMapType } from '@/stores/map';

export default function MapTypeSwitch() {
  const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);
  const open = Boolean(anchorEl);

  const [mapType] = useAtom(mapTypeAtom);
  const setMapType = useSetMapType();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    setMapType(mapTypeSchema.parse(e.target.value));

  const handlePopoverOpen = (event: React.MouseEvent<HTMLElement>) =>
    setAnchorEl(event.currentTarget);

  const handlePopoverClose = () => setAnchorEl(null);

  const togglePopover = (e: React.MouseEvent<HTMLElement>) => {
    if (open) handlePopoverClose();
    else handlePopoverOpen(e);
  };

  return (
    <Stack spacing={1}>
      <FormControl>
        <FormLabel id="map-type-radio-buttons-group">
          <Stack direction="row" alignItems="center">
            <Typography sx={{ color: (theme) => theme.palette.text.grey }}>
              マップ切り替え
            </Typography>
            <IconButton
              aria-owns={open ? 'mouse-over-popover' : undefined}
              aria-haspopup="true"
              onClick={togglePopover}
              onMouseEnter={handlePopoverOpen}
              onMouseLeave={handlePopoverClose}
              size="small"
            >
              <Info fontSize="inherit" />
            </IconButton>
            <Popover
              id="mouse-over-popover"
              sx={{ pointerEvents: 'none' }}
              open={open}
              anchorEl={anchorEl}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
              }}
              onClose={handlePopoverClose}
              disableRestoreFocus
            >
              <Typography sx={{ p: 2, maxWidth: '400px' }}>
                Google Mapでストリートビューを表示している場合、
                国土地理院地図に切り替えるとストリートビューは終了します。
              </Typography>
            </Popover>
          </Stack>
        </FormLabel>
        <RadioGroup
          aria-labelledby="map-type-radio-buttons-group"
          name="controlled-radio-buttons-group"
          value={mapType}
          onChange={handleChange}
        >
          {mapTypes.map((type) => (
            <FormControlLabel
              key={type}
              value={type}
              control={<Radio />}
              label={match(type)
                .with('google', () => 'Google Map')
                .with('gsi', () => '国土地理院地図')
                .exhaustive()}
            />
          ))}
        </RadioGroup>
      </FormControl>
    </Stack>
  );
}
