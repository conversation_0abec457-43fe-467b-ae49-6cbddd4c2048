import React from 'react';

import { Settings } from '@mui/icons-material';
import { Box, IconButton, Paper, Popover, Stack } from '@mui/material';

import { FontSizeToggleButtons } from './FontSizeToggleButtons';
import MapTypeSwitch from './MapTypeSwitch';

const UISettings = () => {
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'ui-settings-popover' : undefined;

  return (
    <>
      <Box>
        <IconButton aria-describedby={id} color="inherit" onClick={handleClick}>
          <Settings />
        </IconButton>
      </Box>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Stack component={Paper} spacing={3} sx={{ p: 2 }}>
          <FontSizeToggleButtons />
          <MapTypeSwitch />
        </Stack>
      </Popover>
    </>
  );
};

export default UISettings;
