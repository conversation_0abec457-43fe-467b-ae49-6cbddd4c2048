import type React from 'react';

import { useAtom } from 'jotai';

import { Stack, ToggleButton, ToggleButtonGroup, Typography } from '@mui/material';

import { useAppContext } from '@/contexts/app-context';
import { fontSizeAtom, fontSizeSchema } from '@/stores/font-size';

const options = [
  { label: '小', value: 14 },
  { label: '中', value: 16 },
  { label: '大', value: 18 },
];

export const FontSizeToggleButtons: React.FC = () => {
  const { labels } = useAppContext();
  const [fontSize, setFontSize] = useAtom(fontSizeAtom);

  const handleChange = (value: number | null) => {
    if (value !== null) setFontSize(fontSizeSchema.parse(value));
  };
  return (
    <Stack spacing={1}>
      <Typography sx={{ color: (t) => t.palette.text.grey }}>{labels.system.fontSize}</Typography>
      <ToggleButtonGroup
        value={fontSize}
        exclusive
        onChange={(_, newFontSize) => handleChange(newFontSize)}
        size="small"
        sx={{ width: '100%' }}
      >
        {options.map(({ value, label }) => (
          <ToggleButton
            key={value}
            value={value}
            sx={{ flexGrow: 1, fontSize: 20, fontWeight: 'bold' }}
          >
            {label}
          </ToggleButton>
        ))}
      </ToggleButtonGroup>
    </Stack>
  );
};
