import React from 'react';

import {
  SignalC<PERSON>ular0Bar,
  SignalCellular1Bar,
  SignalCellular2Bar,
  SignalCellular3Bar,
  SignalCellular4Bar,
  SignalCellularConnectedNoInternet0Bar,
} from '@mui/icons-material';
import { Box, IconButton, Paper, Popover, Stack, Tooltip, Typography } from '@mui/material';

import { datetimeFormatter } from '@/funcs/date';
import { useGeoWatch, useGeolocationPosition } from '@/stores/position';

const calcSignalIcon = (isWatch: boolean, position: GeolocationPosition | undefined) => {
  if (!isWatch) return <SignalCellularConnectedNoInternet0Bar />;
  if (!position) return <SignalCellularConnectedNoInternet0Bar />;
  const {
    coords: { accuracy },
  } = position;
  if (accuracy < 10) return <SignalCellular4Bar />;
  if (accuracy < 20) return <SignalCellular3Bar />;
  if (accuracy < 30) return <SignalCellular2Bar />;
  if (accuracy < 40) return <SignalCellular1Bar />;
  return <SignalCellular0Bar />;
};

export const GpsStrength = () => {
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) =>
    setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);

  const isWatching = useGeoWatch();
  const position = useGeolocationPosition();
  const accuracy = position?.coords.accuracy;
  const hasAccuracy = accuracy !== undefined;
  const timestamp = position?.timestamp;

  const open = Boolean(anchorEl);
  const id = open ? 'gps-strength-popover' : undefined;

  return (
    <>
      <Box>
        <Tooltip title="位置情報の精度" placement="bottom">
          <IconButton aria-describedby={id} color="inherit" onClick={handleClick}>
            {calcSignalIcon(isWatching, position)}
          </IconButton>
        </Tooltip>
      </Box>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Stack component={Paper} spacing={1} sx={{ p: 2 }}>
          <Typography variant="h6">位置情報の精度</Typography>
          {(!isWatching || !hasAccuracy) && <Typography>位置情報が取得できません</Typography>}
          {isWatching && hasAccuracy && accuracy && (
            <Typography>
              誤差の可能性: {accuracy < 100 ? `${accuracy.toFixed(1)}m` : '100m以上'}
            </Typography>
          )}
          {isWatching && timestamp && (
            <Typography>最終更新: {datetimeFormatter(new Date(timestamp))}</Typography>
          )}
        </Stack>
      </Popover>
    </>
  );
};
