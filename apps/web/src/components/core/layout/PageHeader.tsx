import { ArrowBack } from '@mui/icons-material';
import {
  type Breakpoint,
  Container,
  IconButton,
  Paper,
  Stack,
  Typography,
  alpha,
  useTheme,
} from '@mui/material';
import { type LinkProps, useNavigate, useRouter } from '@tanstack/react-router';

import { useBodyScrollbarWidthObserver } from '@/hooks/useBodyScrollbarWidthObserver';
import { usePermanentSidebarWidth } from '@/stores/sidebar-open';

type Props = {
  back?: LinkProps;
  noBack: boolean;
  maxWidth: Breakpoint;
  title: string;
  firstRight?: React.JSX.Element;
  second?: React.JSX.Element;
};

export const PageHeader = ({ maxWidth, back, noBack, title, firstRight, second }: Props) => {
  const maxWidthPx = useTheme().breakpoints.values[maxWidth];
  const contentWidth = `(${maxWidthPx}px - 24px * 2)` as const;
  const scrollbarWidth = useBodyScrollbarWidthObserver();
  const sidebarWidth = usePermanentSidebarWidth();

  const marginBase =
    `(100vw - ${contentWidth} - ${sidebarWidth}px - ${scrollbarWidth}px) * 0.5` as const;
  /**
   * ## marginWithIcon の計算について
   * 使用している値の意味は、登場している順に次の通りです。
   * | 数値  | 説明                       |
   * | ----: | :------------------------- |
   * |  24px | Toolbarのpadding           |
   * |  40px | IconButtonの横幅           |
   * | -12px | edge="start"による折り返し |
   */
  const marginWithIcon = `${marginBase} - (24px + 40px - 12px)` as const;
  const navigate = useNavigate();
  const router = useRouter();
  const handleClickBack = () => {
    if (back) return navigate(back);
    return router.history.go(-1);
  };
  return (
    <Stack
      component={Paper}
      spacing={2}
      sx={{
        py: { xs: 1, sm: 2 },
        width: 1,
        bgcolor: (t) => alpha(t.palette.primary.main, 0.1),
        borderRadius: 0,
      }}
    >
      {/* first */}
      <Stack direction="row" sx={{ alignItems: 'center' }}>
        {!noBack && (
          <IconButton edge="start" onClick={handleClickBack} sx={{ ml: 1.5 }}>
            <ArrowBack />
          </IconButton>
        )}
        <Typography
          sx={{
            ml: (t) => ({
              xs: `max(${t.spacing(2)}, calc(${!noBack ? marginWithIcon : marginBase}))`,
              sm: `max(${t.spacing(!noBack ? 1 : 3)}, calc(${!noBack ? marginWithIcon : marginBase}))`,
            }),
          }}
          variant="h4"
        >
          {title}
        </Typography>
        <Stack
          direction="row"
          spacing={1}
          sx={{
            ml: 1,
            mr: (t) => ({
              xs: `max(${t.spacing(2)}, calc(${marginBase}))`,
              sm: `max(${t.spacing(3)}, calc(${marginBase}))`,
            }),
            alignItems: 'center',
            flexGrow: 1,
          }}
        >
          {firstRight}
        </Stack>
      </Stack>
      {second && (
        <Stack sx={{ width: 1, alignItems: 'center' }}>
          <Container maxWidth={maxWidth}>
            <Stack direction="row" spacing={2} sx={{ alignItems: 'end' }}>
              {second}
            </Stack>
          </Container>
        </Stack>
      )}
    </Stack>
  );
};
