import type { Breakpoint, SxProps, Theme } from '@mui/material';

export const SIDEBAR_WIDTH = 300;
const BREAKPOINT = 'lg' as const satisfies Breakpoint;

export const calcSx = (open: boolean): SxProps<Theme> => ({
  width: { xs: '100%', [BREAKPOINT]: !open ? '100%' : `calc(100% - ${SIDEBAR_WIDTH}px)` },
  marginLeft: { xs: 0, [BREAKPOINT]: !open ? 0 : `${SIDEBAR_WIDTH}px` },
  transition: (t) =>
    t.transitions.create(['width', 'margin'], {
      easing: t.transitions.easing.sharp,
      duration: open ? t.transitions.duration.enteringScreen : t.transitions.duration.leavingScreen,
    }),
});
