import { type Breakpoint, Container, Stack } from '@mui/material';
import type { LinkProps } from '@tanstack/react-router';
import type { CSSProperties } from 'react';
import { PageHeader } from './PageHeader';

type Props = {
  maxWidth?: Breakpoint;
  scrollable?: boolean;
  back?: LinkProps;
  noBack?: boolean;
  title: string;
  children: React.JSX.Element;
  firstRight?: React.JSX.Element;
  second?: React.JSX.Element;
};

export const MainLayout = (props: Props) => {
  const { maxWidth = 'md', noBack = false, scrollable = false, children } = props;
  const propsWithDefaults = { ...props, maxWidth, noBack };
  const overflow: CSSProperties['overflow'] = scrollable ? 'auto' : 'hidden';
  const containerHeight = scrollable ? undefined : 1;
  return (
    <Stack sx={{ width: 1, height: 1 }}>
      <PageHeader {...propsWithDefaults} />
      {/* overflowを設定しないと flexGrow だけでは高さを持たいない */}
      <Stack sx={{ width: 1, flexGrow: 1, overflow }}>
        <Container
          component={Stack}
          maxWidth={maxWidth}
          sx={{ height: containerHeight, py: { xs: 2, sm: 3 } }}
        >
          {children}
        </Container>
      </Stack>
    </Stack>
  );
};
