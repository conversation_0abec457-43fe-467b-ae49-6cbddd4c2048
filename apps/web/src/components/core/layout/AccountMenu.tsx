import * as React from 'react';

import { Divider, ListItem, ListItemIcon, ListItemText, MenuItem } from '@mui/material';
import Avatar from '@mui/material/Avatar';
import IconButton from '@mui/material/IconButton';
import Menu from '@mui/material/Menu';
import Tooltip from '@mui/material/Tooltip';

import { useAppContext } from '@/contexts/app-context';
import { LogoutButton } from '@/features/auth/logout/components';
import { AccountBox, Person } from '@mui/icons-material';
import { createLink } from '@tanstack/react-router';
import { memberEnvKeys } from 'common';
import { SlackTestButton } from './slack-test/SlackTestButton';
import { SlackTestDialog } from './slack-test/SlackTestDialog';

const RouterMenuItem = createLink(MenuItem);

const showDevMenu = import.meta.env.DEV || memberEnvKeys.includes(import.meta.env.VITE_ENV);

export default function AccountMenu() {
  const { user } = useAppContext();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);

  return (
    <>
      <Tooltip title="アカウント設定">
        <IconButton onClick={handleClick} color="inherit">
          {user.displayName ? (
            <Avatar
              sx={{
                width: 30,
                height: 30,
                bgcolor: (t) => t.palette.secondary.main,
                color: (t) => t.palette.secondary.contrastText,
              }}
            >
              {user.displayName?.slice(0, 1)}
            </Avatar>
          ) : (
            <Person />
          )}
        </IconButton>
      </Tooltip>
      <Menu
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <ListItem>
          <ListItemText sx={{ ml: 1 }}>{user.displayName}</ListItemText>
        </ListItem>
        <Divider />
        <RouterMenuItem to="/me" onClick={handleClose}>
          <ListItemIcon>
            <AccountBox />
          </ListItemIcon>
          <ListItemText>マイページ</ListItemText>
        </RouterMenuItem>
        <LogoutButton />
        {showDevMenu && <SlackTestButton onClick={handleClose} />}
      </Menu>
      <SlackTestDialog />
    </>
  );
}
