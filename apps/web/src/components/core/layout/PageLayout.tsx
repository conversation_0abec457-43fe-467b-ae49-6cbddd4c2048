import { Box, Toolbar } from '@mui/material';

import { useAppBarRect } from '@/stores/app-bar-rect';
import { useSidebar } from '@/stores/sidebar-open';

import AppBar from './AppBar';
import { Sidebar } from './Sidebar';
import { calcSx } from './constants';

type Props = { children: React.JSX.Element };
const PageLayout: React.FC<Props> = ({ children }) => {
  const [open] = useSidebar();
  const { height } = useAppBarRect();
  return (
    <Box sx={{ position: 'relative', display: 'flex' }}>
      <AppBar />
      <Sidebar />
      <Box sx={calcSx(open)}>
        <Toolbar sx={{ height }} />
        <Box
          component="main"
          sx={{
            height: `calc(100dvh - ${height}px)`,
            overflow: 'auto',
            scrollBehavior: 'smooth',
            bgcolor: '#f8f8f8',
          }}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default PageLayout;
