import type React from 'react';

import { AccessDeniedPage } from '@/pages/access-denied/index.page';

import { useAppContext } from '@/contexts/app-context';
import { homeMeta } from '@/router/routes/home';
import { useMatchRoute } from '@tanstack/react-router';

type Props = {
  children: React.ReactNode;
};

const AuthPage: React.FC<Props> = ({ children }) => {
  const { user } = useAppContext();
  const matchRoute = useMatchRoute();
  const ok =
    matchRoute({ to: homeMeta.path }) || user.role.features.some((to) => matchRoute({ to }));
  if (!ok) return <AccessDeniedPage />;
  return children;
};

export default AuthPage;
