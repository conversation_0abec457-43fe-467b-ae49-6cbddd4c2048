import { Suspense } from 'react';

import { Outlet } from '@tanstack/react-router';

import { AppProviders } from './AppProviders';
import AuthPage from './AuthPage';
import { Authentication } from './Authentication';
import { LoadingSpinner } from './LoadingSpinner';
import PageLayout from './layout/PageLayout';

export const AppRoute = () => {
  return (
    <Authentication>
      <AppProviders>
        <AuthPage>
          <PageLayout>
            <Suspense fallback={<LoadingSpinner />}>
              <Outlet />
            </Suspense>
          </PageLayout>
        </AuthPage>
      </AppProviders>
    </Authentication>
  );
};
