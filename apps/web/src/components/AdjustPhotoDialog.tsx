import { adjustBrightnessAndSaturation } from '@/utils/images';
import { Contrast, LightMode } from '@mui/icons-material';
import {
  Button,
  Card,
  CardMedia,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  Slider,
  Stack,
} from '@mui/material';
import type { Image, LocalImage } from 'common';
import React from 'react';

interface Props {
  open: boolean;
  onClose: () => void;
  image?: Image;
  onAdjust: (adjustedImages: LocalImage) => void;
}

type Adjustment = {
  brightness: number;
  saturation: number;
};

export const AdjustPhotoDialog = ({ open, onClose, image, onAdjust }: Props) => {
  if (!image || !('base64' in image)) return null;
  const DEFAULT_ADJUSTMENTS = { brightness: 100, saturation: 100 };

  const [adjustments, setAdjustments] = React.useState<Adjustment>(DEFAULT_ADJUSTMENTS);
  const [originalBase64] = React.useState(image.base64);

  const handleReset = () => {
    setAdjustments(DEFAULT_ADJUSTMENTS);
    image.base64 = originalBase64;
  };

  const handleContinue = async () => {
    const base64 = await adjustBrightnessAndSaturation(
      originalBase64,
      adjustments.brightness,
      adjustments.saturation,
    );
    onAdjust({ ...image, base64, filename: image.filename, description: image.description });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>画像の明度、彩度の調整</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ width: '100%' }}>
          <Grid sx={{ width: '100%' }}>
            <Card sx={{ position: 'relative', width: 1 }}>
              <CardMedia
                component="img"
                image={image.base64}
                alt="Image adjust"
                sx={{
                  filter: `brightness(${adjustments.brightness}%) saturate(${adjustments.saturation}%)`,
                  width: 1,
                  aspectRatio: '1/1',
                  objectFit: 'cover',
                }}
              />
              <Stack
                spacing={1}
                sx={{
                  position: 'absolute',
                  left: 0,
                  bottom: 0,
                  width: 1,
                  p: 1,
                }}
              >
                <Stack
                  spacing={2}
                  direction="row"
                  sx={{
                    p: 1,
                    borderRadius: 2,
                    alignItems: 'center',
                    overflow: 'hidden',
                    bgcolor: (t) => t.palette.background.translucentWhite,
                  }}
                >
                  <LightMode />
                  <Slider
                    value={adjustments.brightness}
                    onChange={(_, value) => {
                      setAdjustments((prev) => ({
                        ...prev,
                        brightness: typeof value === 'number' ? value : prev.brightness,
                      }));
                    }}
                    min={0}
                    max={200}
                    aria-labelledby="brightness-slider"
                    size="small"
                  />
                </Stack>
                <Stack
                  spacing={2}
                  direction="row"
                  sx={{
                    p: 1,
                    borderRadius: 2,
                    overflow: 'hidden',
                    bgcolor: (t) => t.palette.background.translucentWhite,
                  }}
                >
                  <Contrast />
                  <Slider
                    value={adjustments.saturation}
                    onChange={(_, value) => {
                      setAdjustments((prev) => ({
                        ...prev,
                        saturation: typeof value === 'number' ? value : prev.saturation,
                      }));
                    }}
                    min={0}
                    max={200}
                    aria-labelledby="saturation-slider"
                    size="small"
                  />
                </Stack>
              </Stack>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button variant="outlined" onClick={handleReset} fullWidth>
          リセット
        </Button>
        <Button variant="contained" onClick={handleContinue} fullWidth>
          完了
        </Button>
      </DialogActions>
    </Dialog>
  );
};
