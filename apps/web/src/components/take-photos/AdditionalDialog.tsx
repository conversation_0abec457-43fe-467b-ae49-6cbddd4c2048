import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';

import { TwoActionButtons } from '@/components/TwoActionButtons';
import { Add, NavigateNext } from '@mui/icons-material';

type Props = {
  open: boolean;
  onNext: () => void;
  onComplete: () => void;
};

export const AdditionalDialog = ({ open, onNext, onComplete }: Props) => {
  return (
    <Dialog open={open} maxWidth="sm" fullWidth>
      <DialogTitle>写真の追加撮影</DialogTitle>
      <DialogContent>
        <DialogContentText>写真を追加で撮影しますか？</DialogContentText>
      </DialogContent>
      <DialogActions>
        <TwoActionButtons
          primary={{
            icon: NavigateNext,
            label: '次へ',
            onClick: onComplete,
          }}
          secondary={{
            icon: Add,
            end: true,
            label: 'もう一枚撮影する',
            onClick: onNext,
          }}
        />
      </DialogActions>
    </Dialog>
  );
};
