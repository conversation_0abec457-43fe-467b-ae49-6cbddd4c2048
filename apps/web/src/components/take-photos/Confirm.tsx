import { Card, CardMedia, Container, Stack } from '@mui/material';

import { type StackButtonProps, TwoActionButtons } from '@/components/TwoActionButtons';
import type { StateVersion } from './state';

type Props = {
  image: StateVersion['images'][number];
  primary: StackButtonProps;
  secondary: StackButtonProps;
};

export const Confirm = ({ image, primary, secondary }: Props) => {
  return (
    <Container maxWidth="sm" sx={{ height: '100%', display: 'flex', alignItems: 'center' }}>
      <Stack width="100%" spacing={1} alignItems="center" sx={{ p: 3 }}>
        <Card
          sx={{
            width: '100%',
            aspectRatio: '1/1',
          }}
        >
          <CardMedia sx={{ height: '100%' }} image={image.base64} title={image.filename} />
        </Card>
        <TwoActionButtons
          primary={primary}
          secondary={secondary}
          fullWidth
          sx={{ width: '100%' }}
        />
      </Stack>
    </Container>
  );
};
