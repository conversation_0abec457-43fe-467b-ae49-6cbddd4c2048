import type { LocalImage } from 'common';
import type { ImageSettings } from 'models';

export type TakePhotosState = 'guide' | 'camera' | 'confirm' | 'additional' | 'end';
export type StateVersion = {
  state: TakePhotosState;
  images: LocalImage[];
};
export type StateVersions = StateVersion[];

const calcInitialState = (settings: ImageSettings): TakePhotosState => {
  if (settings.guides.length !== 0) return 'guide';
  return settings.min !== 0 ? 'camera' : 'additional';
};

export const calcInitialStateVersion = (settings: ImageSettings): StateVersion => {
  const state = calcInitialState(settings);
  return { state, images: [] };
};

export const calcNextStateOnConfirm = (
  images: number,
  settings: ImageSettings,
): TakePhotosState => {
  if (images < settings.guides.length) return 'guide';
  if (images < settings.min) return 'camera';
  if (images < settings.max) return 'additional';
  return 'end';
};
