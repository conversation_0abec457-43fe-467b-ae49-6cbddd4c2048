import { Card, CardMedia, Container, Paper, Stack, Typography } from '@mui/material';

import { type StackButtonProps, TwoActionButtons } from '@/components/TwoActionButtons';

import { BASE_URL } from '@/types/vite-env';
import { ImageUtil } from 'common';
import type { ImageSettings } from 'models';

type Props = {
  guide: ImageSettings['guides'][number];
  primary: StackButtonProps;
  secondary: StackButtonProps;
};

export const Guide = ({ guide, primary, secondary }: Props) => {
  const img = new ImageUtil(guide);
  return (
    <Container maxWidth="sm" sx={{ height: '100%', display: 'flex', alignItems: 'center' }}>
      <Stack component={Paper} width="100%" spacing={1} alignItems="center" sx={{ p: 2 }}>
        <Card
          sx={{
            width: '100%',
            aspectRatio: '1/1',
          }}
        >
          <CardMedia sx={{ height: '100%' }} image={img.src(BASE_URL)} title={guide.description} />
        </Card>
        <Typography variant="h5">{guide.description}</Typography>
        <TwoActionButtons
          primary={primary}
          secondary={secondary}
          fullWidth
          sx={{ width: '100%' }}
          spacing={2}
        />
      </Stack>
    </Container>
  );
};
