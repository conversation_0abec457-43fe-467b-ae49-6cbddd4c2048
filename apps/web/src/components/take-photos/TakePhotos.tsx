import React from 'react';

import { Close, NavigateBefore, NavigateNext, Undo } from '@mui/icons-material';
import { useRouter } from '@tanstack/react-router';

import { type ImageSettings, takePhotoDescription, takePhotoFilename } from 'models';

import type { StackButtonProps } from '@/components/TwoActionButtons';
import type { LocalImage } from 'common';
import { Camera } from './Camera';
import { Confirm } from './Confirm';
import { Guide } from './Guide';
import {
  type StateVersion,
  type StateVersions,
  type TakePhotosState,
  calcInitialStateVersion,
  calcNextStateOnConfirm,
} from './state';

type Props = {
  settings: ImageSettings;
  onComplete: (images: LocalImage[]) => void;
  onCancel?: () => void;
};

type VersioningCallback = (version: StateVersion) => StateVersion;

export const TakePhotos = ({ settings, onComplete, onCancel }: Props) => {
  const router = useRouter();
  const [versions, setVersions] = React.useState<StateVersions>([
    calcInitialStateVersion(settings),
  ]);
  const current = versions.at(-1);
  if (current === undefined) throw new Error('current is undefined');
  const { state, images } = current;
  const setStateVersion = (callback: VersioningCallback) =>
    setVersions((versions) => [...versions, callback(current)]);
  const setState = (state: TakePhotosState) =>
    setStateVersion((version) => ({ ...version, state }));
  const undo = () => setVersions((versions) => versions.slice(0, -1));

  if (state === 'guide') {
    const guide = settings.guides.at(images.length);
    if (guide === undefined) throw new Error('guide is undefined');
    const primary: StackButtonProps = {
      icon: NavigateNext,
      label: '次へ',
      onClick: () => setState('camera'),
    };
    const isFirstPhoto = images.length === 0;
    const secondary: StackButtonProps = {
      icon: isFirstPhoto ? Close : NavigateBefore,
      label: isFirstPhoto ? 'キャンセル' : '戻る',
      onClick: () => {
        if (isFirstPhoto) {
          if (onCancel) onCancel();
          else router.history.go(-1);
        } else {
          undo();
        }
      },
    };
    return <Guide guide={guide} primary={primary} secondary={secondary} />;
  }

  if (state === 'confirm') {
    const image = images.at(images.length - 1);
    if (image === undefined) throw new Error('image is undefined');
    const primary: StackButtonProps = {
      icon: NavigateNext,
      label: '次へ',
      onClick: () => setState(calcNextStateOnConfirm(images.length, settings)),
    };
    const secondary: StackButtonProps = {
      icon: Undo,
      label: '撮り直し',
      onClick: undo,
    };
    return <Confirm image={image} primary={primary} secondary={secondary} />;
  }

  const handleTakePhoto = (image: StateVersion['images'][number]) => {
    const nextImages = [...images, image];
    if (settings.max === nextImages.length) onComplete(nextImages);
    else setStateVersion(() => ({ state: 'confirm', images: nextImages }));
  };

  const handlePrev = undo;
  const handleNext = () => setState('camera');
  const handleComplete = () => onComplete(images);
  const filename = takePhotoFilename(images.length + 1);
  const description =
    settings.guides.at(images.length)?.description ?? takePhotoDescription(images.length + 1);
  return (
    <Camera
      state={state}
      filename={filename}
      description={description}
      onTakePhoto={handleTakePhoto}
      onPrev={handlePrev}
      onNext={handleNext}
      onComplete={handleComplete}
    />
  );
};
