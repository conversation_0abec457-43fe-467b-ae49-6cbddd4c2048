import { Icon<PERSON><PERSON>on, Stack, Tooltip } from '@mui/material';

import { type LocalImage, base64ToContentType, contentTypeToExt } from 'common';

import { CameraCapture } from '@/components/CameraCapture';
import { ArrowBack } from '@mui/icons-material';
import { resizeToSquare } from 'mui-ex';
import { AdditionalDialog } from './AdditionalDialog';
import type { TakePhotosState } from './state';

type Props = {
  state: TakePhotosState;
  filename: string;
  description: string;
  onTakePhoto: (image: LocalImage) => void;
  onPrev: () => void;
  onNext: () => void;
  onComplete: () => void;
};

export const Camera = ({
  state,
  filename,
  description,
  onTakePhoto,
  onPrev,
  onNext,
  onComplete,
}: Props) => {
  const handleCapture = async (imageSrc: string) => {
    const base64 = await resizeToSquare(imageSrc);
    const contentType = base64ToContentType(base64);
    const ext = contentTypeToExt(contentType);
    onTakePhoto({ id: base64, filename: `${filename}.${ext}`, base64, description });
  };

  return (
    <Stack justifyContent="center" alignItems="center" sx={{ width: '100%', height: '100%' }}>
      <CameraCapture
        description={description}
        onCapture={handleCapture}
        CloseButton={
          <Tooltip title="戻る">
            <IconButton onClick={onPrev}>
              <ArrowBack sx={{ color: 'white' }} />
            </IconButton>
          </Tooltip>
        }
      />
      <AdditionalDialog open={state === 'additional'} onNext={onNext} onComplete={onComplete} />
    </Stack>
  );
};
