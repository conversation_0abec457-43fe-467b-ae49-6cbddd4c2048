import { createContext, useContext } from 'react';

import { Box, type Breakpoint, Stack, type SxProps, Typography } from '@mui/material';

type Context = { titleWidth?: number; contentWidth?: number | null };
export const FormSectionContext = createContext<Context>({});

type Props = {
  title: string;
  children: React.JSX.Element;
  sx?: SxProps;
};

/**
 * # FormSection
 *
 * フォームのセクションを作成するためのコンポーネントです。
 *
 * 複数のフォーム要素をグループ化して表示するために使用します。
 *
 * ※１つのフォーム要素を表示する場合は `FormSection` を使用しないでください。
 *
 * ## Usage
 *
 * ### デフォルト値で使用
 *
 * そのまま FormSection を使用すると以下の横幅で表示されます。
 * - タイトルの横幅: 180px
 * - コンテンツの横幅: 300px
 *
 * ```tsx
 * <FormSection title="タイトル">
 *   ...
 * </FormSection>
 * ```
 *
 * ### タイトル／コンテンツの横幅を変更
 *
 * タイトルの横幅またはコンテンツの横幅を変更する場合は
 *
 * `FormSectionContext.Provider` を使用します。
 *
 * ```tsx
 * // コンポーネントの外で定義
 * const contextValue = { titleWidth: 120, contentWidth: 400 };
 * ...
 *
 * <FormSectionContext.Provider value={contextValue}>
 *   <FormSection title="タイトル">
 *     ...
 *   </FormSection>
 * </FormSectionContext.Provider>
 * ```
 *
 * ### コンテンツの横幅を親要素いっぱいに広げる
 *
 * コンテンツの横幅を親要素いっぱいに広げる場合は
 *
 * `contentWidth` に null を設定します。
 *
 * ```tsx
 * // コンポーネントの外で定義
 * const contextValue = { contentWidth: null };
 *
 * <FormSectionContext.Provider value={contextValue}>
 *   <FormSection title="タイトル">
 *     ...
 *   </FormSection>
 * </FormSectionContext.Provider>
 */
export const FormSection: React.FC<Props> = ({ children, title, sx }) => {
  const context = useContext(FormSectionContext);
  const titleWidth = context.titleWidth ?? 180;
  const contentWidth = context.contentWidth === undefined ? 300 : context.contentWidth;
  const breakpoint: Breakpoint = 'md';
  if (contentWidth === null)
    return (
      <Stack
        sx={{ width: '100%', ...sx }}
        direction={{ xs: 'column', [breakpoint]: 'row' }}
        alignItems={{ xs: 'flex-start', [breakpoint]: 'center' }}
      >
        <Box sx={{ width: titleWidth }}>
          <Typography variant="subtitle1">{title}</Typography>
        </Box>
        <Stack
          sx={{ width: { xs: '100%', [breakpoint]: `calc(100% - ${titleWidth}px)` } }}
          spacing={2}
        >
          {children}
        </Stack>
      </Stack>
    );

  return (
    <Stack
      sx={{
        width: '100%',
        minWidth: {
          xs: `max(${titleWidth}px,${contentWidth}px)`,
          [breakpoint]: `calc(${titleWidth}px + ${contentWidth}px)`,
        },
        ...sx,
      }}
      direction={{ xs: 'column', [breakpoint]: 'row' }}
      alignItems={{ xs: 'flex-start', [breakpoint]: 'center' }}
    >
      <Box sx={{ width: titleWidth }}>
        <Typography variant="subtitle1" sx={{ width: titleWidth }}>
          {title}
        </Typography>
      </Box>
      <Stack sx={{ minWidth: contentWidth, pl: { xs: 1, [breakpoint]: 0 } }} spacing={2}>
        {children}
      </Stack>
    </Stack>
  );
};
