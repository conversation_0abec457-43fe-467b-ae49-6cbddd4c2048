import type { RouteItemMeta } from '@/router/core/types';

import { Card, CardActionArea, CardContent, Stack, Typography } from '@mui/material';
import { createLink } from '@tanstack/react-router';

const RouterCardActionArea = createLink(CardActionArea);

export const MenuItemCard = ({ useTitle, path, Icon }: RouteItemMeta) => {
  const title = useTitle();
  return (
    <Card sx={{ height: 1 }}>
      <RouterCardActionArea to={path} sx={{ height: 1 }}>
        <CardContent component={Stack} alignItems="center" spacing={2}>
          <Icon sx={{ fontSize: 80 }} />
          <Typography variant="h5">{title}</Typography>
        </CardContent>
      </RouterCardActionArea>
    </Card>
  );
};
