import { ChevronRight } from '@mui/icons-material';
import {
  Box,
  IconButton,
  ListItem,
  ListItemButton,
  type ListItemButtonProps,
  type ListItemProps,
  ListItemText,
  Stack,
  type Theme,
  useMediaQuery,
} from '@mui/material';
import Typography from '@mui/material/Typography';

type Props = {
  label: string;
  value: string | null | undefined;
  onClick?: () => void;
  divider?: boolean;
};

export default function KeyValueListItem({ label, value, onClick, divider }: Props) {
  const Item: React.FC<ListItemProps & ListItemButtonProps> = !onClick ? ListItem : ListItemButton;
  const isXs = useMediaQuery<Theme>((theme) => theme.breakpoints.only('xs'));
  if (isXs) {
    return (
      <Item onClick={() => onClick?.()} divider={divider} dense>
        <ListItemText
          primary={
            <Typography variant="caption" color="text.secondary">
              {label}
            </Typography>
          }
          secondary={<Typography>{value}</Typography>}
        />
      </Item>
    );
  }

  return (
    <Item onClick={() => onClick?.()} divider={divider}>
      <Stack direction="row" alignItems="center" spacing={1} sx={{ width: 1 }}>
        <Box sx={{ flex: '0 0 300px' }}>
          <Typography variant="subtitle1" color="text.secondary">
            {label}
          </Typography>
        </Box>
        <Box sx={{ flexGrow: 1 }}>
          <Typography>{value}</Typography>
        </Box>
        {onClick && (
          <IconButton>
            <ChevronRight />
          </IconButton>
        )}
      </Stack>
    </Item>
  );
}
