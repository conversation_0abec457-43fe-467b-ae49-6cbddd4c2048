import React from 'react';

import {
  Box,
  Card,
  CardActionArea,
  CardMedia,
  Stack,
  type Theme,
  useMediaQuery,
  useTheme,
} from '@mui/material';

import { type Image, ImageUtil, isNullish } from 'common';

import { BASE_URL } from '@/types/vite-env';

import { P, match } from 'ts-pattern';
import { ImagePreviewDialog } from './ImagePreviewDialog';

type Size = 'small' | 'medium' | 'large';
type Props<T extends Image> = {
  images: T[];
  cols?: number;
  minCols?: number;
  size?: Size;
  createChip?: (image: T | undefined) => React.JSX.Element | undefined;
};

const widthByCols = (cols: number, t: Theme) => `calc((100% - ${t.spacing(cols)}) / ${cols})`;

export const Thumbnails = <T extends Image>({
  images,
  cols,
  minCols = 2,
  size,
  createChip,
}: Props<T>) => {
  const [previewIndex, setPreviewIndex] = React.useState<number | undefined>();
  const t = useTheme<Theme>();
  const xs = useMediaQuery(t.breakpoints.only('xs'));

  if (images.length === 0) return;
  if (isNullish(cols) && isNullish(size)) throw new Error('Either cols or size must be provided');
  if (!isNullish(cols) && !isNullish(size))
    throw new Error('Either cols or size must be provided, not both');

  const calcSize = (count: number) => count * 46 + Number(t.spacing(count - 1).slice(0, -2));
  const width = match({ cols, size, xs })
    .with({ cols: P.number }, ({ cols }) => widthByCols(cols, t))
    .with({ xs: true }, () => widthByCols(minCols, t))
    .with({ size: 'small', xs: false }, () => calcSize(1))
    .with({ size: 'medium', xs: false }, () => calcSize(2))
    .with({ size: 'large', xs: false }, () => calcSize(4))
    .with({ cols: undefined }, () => '100%') // 到達しない
    .with({ size: undefined }, () => '100%') // 到達しない
    .exhaustive();

  const preview = typeof previewIndex === 'number' ? images[previewIndex] : undefined;
  return (
    <Box sx={{ overflow: 'hidden' }}>
      <Stack direction="row" flexWrap="wrap" style={{ margin: t.spacing(-0.5) }}>
        {images.map((image, index) => {
          const img = new ImageUtil(image);
          const chip = createChip?.(image);
          return (
            <Card key={img.key} sx={{ width, m: 0.5 }}>
              <CardActionArea sx={{ width: 1 }} onClick={() => setPreviewIndex(index)}>
                {chip && (
                  <Box sx={{ position: 'relative', width: 1, height: 0 }}>
                    <Box sx={{ position: 'absolute', width: 1, height: 'max-content', p: 0.5 }}>
                      {chip}
                    </Box>
                  </Box>
                )}
                <CardMedia
                  image={img.src(BASE_URL)}
                  title={img.description}
                  sx={{ width: 1, aspectRatio: '1/1', objectFit: 'cover' }}
                />
              </CardActionArea>
            </Card>
          );
        })}
      </Stack>
      <ImagePreviewDialog
        images={images}
        index={previewIndex}
        createChip={createChip}
        open={Boolean(preview)}
        onClose={() => setPreviewIndex(undefined)}
      />
    </Box>
  );
};
