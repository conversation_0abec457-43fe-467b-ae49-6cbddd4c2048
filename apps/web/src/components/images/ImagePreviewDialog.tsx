import { BASE_URL } from '@/types/vite-env';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  Stack,
  Typography,
} from '@mui/material';
import { enqueueSnackbar } from 'notistack';
import { useState } from 'react';
import { EffectCoverflow, Navigation, Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';

import { Close, Download } from '@mui/icons-material';
import { type Image, ImageUtil, downloadFromUrl } from 'common';

type Props<T extends Image> = {
  images: T[];
  createChip?: (image: T | undefined) => React.JSX.Element | undefined;
  index: number | undefined;
  open: boolean;
  onClose: () => void;
};

export const ImagePreviewDialog = <T extends Image>({
  images,
  index,
  createChip,
  open,
  onClose,
}: Props<T>) => {
  const [currentSlide, setCurrentSlide] = useState(index ?? 0);

  const handleDownload = async () => {
    const currentImage = images[currentSlide];
    if (!currentImage) return;

    const img = new ImageUtil(currentImage);
    const imageUrl = img.src(BASE_URL);

    try {
      await downloadFromUrl(imageUrl);
      enqueueSnackbar('画像をダウンロードしました', {
        variant: 'success',
      });
    } catch (error) {
      console.error('画像のダウンロードに失敗しました:', error);
      enqueueSnackbar('画像のダウンロードに失敗しました', {
        variant: 'error',
      });
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth={false} fullWidth>
      <DialogContent sx={{ p: 0 }}>
        <Swiper
          navigation
          pagination={{ clickable: true }}
          modules={[Navigation, Pagination, EffectCoverflow]}
          effect="coverflow"
          grabCursor={true}
          centeredSlides={true}
          slidesPerView={'auto'}
          coverflowEffect={{
            rotate: 50,
            depth: 200,
            scale: 0.8,
            slideShadows: false,
          }}
          spaceBetween={8}
          initialSlide={index}
          onSlideChange={(swiper) => setCurrentSlide(swiper.activeIndex)}
        >
          {images.map((image) => {
            const img = new ImageUtil(image);
            const chip = createChip?.(image);
            return (
              <SwiperSlide key={img.key} style={{ width: 'min(100vw - 64px, 100vh - 300px)' }}>
                <Box>
                  {(chip || img?.description) && (
                    <Stack
                      direction="row"
                      alignItems="center"
                      spacing={1}
                      sx={{
                        width: 1,
                        height: 'max-content',
                        p: 1,
                      }}
                    >
                      {chip}
                      <Typography variant="h6" sx={{ px: 1 }}>
                        {img?.description}
                      </Typography>
                    </Stack>
                  )}
                  <Box
                    component="img"
                    src={img?.src(BASE_URL)}
                    sx={{
                      display: 'block',
                      width: 1,
                      aspectRatio: '1/1',
                      objectFit: 'cover',
                    }}
                  />
                </Box>
              </SwiperSlide>
            );
          })}
        </Swiper>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" startIcon={<Download />} onClick={handleDownload}>
          ダウンロード
        </Button>
        <Button variant="contained" startIcon={<Close />} onClick={onClose}>
          閉じる
        </Button>
      </DialogActions>
    </Dialog>
  );
};
