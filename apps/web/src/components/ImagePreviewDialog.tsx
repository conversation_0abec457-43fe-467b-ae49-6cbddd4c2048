import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  ImageList,
  ImageListItem,
  type Theme,
  useMediaQuery,
} from '@mui/material';

type Props = {
  isOpen: boolean;
  toggleDialog: (isOpen: boolean) => void;
  imagePreview: string;
};

export const ImagePreviewDialog = ({ isOpen, toggleDialog, imagePreview }: Props) => {
  const isMobile = useMediaQuery<Theme>((theme) => theme.breakpoints.down('sm'));

  return (
    <Dialog open={isOpen} onClose={() => toggleDialog(false)} maxWidth="xl">
      <DialogContent>
        <ImageList cols={1} gap={8}>
          <ImageListItem>
            <img
              src={imagePreview}
              alt="Preview"
              style={{ objectFit: 'cover', height: isMobile ? '200px' : '700px' }}
            />
          </ImageListItem>
        </ImageList>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={() => toggleDialog(false)}>
          閉じる
        </Button>
      </DialogActions>
    </Dialog>
  );
};
