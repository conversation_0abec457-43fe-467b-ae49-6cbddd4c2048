import { MenuItemCard } from '@/components/MenuItemCard';
import { type RouteGroupMeta, useVisible } from '@/router/core/types';
import { Grid } from '@mui/material';
import { MainLayout } from './core/layout/MainLayout';

type Props = { group: RouteGroupMeta };

export const BaseMenuPage = ({ group }: Props) => {
  const title = group.useTitle();
  return (
    <MainLayout title={title} maxWidth="lg" noBack scrollable>
      <Grid container spacing={{ xs: 1, sm: 3 }}>
        {group.children
          .filter((meta) => useVisible(meta))
          .map((meta) => {
            return (
              <Grid key={meta.path} size={{ xs: 6, md: 4 }}>
                <MenuItemCard {...meta} />
              </Grid>
            );
          })}
      </Grid>
    </MainLayout>
  );
};
