import type { SvgIconComponent } from '@mui/icons-material';
import { LoadingButton, type LoadingButtonProps } from '@mui/lab';
import {
  type Breakpoint,
  Stack,
  type StackProps,
  type SxProps,
  type Theme,
  useMediaQuery,
} from '@mui/material';
import { match } from 'ts-pattern';

import { renderButtonIcon } from './render-button-icon';

export type StackButtonProps = Omit<
  LoadingButtonProps,
  'variant' | 'fullWidth' | 'startIcon' | 'endIcon' | 'sx' | 'children'
> & {
  label: string;
  icon: SvgIconComponent;
  sx?: Omit<SxProps<Theme>, 'width' | 'flexGrow'>;
  hide?: boolean;
};

type Props = Omit<StackProps, 'direction' | 'sx'> & {
  breakpoints?: {
    /** @default sm 左右配置の下限 (rowMin以下は上下配置) */
    rowMin?: Breakpoint;
    /** @default lg fullWidthの上限 (`fullWidth:true`の場合には無効) */
    fullWidthMax?: Breakpoint;
  };
  /** @default false fullWidthの上限を超えている場合にfullWidthするか */
  fullWidth?: boolean;
  sx?: Omit<SxProps<Theme>, 'flexDirection'>;

  primary: StackButtonProps;
  secondary: StackButtonProps & {
    end?: boolean;
  };
};

export const TwoActionButtons: React.FC<Props> = ({
  breakpoints,
  fullWidth = false,
  sx,
  primary,
  secondary: { end = false, ...secondary },
  ...stackProps
}) => {
  const rowMin = breakpoints?.rowMin ?? 'sm';
  const fullWidthMax = breakpoints?.fullWidthMax ?? 'lg';
  const isRow = useMediaQuery((theme: Theme) => theme.breakpoints.up(rowMin));
  const isFullWidth = useMediaQuery((theme: Theme) => theme.breakpoints.down(fullWidthMax));
  const secondaryIcon = renderButtonIcon(secondary.icon);
  const secondaryIconProps: Pick<LoadingButtonProps, 'startIcon' | 'endIcon'> = match({
    end,
    isRow,
  })
    .with({ isRow: false }, () => ({ endIcon: secondaryIcon }))
    .with({ end: true }, () => ({ endIcon: secondaryIcon }))
    .with({ end: false }, () => ({ startIcon: secondaryIcon }))
    .exhaustive();
  return (
    <Stack
      spacing={2}
      {...stackProps}
      direction={{ xs: 'column', [rowMin]: 'row-reverse' }}
      sx={{ width: '100%', ...sx }}
    >
      {!primary.hide && (
        <LoadingButton
          {...primary}
          variant="contained"
          endIcon={renderButtonIcon(primary.icon)}
          fullWidth={isFullWidth || fullWidth}
          sx={{ ...primary.sx }}
        >
          {primary.label}
        </LoadingButton>
      )}
      {!secondary.hide && (
        <LoadingButton
          {...secondary}
          variant="outlined"
          {...secondaryIconProps}
          fullWidth={isFullWidth || fullWidth}
          sx={{ ...secondary.sx }}
        >
          {secondary.label}
        </LoadingButton>
      )}
    </Stack>
  );
};
