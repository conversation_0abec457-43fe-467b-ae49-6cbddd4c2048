import CloseIcon from '@mui/icons-material/Close';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  type DialogProps,
  DialogTitle,
} from '@mui/material';
import IconButton from '@mui/material/IconButton';

import { Multiline } from 'mui-ex/src/common/Multiline';

type Props = DialogProps & {
  title?: string;
  onClose: () => void;
  onConfirm: () => void;
};

export const DeleteConfirmDialog = ({ title, open, onClose, onConfirm, ...props }: Props) => {
  return (
    <Dialog open={open} onClose={onClose} {...props}>
      <DialogTitle>{title ?? '削除確認'}</DialogTitle>
      <IconButton
        aria-label="close"
        onClick={onClose}
        sx={{
          position: 'absolute',
          right: (t) => t.spacing(1),
          top: (t) => t.spacing(1),
          color: (t) => t.palette.grey[500],
        }}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent dividers>
        <DialogContentText>
          <Multiline
            text={`
              本当に削除してもよろしいですか？
              ※この操作は取り消せません。`}
          />
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={onClose}>
          閉じる
        </Button>
        <Button variant="contained" onClick={onConfirm} color="error">
          削除する
        </Button>
      </DialogActions>
    </Dialog>
  );
};
