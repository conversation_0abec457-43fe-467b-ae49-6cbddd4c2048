import { objectEntries, objectFromEntries } from 'common';
import type { Map as LeafletMap } from 'leaflet';
import { useMapEvents } from 'react-leaflet';

type Props = Partial<Record<keyof Parameters<typeof useMapEvents>[0], (map: LeafletMap) => void>>;

// 座標を受け取ってマップの中心に移動させる副作用のみのコンポーネント
export const GsiEventListener = (props: Props) => {
  const map = useMapEvents(
    objectFromEntries(objectEntries(props).map(([key, value]) => [key, () => value(map)])),
  );
  return null;
};
