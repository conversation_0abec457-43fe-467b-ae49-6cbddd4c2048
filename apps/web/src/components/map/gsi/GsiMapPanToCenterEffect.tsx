import React from 'react';

import { useMap } from 'react-leaflet';

type Props = {
  center?: google.maps.LatLngLiteral | null | undefined;
  disabled?: boolean;
};

// 座標を受け取ってマップの中心に移動させる副作用のみのコンポーネント
export const GsiMapPanToCenterEffect = ({ center, disabled }: Props) => {
  const map = useMap();
  React.useEffect(() => {
    if (map && center && !disabled) map.panTo(center);
  }, [map, center, disabled]);
  return null;
};
