import { useTimeout } from '@/hooks/useTimeout';
import type { Popup as LeafletPopup } from 'leaflet';
import React from 'react';
import { useMap } from 'react-leaflet';

export const usePopupSync = (open: boolean) => {
  const map = useMap();
  const [refReady, setRefReady] = React.useState(false);
  const popupRef = React.useRef<LeafletPopup | null>(null);

  const { start } = useTimeout(() => {
    if (popupRef.current) popupRef.current.openOn(map);
  }, 0.5);

  React.useEffect(() => {
    if (refReady && popupRef.current) {
      if (open) start();
      else popupRef.current.close();
    }
  }, [refReady, open, start]);

  return React.useCallback((r: LeafletPopup) => {
    popupRef.current = r;
    setRefReady(true);
  }, []);
};
