import React from 'react';

import { useMap } from '@vis.gl/react-google-maps';

type Props = {
  id: string;
  center?: google.maps.LatLngLiteral | null | undefined;
  disabled?: boolean;
};

// 座標を受け取ってマップの中心を移動させる
export const useGoogleMapPanToCenterEffect = ({ id, center, disabled }: Props) => {
  const map = useMap(id);
  React.useEffect(() => {
    if (map && center && !disabled) map.panTo(center);
  }, [map, center, disabled]);
};
