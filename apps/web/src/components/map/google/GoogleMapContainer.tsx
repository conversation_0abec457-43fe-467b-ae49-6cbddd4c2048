import type { PropsWithChildren } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

import { Typography } from '@mui/material';
import { useApiLoadingStatus } from '@vis.gl/react-google-maps';

import { useAppContext } from '@/contexts/app-context';
import LostMap from '../LostMap';

const GoogleMapLoadingError = () => {
  return (
    <LostMap>
      <Typography variant="h6" color="text.secondary">
        Google Mapの読み込みに失敗しました
      </Typography>
    </LostMap>
  );
};

const GoogleMapStatusHandler = ({ children }: PropsWithChildren) => {
  const apiStatus = useApiLoadingStatus();
  const { labels } = useAppContext();

  if (apiStatus === 'FAILED') {
    return (
      <LostMap>
        <>
          <Typography variant="h5" color="text.secondary">
            Google Mapの読み込みに失敗しました
          </Typography>
          <Typography variant="h6" color="text.secondary">
            {`ページの${labels.action.reload}をお試しください`}
          </Typography>
        </>
      </LostMap>
    );
  }
  if (apiStatus === 'AUTH_FAILURE') {
    return (
      <LostMap>
        <>
          <Typography variant="h5" color="text.secondary">
            Google Mapにアクセスできません
          </Typography>
          <Typography variant="h6" color="text.secondary">
            管理者へお問い合わせください
          </Typography>
        </>
      </LostMap>
    );
  }

  return <>{children}</>;
};

export default function GoogleMapContainer({ children }: PropsWithChildren) {
  return (
    <ErrorBoundary FallbackComponent={GoogleMapLoadingError}>
      <GoogleMapStatusHandler>{children}</GoogleMapStatusHandler>
    </ErrorBoundary>
  );
}
