import { type Control, type UseFormWatch, useController } from 'react-hook-form';

import { EditPoint } from './edit-point/_EditLocation';
import { ReadPoint } from './read-point/_ReadPoint';

type Target = google.maps.LatLngLiteral;
type CommonProps = { readOnly?: boolean };
type Props<T extends Target> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<Target>;
  watch: UseFormWatch<Target>;
};

export const RhfMapPoint = <T extends Target>(props: Props<T>) => {
  const { readOnly, control, watch } = props as unknown as InnerProps;

  const latCtrl = useController({ name: 'lat', control });
  const lngCtrl = useController({ name: 'lng', control });

  const position = { lat: watch('lat'), lng: watch('lng') };
  if (readOnly) return <ReadPoint position={position} />;
  return (
    <EditPoint
      position={position}
      onChange={(position) => {
        latCtrl.field.onChange(position.lat);
        lngCtrl.field.onChange(position.lng);
      }}
    />
  );
};
