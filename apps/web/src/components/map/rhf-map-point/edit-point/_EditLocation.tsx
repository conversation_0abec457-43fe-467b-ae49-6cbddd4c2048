import { useMapType } from '@/stores/map';
import { GoogleEditPoint } from './GoogleEditPoint';
import { GsiEditPoint } from './GsiEditPoint';

type Props = {
  position: google.maps.LatLngLiteral | null | undefined;
  onChange: (position: google.maps.LatLngLiteral) => void;
};

export const EditPoint = ({ position, onChange }: Props) => {
  const mapType = useMapType();
  if (mapType === 'google') return <GoogleEditPoint position={position} onChange={onChange} />;
  return <GsiEditPoint position={position} onChange={onChange} />;
};
