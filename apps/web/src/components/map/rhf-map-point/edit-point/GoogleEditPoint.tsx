import React from 'react';

import { AdvancedMarker, Map as GoogleMap, useMap } from '@vis.gl/react-google-maps';

import { useAppContext } from '@/contexts/app-context';

import { Paper } from '@mui/material';
import GoogleMapContainer from '../../google/GoogleMapContainer';

type Props = {
  position: google.maps.LatLngLiteral | null | undefined;
  onChange: (position: google.maps.LatLngLiteral) => void;
};

const _GoogleEditPoint = ({ position, onChange }: Props) => {
  const { map } = useAppContext();
  const [id] = React.useState(crypto.randomUUID());
  const mapRef = useMap(id);
  const handleDragEnd = async (event: google.maps.MapMouseEvent) => {
    if (event.latLng === null) return;
    const next: google.maps.LatLngLiteral = { lat: event.latLng.lat(), lng: event.latLng.lng() };
    onChange(next);
  };

  const center = position ?? map.defaultCenter;
  React.useEffect(() => {
    mapRef?.panTo(center);
  }, [mapRef?.panTo, center]);

  return (
    <Paper sx={{ width: 1, height: 1, overflow: 'hidden' }}>
      <GoogleMap
        {...map}
        id={id}
        defaultCenter={center}
        disableDefaultUI
        zoomControl
        scrollwheel={false}
      >
        <AdvancedMarker position={position} draggable onDragEnd={handleDragEnd} />
      </GoogleMap>
    </Paper>
  );
};

export const GoogleEditPoint = (props: Props) => {
  return (
    <GoogleMapContainer>
      <_GoogleEditPoint {...props} />
    </GoogleMapContainer>
  );
};
