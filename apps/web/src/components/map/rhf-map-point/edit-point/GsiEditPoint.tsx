import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>mControl } from 'react-leaflet';

import { useAppContext } from '@/contexts/app-context';
import { Paper } from '@mui/material';
import type { DragEndEvent } from 'leaflet';
import { GsiMapPanToCenterEffect } from '../../gsi/GsiMapPanToCenterEffect';
import { GsiTileLayer } from '../../gsi/GsiTileLayer';

type Props = {
  position: google.maps.LatLngLiteral | null | undefined;
  onChange: (position: google.maps.LatLngLiteral) => void;
};

export const GsiEditPoint = ({ position, onChange }: Props) => {
  const { map } = useAppContext();

  const handleDragEnd = async (event: DragEndEvent) => {
    if (event.target === null) return;
    const latlng = event.target.getLatLng();
    onChange({ lat: latlng.lat, lng: latlng.lng });
  };

  const center = position ?? map.defaultCenter;

  return (
    <Paper sx={{ width: 1, height: 1, overflow: 'hidden' }}>
      <MapContainer
        style={{ height: '100%', width: '100%' }}
        center={center}
        zoom={map.defaultZoom}
        zoomControl={false}
        scrollWheelZoom={false}
      >
        <GsiTileLayer />
        <ZoomControl position="bottomright" />
        <GsiMapPanToCenterEffect center={center} />
        <Marker position={center} draggable eventHandlers={{ dragend: handleDragEnd }} />
      </MapContainer>
    </Paper>
  );
};
