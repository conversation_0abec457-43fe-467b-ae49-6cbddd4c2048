import { type Control, type UseFormWatch, useController } from 'react-hook-form';

import { fetchAddress } from '@/utils';
import { EditPoint } from './edit-point/_EditLocation';
import { ReadPoint } from './read-point/_ReadPoint';

type Target = {
  location: google.maps.LatLngLiteral & {
    prefecture: string;
    city: string;
    town: string;
    furtherAddress: string;
    landmarkId: string;
  };
};
type CommonProps = { readOnly?: boolean };
type Props<T extends Target> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<Target>;
  watch: UseFormWatch<Target>;
};

export const RhfMapPointOnLocation = <T extends Target>(props: Props<T>) => {
  const { readOnly, control, watch } = props as unknown as InnerProps;

  const latCtrl = useController({ name: 'location.lat', control });
  const lngCtrl = useController({ name: 'location.lng', control });
  const prefectureCtrl = useController({ name: 'location.prefecture', control });
  const cityCtrl = useController({ name: 'location.city', control });
  const townCtrl = useController({ name: 'location.town', control });
  const furtherAddressCtrl = useController({ name: 'location.furtherAddress', control });

  const position = { lat: watch('location.lat'), lng: watch('location.lng') };
  if (readOnly) return <ReadPoint position={position} />;
  return (
    <EditPoint
      position={position}
      onChange={async (position) => {
        latCtrl.field.onChange(position.lat);
        lngCtrl.field.onChange(position.lng);
        const address = await fetchAddress(position);
        prefectureCtrl.field.onChange(address?.prefecture ?? '');
        cityCtrl.field.onChange(address?.city ?? '');
        townCtrl.field.onChange(address?.town ?? '');
        furtherAddressCtrl.field.onChange(address?.furtherAddress ?? '');
      }}
    />
  );
};
