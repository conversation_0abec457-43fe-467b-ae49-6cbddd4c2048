import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>mControl } from 'react-leaflet';

import { useAppContext } from '@/contexts/app-context';
import { Paper } from '@mui/material';
import { GsiMapPanToCenterEffect } from '../../gsi/GsiMapPanToCenterEffect';
import { GsiTileLayer } from '../../gsi/GsiTileLayer';

type Props = {
  position: google.maps.LatLngLiteral;
};

export const GsiReadPoint = ({ position }: Props) => {
  const { map } = useAppContext();
  return (
    <Paper sx={{ width: 1, height: 1, overflow: 'hidden' }}>
      <MapContainer
        style={{ height: '100%', width: '100%' }}
        center={position}
        zoom={map.defaultZoom}
        zoomControl={false}
        scrollWheelZoom={false}
        doubleClickZoom={false}
      >
        <GsiTileLayer />
        <ZoomControl position="bottomright" />
        <GsiMapPanToCenterEffect center={position} />
        <Marker position={position} />
      </MapContainer>
    </Paper>
  );
};
