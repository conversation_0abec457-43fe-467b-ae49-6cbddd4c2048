import { useMapType } from '@/stores/map';
import { Typography } from '@mui/material';
import { isNullish } from 'common';
import LostMap from '../../LostMap';
import { GoogleReadPoint } from './GoogleReadPoint';
import { GsiReadPoint } from './GsiReadPoint';

type Props = {
  position: google.maps.LatLngLiteral | null | undefined;
};

export const ReadPoint = ({ position }: Props) => {
  const mapType = useMapType();

  if (isNullish(position)) {
    return (
      <LostMap>
        <Typography color="text.secondary" sx={{ pb: 4 }}>
          位置情報がありません
        </Typography>
      </LostMap>
    );
  }

  if (mapType === 'google') return <GoogleReadPoint position={position} />;
  return <GsiReadPoint position={position} />;
};
