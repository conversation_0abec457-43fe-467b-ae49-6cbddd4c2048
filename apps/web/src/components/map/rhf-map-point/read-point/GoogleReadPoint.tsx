import { AdvancedMarker, Map as GoogleMap } from '@vis.gl/react-google-maps';

import { useAppContext } from '@/contexts/app-context';
import { Paper } from '@mui/material';
import GoogleMapContainer from '../../google/GoogleMapContainer';

type Props = {
  position: google.maps.LatLngLiteral;
};

const _GoogleReadPoint = ({ position }: Props) => {
  const { map } = useAppContext();
  return (
    <Paper sx={{ width: 1, height: 1, overflow: 'hidden' }}>
      <GoogleMap
        {...map}
        center={position}
        defaultCenter={position}
        gestureHandling="none"
        disableDefaultUI
        zoomControl
        scrollwheel={false}
      >
        <AdvancedMarker position={position} />
      </GoogleMap>
    </Paper>
  );
};

export const GoogleReadPoint = (props: Props) => {
  return (
    <GoogleMapContainer>
      <_GoogleReadPoint {...props} />
    </GoogleMapContainer>
  );
};
