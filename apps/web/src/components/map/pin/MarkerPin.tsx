import { markerStatusToColor, markerStatusToIcon } from '@/models/marker';
import type { BicycleMarkerStatus } from 'common';
import { PinBase } from './PinBase';

type Props = { status: BicycleMarkerStatus };

export const MarkerPin = ({ status }: Props) => {
  const color = markerStatusToColor(status);
  const Icon = markerStatusToIcon(status);
  return (
    <PinBase color={color[600]}>
      <Icon sx={{ zIndex: 1, fontSize: 16, color: '#fff', mb: '7px' }} />
    </PinBase>
  );
};
