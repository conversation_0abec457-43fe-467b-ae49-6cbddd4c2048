import { eventTypeToColor } from '@/models/bicycle';
import { PedalBike } from '@mui/icons-material';
import type { BicycleStatus } from 'common';
import { PinBase } from './PinBase';

type Props = { status: BicycleStatus };

export const BicyclePin = ({ status }: Props) => {
  const color = eventTypeToColor(status);
  return (
    <PinBase color={color[600]}>
      <PedalBike sx={{ zIndex: 1, fontSize: 16, color: '#fff', mb: '7px' }} />
    </PinBase>
  );
};
