import { useTheme } from '@/theme';
import { Box } from '@mui/material';

type Props = { color?: string; children?: React.JSX.Element };

export const PinBase = ({ color, children }: Props) => {
  const t = useTheme();
  return (
    <Box
      sx={{
        width: 40,
        height: 40,
        position: 'relative',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        style={{ position: 'absolute', left: 0, top: 0, width: '100%', height: '100%' }}
      >
        <title>icon</title>
        <path
          fill={color ?? t.palette.text.secondary}
          d="M12 2
          C 8.13  2     5      5.13  5   9
          c 0     5.25  7     13     7  13
          s 7    -7.75  7    -13
          c 0    -3.87 -3.13  -7    -7  -7"
        />
      </svg>
      {children}
    </Box>
  );
};
