import { Box } from '@mui/material';

type Props = { children: React.JSX.Element };
export default function LostMap({ children }: Props) {
  return (
    <Box
      sx={{
        width: 1,
        height: 1,
        display: 'grid',
        placeContent: 'center',
        textAlign: 'center',
        backgroundImage: `url('/undraw/lost_on_map.svg')`,
        backgroundColor: 'rgba(255,255,255,0.8)',
        backgroundBlendMode: 'lighten',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
    >
      {children}
    </Box>
  );
}
