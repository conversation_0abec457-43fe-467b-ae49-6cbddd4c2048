import { Close } from '@mui/icons-material';
import { Button, Dialog, Slide } from '@mui/material';

import { QrcodeScanController } from './QrcodeScanController';

type Props = {
  open: boolean;
  onClose: () => void;
  title: string;
  description: string;
  onScan: (text: string) => void;
};

export const QrcodeScanDialog = ({ open, onClose, title, description, onScan }: Props) => {
  const handleScan = async (text: string) => {
    onScan(text);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullScreen
      slots={{ transition: Slide }}
      slotProps={{ transition: { direction: 'up' } }}
    >
      <QrcodeScanController
        title={title}
        description={description}
        onScan={handleScan}
        BackButton={
          <Button variant="text" endIcon={<Close />} onClick={onClose}>
            キャンセル
          </Button>
        }
      />
    </Dialog>
  );
};
