import { Checkbox, Stack, Typography } from '@mui/material';
import { isNullish } from 'common';

type Props = {
  label: string;
  description: string;
  value: boolean | null | undefined;
};

export default function CheckboxField({ label, value, description }: Props) {
  return (
    <Stack
      sx={{
        borderBottomColor: (t) => t.palette.primary.main,
        borderBottomWidth: 1,
        borderBottomStyle: 'solid',
      }}
    >
      <Typography variant="caption" color="primary">
        {label}
      </Typography>
      <Stack direction="row" sx={{ pl: 0.5 }}>
        <Checkbox checked={Boolean(value)} indeterminate={isNullish(value)} />
        <Typography>{description}</Typography>
      </Stack>
    </Stack>
  );
}
