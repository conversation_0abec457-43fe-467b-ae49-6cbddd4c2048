import { Close } from '@mui/icons-material';
import { Dialog, IconButton, Slide, Tooltip } from '@mui/material';

import { type LocalImage, base64ToContentType, contentTypeToExt } from 'common';

import { CameraCapture } from '@/components/CameraCapture';
import { resizeToSquare } from 'mui-ex';

type Props = {
  open: boolean;
  onClose: () => void;
  filename: string;
  description: string;
  onTakePhoto: (image: LocalImage) => void;
};

export const TakePhotoDialog = ({ open, onClose, filename, description, onTakePhoto }: Props) => {
  const handleCapture = async (imageSrc: string) => {
    const base64 = await resizeToSquare(imageSrc);
    const contentType = base64ToContentType(base64);
    const ext = contentTypeToExt(contentType);
    onTakePhoto({ id: base64, filename: `${filename}.${ext}`, base64, description });
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullScreen
      slots={{ transition: Slide }}
      slotProps={{ transition: { direction: 'up' } }}
    >
      <CameraCapture
        description={description}
        onCapture={handleCapture}
        CloseButton={
          <Tooltip title="キャンセル">
            <IconButton onClick={onClose}>
              <Close sx={{ color: 'white' }} />
            </IconButton>
          </Tooltip>
        }
      />
    </Dialog>
  );
};
