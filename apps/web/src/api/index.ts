import { fetchAuthSession } from '@aws-amplify/auth';
import { QueryClient } from '@tanstack/react-query';
import { type CreateTRPCClientOptions, createTRPCClient, httpBatchLink } from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import superjson from 'superjson';

import type { AppRouter } from 'lambda-api/src/trpc';

import { BASE_URL } from '@/types/vite-env';

// 型をエクスポートする場合は以下のプラクティスを参照
// https://trpc.io/docs/client/react/infer-types

const options: CreateTRPCClientOptions<AppRouter> = {
  links: [
    httpBatchLink({
      url: import.meta.env.DEV ? `${BASE_URL}/v1` : '/v1',
      transformer: superjson,
      headers: async () => {
        const cognitoTokens = (await fetchAuthSession()).tokens;
        const token = cognitoTokens?.idToken?.toString();
        if (!token) throw new Error('Cognito Token is not found');
        return { Authorization: `Bearer ${token}` };
      },
    }),
  ],
};

export const trpc = createTRPCReact<AppRouter>();

export const trpcReactClient = trpc.createClient(options);

export const trpcClient = createTRPCClient(options);

export const queryClient = new QueryClient();
