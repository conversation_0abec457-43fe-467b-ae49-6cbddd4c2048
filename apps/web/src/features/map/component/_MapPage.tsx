import React from 'react';

import { Box, Fab } from '@mui/material';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { useFontSize } from '@/stores/font-size';
import { usePosition, usePositionEffect } from '@/stores/position';
import { NearMe } from '@mui/icons-material';
import { BicycleStatusSchema, parseValidLatLng, sortDate } from 'common';
import {
  FilterModelContext,
  type FilterModelContextValue,
  createFilter,
  useFilterModelState,
} from 'mui-ex';
import { match } from 'ts-pattern';

import type { Bicycles, Markers } from 'lambda-api';

import type { MapPageState } from '../types';
import { List } from './list/_List';
import { MapComponent } from './map-component/_MapComponent';
import { MapToolbar } from './map-toolbar/_MapToolbar';
import { useMapFilterColumns } from './useMapFilterColumns';

const markerToDate = (marker: Markers[number]) => marker.createdAt;
const bicycleToDate = (bicycle: Bicycles[number]) => {
  const last = bicycle.events.filter((e) => BicycleStatusSchema.safeParse(e.type).success).at(-1);
  if (last === undefined) throw new Error(`No status event found for bicycle: ${bicycle.id}`);
  return last.date;
};

export const MapPage = () => {
  const { map } = useAppContext();
  const fontSize = useFontSize();
  const { data: rawMarkers = [], isPending: isMarkerPending } = trpc.markers.list.useQuery();
  const { data: rawBicycles = [], isPending: isBicyclePending } = trpc.bicycles.list.useQuery();
  const isPending = isMarkerPending || isBicyclePending;
  const rawData = React.useMemo(() => [...rawMarkers, ...rawBicycles], [rawMarkers, rawBicycles]);

  const [state, setState] = React.useState<MapPageState>({});

  const columns = useMapFilterColumns();
  const { filterModel, setFilterModel, setFilterItems } = useFilterModelState({
    persistent: 'map',
  });
  const filterModelContextValue: FilterModelContextValue = React.useMemo(
    () => ({ rows: rawData, columns, filterModel, setFilterModel, setFilterItems }),
    [rawData, columns, filterModel, setFilterModel, setFilterItems],
  );
  const markers = rawMarkers.filter(createFilter(filterModel, columns));
  const bicycles = rawBicycles.filter(createFilter(filterModel, columns));

  const handleClickMarkerItem = (markerId: string | undefined) => {
    if (markerId === undefined) return setState((s) => ({ ...s, markerId: undefined }));
    const center = parseValidLatLng(markers.find((m) => m.id === markerId));
    if (!center) throw new Error(`Marker not found: ${markerId}`);
    setState(() => ({ markerId, center }));
  };
  const handleClickBicycleItem = (bicycleId: string | undefined) => {
    if (bicycleId === undefined) return setState((s) => ({ ...s, bicycleId: undefined }));
    const center = parseValidLatLng(bicycles.find((b) => b.id === bicycleId)?.location?.last);
    if (!center) throw new Error(`Bicycle not found: ${bicycleId}`);
    setState(() => ({ bicycleId, center }));
  };
  const handleReset = () => setState(() => ({}));
  const handleChangeCenter = (center: google.maps.LatLngLiteral) => setState(() => ({ center }));
  usePositionEffect(handleChangeCenter);
  const gps = usePosition();

  const marker = markers.find((m) => m.id === state.markerId);
  const bicycle = bicycles.find((b) => b.id === state.bicycleId);
  const data = React.useMemo(
    () =>
      [...markers, ...bicycles].sort((a, b) => {
        const dateA = a.model === 'BicycleMarker' ? markerToDate(a) : bicycleToDate(a);
        const dateB = b.model === 'BicycleMarker' ? markerToDate(b) : bicycleToDate(b);
        return sortDate('desc', dateA, dateB);
      }),
    [markers, bicycles],
  );

  const handleClickNearMe = () => setState(() => ({ center: gps ?? map.defaultCenter }));
  const nextCenter = parseValidLatLng(
    marker ?? bicycle?.location?.last ?? state.center ?? gps ?? map.defaultCenter,
  );
  if (nextCenter === undefined) throw new Error('center is undefined');

  const toolbarHeight = match(fontSize)
    .with(14, () => 72)
    .with(16, () => 76)
    .with(18, () => 80)
    .exhaustive();

  return (
    <FilterModelContext.Provider value={filterModelContextValue}>
      <MapToolbar toolbarHeight={toolbarHeight} rawData={rawData} />
      <Box sx={{ position: 'relative', width: 1, height: `calc(100% - ${toolbarHeight}px)` }}>
        <MapComponent
          data={data}
          gps={gps}
          center={nextCenter}
          state={state}
          onChangeCenter={handleChangeCenter}
          onClickMarkerItem={handleClickMarkerItem}
          onClickBicycleItem={handleClickBicycleItem}
          onReset={handleReset}
        />
        <List
          loading={isPending}
          data={data}
          state={state}
          onClickMarkerItem={handleClickMarkerItem}
          onClickBicycleItem={handleClickBicycleItem}
        />
        <Box
          sx={{ position: 'absolute', bottom: (t) => t.spacing(16), right: (t) => t.spacing(1) }}
        >
          <Fab onClick={handleClickNearMe}>
            <NearMe />
          </Fab>
        </Box>
      </Box>
    </FilterModelContext.Provider>
  );
};
