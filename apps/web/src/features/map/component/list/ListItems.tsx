import React from 'react';

import { Box } from '@mui/material';
import { useVirtualizer } from '@tanstack/react-virtual';

import type { Bicycles, Markers } from 'lambda-api';

import { useResizeObserver } from '@/hooks/useResizeObserver';
import { type FontSize, useFontSize } from '@/stores/font-size';
import type { MapPageState } from '../../types';
import { BicycleItem } from './BicycleItem';
import { MarkerItem } from './MarkerItem';

const itemHeightMap: Record<FontSize, number> = {
  14: 91,
  16: 98,
  18: 107,
};

type Props = {
  data: (Markers[number] | Bicycles[number])[];
  state: MapPageState;
  onClickMarkerItem: (id: string | undefined) => void;
  onClickBicycleItem: (id: string | undefined) => void;
};

export const ListItems = React.memo(
  ({ data, state, onClickMarkerItem, onClickBicycleItem }: Props) => {
    const fontSize = useFontSize();

    const { ref, elementRef, rect } = useResizeObserver<HTMLDivElement>();
    const rowVirtualizer = useVirtualizer({
      count: data.length,
      getScrollElement: () => elementRef.current,
      estimateSize: () => itemHeightMap[fontSize],
    });
    return (
      <Box
        ref={ref}
        sx={{
          height: (t) => `calc(100dvh - ${rect.y}px - ${t.spacing(1)})`,
          overflow: 'auto',
        }}
      >
        <div
          style={{
            height: `${rowVirtualizer.getTotalSize()}px`,
            width: '100%',
            position: 'relative',
          }}
        >
          {rowVirtualizer.getVirtualItems().map((virtualItem) => {
            const item = data[virtualItem.index];
            return (
              <div
                key={virtualItem.key}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: `${virtualItem.size}px`,
                  transform: `translateY(${virtualItem.start}px)`,
                }}
              >
                {item.model === 'BicycleMarker' ? (
                  <MarkerItem
                    key={item.id}
                    marker={item}
                    state={state}
                    onClickMarkerItem={onClickMarkerItem}
                  />
                ) : (
                  <BicycleItem
                    key={item.id}
                    bicycle={item}
                    state={state}
                    onClickBicycleItem={onClickBicycleItem}
                  />
                )}
              </div>
            );
          })}
        </div>
      </Box>
    );
  },
);
