import { type Theme, useMediaQuery } from '@mui/material';

import type { Bicycles, Markers } from 'lambda-api';

import { type FontSize, useFontSize } from '@/stores/font-size';
import type { MapPageState } from '../../types';
import { ListPanel } from './ListPanel';
import { ListPopover } from './ListPopover';

const widthMap: Record<FontSize, number> = {
  14: 340,
  16: 360,
  18: 380,
};

type Props = {
  loading: boolean;
  data: (Markers[number] | Bicycles[number])[];
  state: MapPageState;
  onClickMarkerItem: (id: string | undefined) => void;
  onClickBicycleItem: (id: string | undefined) => void;
};

export const List = ({ loading, data, state, onClickMarkerItem, onClickBicycleItem }: Props) => {
  const lgDown = useMediaQuery<Theme>((theme) => theme.breakpoints.down('lg'));
  const ListComponent = lgDown ? ListPopover : ListPanel;
  const fontSize = useFontSize();
  const width = widthMap[fontSize];
  return (
    <ListComponent
      width={width}
      loading={loading}
      data={data}
      state={state}
      onClickMarkerItem={onClickMarkerItem}
      onClickBicycleItem={onClickBicycleItem}
    />
  );
};
