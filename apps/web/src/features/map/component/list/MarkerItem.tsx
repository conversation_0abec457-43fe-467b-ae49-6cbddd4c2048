import { MarkerStatusChip } from '@/components/bicycles/MarkerStatusChip';
import { datetimeFormatter } from '@/funcs/date';
import { BASE_URL } from '@/types/vite-env';
import { ListItemButton, ListItemText, Stack, Typography } from '@mui/material';
import { ImageUtil } from 'common';
import type { Markers } from 'lambda-api';
import { toShortAddress } from 'models';
import type { MapPageState } from '../../types';
import { ListItemAvatar } from './ListItemAvatar';

type Props = {
  marker: Markers[number];
  state: MapPageState;
  onClickMarkerItem: (id: string | undefined) => void;
};

export const MarkerItem = ({ marker, state, onClickMarkerItem }: Props) => {
  const image = marker.images.at(0);
  const img = new ImageUtil(image);
  const address = toShortAddress(marker);
  const selected = marker.id === state.markerId;
  return (
    <ListItemButton
      alignItems="flex-start"
      selected={selected}
      onClick={() => onClickMarkerItem(selected ? undefined : marker.id)}
      sx={{ px: 1 }}
    >
      <ListItemAvatar src={img.src(BASE_URL)} />
      <ListItemText
        primary={
          <Stack sx={{ pb: 0.5 }}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <Typography noWrap sx={{ flexGrow: 1 }}>
                {marker.landmark?.name}
              </Typography>
              <MarkerStatusChip status={marker.status} size="small" />
            </Stack>
            <Typography variant="body2">{datetimeFormatter(marker.createdAt)}</Typography>
          </Stack>
        }
        secondary={address}
        secondaryTypographyProps={{ noWrap: true }}
      />
    </ListItemButton>
  );
};
