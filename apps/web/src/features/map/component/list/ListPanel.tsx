import React from 'react';

import { ExpandLess, ExpandMore, PushPin } from '@mui/icons-material';
import {
  <PERSON>lapse,
  IconButton,
  LinearProgress,
  List,
  ListSubheader,
  Paper,
  Stack,
  Tooltip,
  Typography,
} from '@mui/material';

import type { Bicycles, Markers } from 'lambda-api';
import { PinOff } from 'mui-ex';

import { useAppContext } from '@/contexts/app-context';
import type { MapPageState } from '../../types';
import { ListItems } from './ListItems';

type Props = {
  width: number;
  loading: boolean;
  data: (Markers[number] | Bicycles[number])[];
  state: MapPageState;
  onClickMarkerItem: (id: string | undefined) => void;
  onClickBicycleItem: (id: string | undefined) => void;
};

export const ListPanel = ({
  width,
  loading,
  data,
  state,
  onClickMarkerItem,
  onClickBicycleItem,
}: Props) => {
  const { labels } = useAppContext();
  const [innerOpen, setOpen] = React.useState(true);
  const [pin, setPin] = React.useState(false);

  const handleClickPin: React.MouseEventHandler<HTMLButtonElement> = React.useCallback(
    (e) => {
      e.stopPropagation();
      if (!pin) setOpen(true);
      setPin((o) => !o);
    },
    [pin],
  );
  const handleClickMarkerItem = React.useCallback(
    (id: string | undefined) => {
      if (!pin) setOpen((o) => !o);
      onClickMarkerItem(id);
    },
    [pin, onClickMarkerItem],
  );
  const handleClickBicycleItem = React.useCallback(
    (id: string | undefined) => {
      if (!pin) setOpen((o) => !o);
      onClickBicycleItem(id);
    },
    [pin, onClickBicycleItem],
  );
  const open = innerOpen && data.length > 0;
  const tooltipTitle = pin
    ? `${labels.domain.bicycle}を選択しても${labels.domain.bicycle}${labels.system.list}は表示したままです`
    : `${labels.domain.bicycle}を選択すると${labels.domain.bicycle}${labels.system.list}が閉じます`;
  return (
    <List
      component={Paper}
      dense
      sx={{
        position: 'absolute',
        left: (t) => t.spacing(2),
        top: (t) => t.spacing(1),
        width,
        p: loading ? 0 : undefined,
        pb: 0,
      }}
      subheader={
        <ListSubheader
          disableGutters
          sx={{ cursor: !loading ? 'pointer' : undefined }}
          onClick={() => !loading && setOpen((o) => !o)}
        >
          <Stack direction="row" alignItems="center" sx={{ pt: 1 }}>
            <Tooltip title={tooltipTitle}>
              <IconButton size="small" sx={{ mx: 1 }} onClick={handleClickPin}>
                {pin ? <PushPin fontSize="inherit" /> : <PinOff fontSize="inherit" />}
              </IconButton>
            </Tooltip>
            <Typography>{`${labels.domain.bicycle}${labels.system.list}`}</Typography>
            <IconButton sx={{ ml: 'auto', mr: 1 }} disabled={loading}>
              {!open ? <ExpandMore /> : <ExpandLess />}
            </IconButton>
          </Stack>
          {loading && <LinearProgress />}
        </ListSubheader>
      }
    >
      <Collapse in={open}>
        <ListItems
          data={data}
          state={state}
          onClickMarkerItem={handleClickMarkerItem}
          onClickBicycleItem={handleClickBicycleItem}
        />
      </Collapse>
    </List>
  );
};
