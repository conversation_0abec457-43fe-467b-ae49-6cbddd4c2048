import { type FontSize, useFontSize } from '@/stores/font-size';
import { Box, ListItemAvatar as MuiListItemAvatar } from '@mui/material';

const sizeMap = new Map<FontSize, number>([
  [14, 66],
  [16, 74],
  [18, 84],
]);

type Props = {
  src: string;
};

export const ListItemAvatar = ({ src }: Props) => {
  const fontSize = useFontSize();
  const size = sizeMap.get(fontSize) ?? 87;
  return (
    <MuiListItemAvatar sx={{ pr: 1 }}>
      <Box
        component="img"
        src={src}
        sx={{ width: size, height: size, borderRadius: 2, objectFit: 'cover' }}
      />
    </MuiListItemAvatar>
  );
};
