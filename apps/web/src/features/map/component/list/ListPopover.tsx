import React from 'react';

import { ArrowDropDown, ArrowDropUp } from '@mui/icons-material';
import { Box, Button, CircularProgress, List, Paper, Popover } from '@mui/material';

import type { Bicycles, Markers } from 'lambda-api';

import { useAppContext } from '@/contexts/app-context';
import { match } from 'ts-pattern';
import type { MapPageState } from '../../types';
import { ListItems } from './ListItems';

type Props = {
  width: number;
  loading: boolean;
  data: (Markers[number] | Bicycles[number])[];
  state: MapPageState;
  onClickMarkerItem: (id: string | undefined) => void;
  onClickBicycleItem: (id: string | undefined) => void;
};

export const ListPopover = ({
  width,
  loading,
  data,
  state,
  onClickMarkerItem,
  onClickBicycleItem,
}: Props) => {
  const { labels } = useAppContext();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const handleClick = (event: React.MouseEvent<HTMLElement>) =>
    setAnchorEl(anchorEl ? null : event.currentTarget);

  const open = Boolean(anchorEl);
  const id = open ? 'list-popper' : undefined;

  const handleClickMarkerItem = (id: string | undefined) => {
    setAnchorEl(null);
    onClickMarkerItem(id);
  };
  const handleClickBicycleItem = (id: string | undefined) => {
    setAnchorEl(null);
    onClickBicycleItem(id);
  };
  const endIcon = match({ loading, open })
    .with({ loading: true }, () => (
      <Box sx={{ width: 22, height: 22, display: 'grid', placeItems: 'center' }}>
        <CircularProgress size={20} />
      </Box>
    ))
    .with({ open: false }, () => <ArrowDropDown />)
    .with({ open: true }, () => <ArrowDropUp />)
    .exhaustive();

  return (
    <>
      <Paper
        sx={{
          position: 'absolute',
          left: (t) => t.spacing(2),
          top: (t) => t.spacing(1),
        }}
      >
        <Button
          variant="text"
          size="large"
          endIcon={endIcon}
          disabled={loading}
          onClick={handleClick}
          sx={{ px: 2 }}
        >
          {`${labels.domain.bicycle}${labels.system.list}`}
        </Button>
      </Paper>
      <Popover
        id={id}
        open={open}
        onClose={() => setAnchorEl(null)}
        anchorEl={anchorEl}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'left' }}
      >
        <List dense sx={{ width, p: 0 }}>
          <ListItems
            data={data}
            state={state}
            onClickMarkerItem={handleClickMarkerItem}
            onClickBicycleItem={handleClickBicycleItem}
          />
        </List>
      </Popover>
    </>
  );
};
//
