import { BicycleStatusChip } from '@/components/bicycles/BicycleStatusChip';
import { datetimeFormatter } from '@/funcs/date';
import { bicycleToImage } from '@/models/bicycle';
import { BASE_URL } from '@/types/vite-env';
import { ListItemButton, ListItemText, Stack, Typography } from '@mui/material';
import { BicycleStatusSchema, ImageUtil } from 'common';
import type { Bicycles } from 'lambda-api';
import { toShortAddress } from 'models';
import type { MapPageState } from '../../types';
import { ListItemAvatar } from './ListItemAvatar';

type Props = {
  bicycle: Bicycles[number];
  state: MapPageState;
  onClickBicycleItem: (id: string | undefined) => void;
};

export const BicycleItem = ({ bicycle, state, onClickBicycleItem }: Props) => {
  const image = bicycleToImage(bicycle);
  const img = new ImageUtil(image);
  const event = bicycle.events.find((e) => BicycleStatusSchema.safeParse(e.type).success);
  if (!event) return null;
  const address = toShortAddress(bicycle.location?.last);
  const selected = bicycle.id === state.bicycleId;
  return (
    <ListItemButton
      key={bicycle.id}
      alignItems="flex-start"
      selected={selected}
      onClick={() => onClickBicycleItem(selected ? undefined : bicycle.id)}
      sx={{ px: 1 }}
    >
      <ListItemAvatar src={img.src(BASE_URL)} />
      <ListItemText
        primary={
          <Stack sx={{ pb: 0.5 }}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <Typography noWrap sx={{ flexGrow: 1 }}>
                {bicycle.location?.last?.landmark?.name}
              </Typography>
              <BicycleStatusChip status={bicycle.status} size="small" />
            </Stack>
            <Typography variant="body2">{datetimeFormatter(event.date)}</Typography>
          </Stack>
        }
        secondary={address}
        secondaryTypographyProps={{ noWrap: true }}
      />
    </ListItemButton>
  );
};
