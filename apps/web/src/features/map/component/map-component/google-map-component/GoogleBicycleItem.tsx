import { AdvancedMarker, InfoWindow, useAdvancedMarkerRef } from '@vis.gl/react-google-maps';

import { BicyclePin } from '@/components/map/pin/BicyclePin';
import type { MapPageState } from '@/features/map/types';
import { parseValidLatLng } from 'common';
import type { Bicycles } from 'lambda-api';
import { BicycleItemInfoContent } from '../common/bicycle/BicycleItemInfoContent';
import { BicycleItemInfoHeader } from '../common/bicycle/BicycleItemInfoHeader';

type Props = {
  bicycle: Bicycles[number];
  state: MapPageState;
  onClickBicycleItem: (id: string | undefined) => void;
};
export const GoogleBicycleItem = ({ bicycle, state, onClickBicycleItem }: Props) => {
  const [markerRef, marker] = useAdvancedMarkerRef();
  const open = state.bicycleId === bicycle.id;
  return (
    <AdvancedMarker
      ref={markerRef}
      position={parseValidLatLng(bicycle.location?.last)}
      onClick={() => onClickBicycleItem(open ? undefined : bicycle.id)}
    >
      <BicyclePin status={bicycle.status} />
      {open && (
        <InfoWindow
          anchor={marker}
          onCloseClick={() => onClickBicycleItem(undefined)}
          headerContent={<BicycleItemInfoHeader bicycle={bicycle} />}
          disableAutoPan
        >
          <BicycleItemInfoContent bicycle={bicycle} />
        </InfoWindow>
      )}
    </AdvancedMarker>
  );
};
