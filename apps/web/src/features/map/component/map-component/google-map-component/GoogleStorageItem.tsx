import { AdvancedMarker, InfoWindow, useAdvancedMarkerRef } from '@vis.gl/react-google-maps';
import React from 'react';

import { parseValidLatLng } from 'common';
import type { Storage } from 'lambda-api';

import { StoragePin } from '@/components/map/pin/StoragePin';
import { StorageContent } from '../common/storage/StorageContent';
import { StorageHeader } from '../common/storage/StorageHeader';

type Props = { storage: Storage };

export const GoogleStorageItem = ({ storage }: Props) => {
  const [markerRef, marker] = useAdvancedMarkerRef();
  const [open, setOpen] = React.useState(false);
  const latlng = parseValidLatLng(storage);
  if (latlng === undefined) return null;
  return (
    <AdvancedMarker ref={markerRef} position={latlng} onClick={() => setOpen(!open)}>
      <StoragePin />
      {open && (
        <InfoWindow
          anchor={marker}
          onCloseClick={() => setOpen(false)}
          headerContent={<StorageHeader storage={storage} />}
          disableAutoPan
        >
          <StorageContent storage={storage} />
        </InfoWindow>
      )}
    </AdvancedMarker>
  );
};
