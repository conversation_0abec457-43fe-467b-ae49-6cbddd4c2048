import React from 'react';

import {
  AdvancedMarker,
  Map as GoogleMap,
  InfoWindow,
  type MapEvent,
  useAdvancedMarkerRef,
} from '@vis.gl/react-google-maps';

import GoogleMapContainer from '@/components/map/google/GoogleMapContainer';
import { useGoogleMapPanToCenterEffect } from '@/components/map/google/useGoogleMapPanToCenterEffect';
import { CurrentLocationPin } from '@/components/map/pin/CurrentLocationPin';
import { useAppContext } from '@/contexts/app-context';
import type { MapPageState } from '@/features/map/types';
import type { Bicycles, Markers } from 'lambda-api';
import { CurrentLocationContent } from '../common/current-location/CurrentLocationContent';
import { CurrentLocationHeader } from '../common/current-location/CurrentLocationHeader';
import { GoogleBicycleItem } from './GoogleBicycleItem';
import { GoogleLandmarkItem } from './GoogleLandmarkItem';
import { GoogleMarkerItem } from './GoogleMarkerItem';
import { GoogleStorageItem } from './GoogleStorageItem';

type Props = {
  data: (Markers[number] | Bicycles[number])[];
  gps: google.maps.LatLngLiteral | undefined;
  center: google.maps.LatLngLiteral;
  state: MapPageState;
  onChangeCenter: (position: google.maps.LatLngLiteral) => void;
  onClickMarkerItem: (id: string | undefined) => void;
  onClickBicycleItem: (id: string | undefined) => void;
  onReset: () => void;
};

const _GoogleMapComponent = ({
  data,
  gps,
  center,
  state,
  onChangeCenter,
  onClickMarkerItem,
  onClickBicycleItem,
  onReset,
}: Props) => {
  const { map, landmarks, storages } = useAppContext();
  const [id] = React.useState(crypto.randomUUID());

  const [dragging, setDragging] = React.useState(false);
  useGoogleMapPanToCenterEffect({ id, center, disabled: dragging });

  const [markerRef, marker] = useAdvancedMarkerRef();
  const [open, setOpen] = React.useState(false);

  const handleDragstart = () => {
    setDragging(true);
    onReset();
  };

  const handleDragend = (event: MapEvent) => {
    setDragging(false);
    const latlng = event.map.getCenter();
    if (latlng) onChangeCenter({ lat: latlng.lat(), lng: latlng.lng() });
  };

  return (
    <GoogleMap
      {...map}
      id={id}
      defaultCenter={center}
      onDragstart={handleDragstart}
      onDragend={handleDragend}
      disableDefaultUI
      zoomControl
    >
      <AdvancedMarker ref={markerRef} position={gps} onClick={() => setOpen((o) => !o)}>
        <CurrentLocationPin />
        {open && (
          <InfoWindow
            anchor={marker}
            onClose={() => setOpen(false)}
            headerContent={<CurrentLocationHeader />}
            disableAutoPan
          >
            <CurrentLocationContent />
          </InfoWindow>
        )}
      </AdvancedMarker>
      {data
        .filter((item) => {
          if (state.markerId === undefined && state.bicycleId === undefined) return true;
          if (state.markerId === item.id) return true;
          if (state.bicycleId === item.id) return true;
          return false;
        })
        .map((item) =>
          item.model === 'BicycleMarker' ? (
            <GoogleMarkerItem
              key={item.id}
              marker={item}
              state={state}
              onClickMarkerItem={onClickMarkerItem}
            />
          ) : (
            <GoogleBicycleItem
              key={item.id}
              bicycle={item}
              state={state}
              onClickBicycleItem={onClickBicycleItem}
            />
          ),
        )}
      {landmarks.map((landmark) => (
        <GoogleLandmarkItem key={landmark.id} landmark={landmark} />
      ))}
      {storages.map((storage) => (
        <GoogleStorageItem key={storage.id} storage={storage} />
      ))}
    </GoogleMap>
  );
};

export const GoogleMapComponent = (props: Props) => {
  return (
    <GoogleMapContainer>
      <_GoogleMapComponent {...props} />
    </GoogleMapContainer>
  );
};
