import { AdvancedMarker, InfoWindow, useAdvancedMarkerRef } from '@vis.gl/react-google-maps';

import { MarkerPin } from '@/components/map/pin/MarkerPin';
import type { MapPageState } from '@/features/map/types';
import { parseValidLatLng } from 'common';
import type { Markers } from 'lambda-api';
import { MarkerItemInfoContent } from '../common/marker/MarkerItemInfoContent';
import { MarkerItemInfoHeader } from '../common/marker/MarkerItemInfoHeader';

type Props = {
  marker: Markers[number];
  state: MapPageState;
  onClickMarkerItem: (id: string | undefined) => void;
};
export const GoogleMarkerItem = ({ marker, state, onClickMarkerItem }: Props) => {
  const [mapMarkerRef, mapMarker] = useAdvancedMarkerRef();
  const open = state.markerId === marker.id;
  return (
    <AdvancedMarker
      ref={mapMarkerRef}
      position={parseValidLatLng(marker)}
      onClick={() => onClickMarkerItem(open ? undefined : marker.id)}
    >
      <MarkerPin status={marker.status} />
      {open && (
        <InfoWindow
          anchor={mapMarker}
          onCloseClick={() => onClickMarkerItem(undefined)}
          headerContent={<MarkerItemInfoHeader marker={marker} />}
          disableAutoPan
        >
          <MarkerItemInfoContent marker={marker} />
        </InfoWindow>
      )}
    </AdvancedMarker>
  );
};
