import { AdvancedMarker, InfoWindow, useAdvancedMarkerRef } from '@vis.gl/react-google-maps';
import React from 'react';

import { parseValidLatLng } from 'common';
import type { Landmark } from 'lambda-api';

import { LandmarkPin } from '@/components/map/pin/LandmarkPin';
import { LandmarkContent } from '../common/landmark/LandmarkContent';
import { LandmarkHeader } from '../common/landmark/LandmarkHeader';

type Props = { landmark: Landmark };
export const GoogleLandmarkItem = ({ landmark }: Props) => {
  const [markerRef, marker] = useAdvancedMarkerRef();
  const [open, setOpen] = React.useState(false);
  const latlng = parseValidLatLng(landmark);
  if (latlng === undefined) return null;
  return (
    <AdvancedMarker ref={markerRef} position={latlng} onClick={() => setOpen(!open)}>
      <LandmarkPin />
      {open && (
        <InfoWindow
          anchor={marker}
          onCloseClick={() => setOpen(false)}
          headerContent={<LandmarkHeader landmark={landmark} />}
          disableAutoPan
        >
          <LandmarkContent landmark={landmark} />
        </InfoWindow>
      )}
    </AdvancedMarker>
  );
};
