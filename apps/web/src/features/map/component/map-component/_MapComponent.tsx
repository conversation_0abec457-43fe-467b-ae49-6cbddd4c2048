import { useMapType } from '@/stores/map';

import type { Bicycles, Markers } from 'lambda-api';
import type { MapPageState } from '../../types';
import { GoogleMapComponent } from './google-map-component/_GoogleMapComponent';
import { GsiMapComponent } from './gsi-map-component/_GsiMapComponent';

type Props = {
  data: (Markers[number] | Bicycles[number])[];
  gps: google.maps.LatLngLiteral | undefined;
  center: google.maps.LatLngLiteral;
  state: MapPageState;
  onChangeCenter: (position: google.maps.LatLngLiteral) => void;
  onClickMarkerItem: (id: string | undefined) => void;
  onClickBicycleItem: (id: string | undefined) => void;
  onReset: () => void;
};

export const MapComponent = (props: Props) => {
  const mapType = useMapType();
  if (mapType === 'google') return <GoogleMapComponent {...props} />;
  return <GsiMapComponent {...props} />;
};
