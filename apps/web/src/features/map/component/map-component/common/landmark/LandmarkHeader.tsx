import { Flag } from '@mui/icons-material';
import { Stack, Typography } from '@mui/material';
import type { Landmark } from 'lambda-api';

type Props = { landmark: Landmark };

export const LandmarkHeader = ({ landmark }: Props) => {
  return (
    <Stack direction="row" alignItems="center" spacing={1} sx={{ pb: 0.5 }}>
      <Flag />
      <Typography>{landmark.name}</Typography>
    </Stack>
  );
};
