import { BicycleStatusChip } from '@/components/bicycles/BicycleStatusChip';
import { simpleDatetimeFormatter } from '@/funcs/date';

import { Box, Stack, Typography } from '@mui/material';
import type { Bicycles } from 'lambda-api';

type Props = { bicycle: Bicycles[number] };

export const BicycleItemInfoHeader = ({ bicycle }: Props) => {
  const event = bicycle.events[0];

  return (
    <Stack direction="row" alignItems="center" spacing={1} sx={{ pb: 0.5 }}>
      <Typography>{bicycle.location?.last?.landmark?.name}</Typography>
      <Typography variant="body2" sx={{ ml: 1, color: 'text.primary', display: 'inline' }}>
        {simpleDatetimeFormatter(event.date)}
      </Typography>
      <Box sx={{ flexGrow: 1 }} />
      <BicycleStatusChip status={bicycle.status} size="small" />
    </Stack>
  );
};
