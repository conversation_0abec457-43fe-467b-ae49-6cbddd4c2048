import { Stack, Typography } from '@mui/material';

import type { Storage } from 'lambda-api';

import { Thumbnails } from '@/components/images/Thumbnails';

type Props = { storage: Storage };

export const StorageContent = ({ storage }: Props) => {
  return (
    <Stack spacing={1}>
      <Typography>{storage.address}</Typography>
      <Thumbnails images={storage.images} cols={3} />
    </Stack>
  );
};
