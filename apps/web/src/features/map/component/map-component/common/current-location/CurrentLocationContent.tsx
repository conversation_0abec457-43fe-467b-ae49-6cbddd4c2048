import { RouterButton } from '@/components/RouterButton';
import { useAppContext } from '@/contexts/app-context';
import { Stack } from '@mui/material';

export const CurrentLocationContent = () => {
  const { tenant, labels } = useAppContext();
  return (
    <Stack spacing={2}>
      {tenant.patrol.flow === 'find' && (
        <RouterButton to="/bicycles/find">
          {`${labels.domain.bicycle}を${labels.ed.find}`}
        </RouterButton>
      )}
      {tenant.patrol.flow === 'mark' && (
        <RouterButton to="/bicycles/mark">
          {`${labels.domain.map}に${labels.domain.marker}を${labels.doing.create}`}
        </RouterButton>
      )}
      {tenant.patrol.flow !== 'find' && (
        <RouterButton to="/bicycles/remove">
          {`${labels.domain.bicycle}を${labels.ed.remove}`}
        </RouterButton>
      )}
    </Stack>
  );
};
