import { Warehouse } from '@mui/icons-material';
import { Stack, Typography } from '@mui/material';
import type { Storage } from 'lambda-api';

type Props = { storage: Storage };

export const StorageHeader = ({ storage }: Props) => {
  return (
    <Stack direction="row" alignItems="center" spacing={1} sx={{ pb: 0.5 }}>
      <Warehouse />
      <Typography>{storage.name}</Typography>
    </Stack>
  );
};
