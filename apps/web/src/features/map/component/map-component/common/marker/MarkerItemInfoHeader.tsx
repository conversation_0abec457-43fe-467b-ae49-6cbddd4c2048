import { MarkerStatusChip } from '@/components/bicycles/MarkerStatusChip';
import { simpleDatetimeFormatter } from '@/funcs/date';
import { Box, Stack, Typography } from '@mui/material';
import type { Markers } from 'lambda-api';

type Props = { marker: Markers[number] };
export const MarkerItemInfoHeader = ({ marker }: Props) => {
  return (
    <Stack direction="row" alignItems="center" spacing={1} sx={{ pb: 0.5 }}>
      <Typography>{marker.landmark?.name}</Typography>
      <Typography variant="body2" sx={{ ml: 1, color: 'text.primary', display: 'inline' }}>
        {simpleDatetimeFormatter(marker.createdAt)}
      </Typography>
      <Box sx={{ flexGrow: 1 }} />
      <MarkerStatusChip status={marker.status} size="small" />
    </Stack>
  );
};
