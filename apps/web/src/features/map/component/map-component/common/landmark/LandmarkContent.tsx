import { Stack, Typography } from '@mui/material';

import type { Landmark } from 'lambda-api';

import { Thumbnails } from '@/components/images/Thumbnails';

type Props = { landmark: Landmark };

export const LandmarkContent = ({ landmark }: Props) => {
  return (
    <Stack spacing={1}>
      <Typography>{landmark.address}</Typography>
      <Thumbnails images={landmark.images} cols={3} />
    </Stack>
  );
};
