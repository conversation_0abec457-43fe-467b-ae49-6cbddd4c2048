import { BicycleMarkerStatusSchema } from 'common';

import { BicycleThumbnails } from '@/components/bicycles/images/BicycleThumbnails';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { MenuItem, Stack, TextField } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import type { Markers } from 'lambda-api';

type Props = { marker: Markers[number] };

export const MarkerItemInfoContent = ({ marker }: Props) => {
  const { labels } = useAppContext();

  const queryClient = useQueryClient();
  const { mutate } = trpc.markers.updateStatus.useMutation({
    onSuccess: () => queryClient.invalidateQueries({ queryKey: getQueryKey(trpc.markers.list) }),
  });
  const handleChangeStatus = (status: string) =>
    mutate({ id: marker.id, status: BicycleMarkerStatusSchema.parse(status) });

  return (
    <Stack spacing={1}>
      <BicycleThumbnails images={marker.images} cols={3} />
      <TextField
        select
        label="ステータス"
        value={marker.status}
        onChange={(e) => handleChangeStatus(e.target.value)}
      >
        {BicycleMarkerStatusSchema.options.map((option) => {
          return (
            <MenuItem key={option} value={option}>
              {labels.markerStatus[option]}
            </MenuItem>
          );
        })}
      </TextField>
    </Stack>
  );
};
