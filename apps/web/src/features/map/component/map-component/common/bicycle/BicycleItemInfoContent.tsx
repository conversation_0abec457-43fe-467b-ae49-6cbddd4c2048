import { UsedQrcodeActions } from '@/components/bicycles/UsedQrcodeActions';
import { BicycleThumbnails } from '@/components/bicycles/images/BicycleThumbnails';

import { Stack } from '@mui/material';
import { isAfter } from 'date-fns';
import type { Bicycles } from 'lambda-api';

type Props = { bicycle: Bicycles[number] };

export const BicycleItemInfoContent = ({ bicycle }: Props) => {
  const images = bicycle.events
    .slice()
    .sort((a, b) => (isAfter(new Date(a.date), new Date(b.date)) ? -1 : 1))
    .flatMap((event) => event.images.map((image) => ({ ...image, event })))
    .slice(0, 3);

  return (
    <Stack spacing={1}>
      <BicycleThumbnails images={images} cols={3} />
      <UsedQrcodeActions bicycle={bicycle} />
    </Stack>
  );
};
