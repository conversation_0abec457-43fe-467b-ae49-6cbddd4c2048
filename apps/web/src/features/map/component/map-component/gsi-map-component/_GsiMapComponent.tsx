import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ZoomControl } from 'react-leaflet';

import { GsiCurrentLocationMarker } from '@/components/map/gsi/GsiCurrentLocationMarker';
import { GsiEventListener } from '@/components/map/gsi/GsiEventListener';
import { GsiMapPanToCenterEffect } from '@/components/map/gsi/GsiMapPanToCenterEffect';
import { GsiTileLayer } from '@/components/map/gsi/GsiTileLayer';
import { useAppContext } from '@/contexts/app-context';
import type { MapPageState } from '@/features/map/types';
import { Stack } from '@mui/material';
import type { Bicycles, Markers } from 'lambda-api';
import type { Map as LeafletMap } from 'leaflet';
import { CurrentLocationContent } from '../common/current-location/CurrentLocationContent';
import { CurrentLocationHeader } from '../common/current-location/CurrentLocationHeader';
import { GsiBicycleItem } from './GsiBicycleItem';
import { GsiMarkerItem } from './GsiMarkerItem';

type Props = {
  data: (Markers[number] | Bicycles[number])[];
  gps: google.maps.LatLngLiteral | undefined;
  center: google.maps.LatLngLiteral;
  state: MapPageState;
  onChangeCenter: (position: google.maps.LatLngLiteral) => void;
  onClickMarkerItem: (id: string | undefined) => void;
  onClickBicycleItem: (id: string | undefined) => void;
  onReset: () => void;
};

export const GsiMapComponent = ({
  data,
  center,
  gps,
  state,
  onChangeCenter,
  onClickMarkerItem,
  onClickBicycleItem,
  onReset,
}: Props) => {
  const { map } = useAppContext();

  const handleDragStart = (map: LeafletMap) => {
    map.closePopup();
  };

  const handleDragEnd = (map: LeafletMap) => {
    const latlng = map.getCenter();
    onChangeCenter({ lat: latlng.lat, lng: latlng.lng });
  };
  return (
    <>
      <MapContainer
        style={{ height: '100%', width: '100%', zIndex: 0 }}
        center={center}
        zoom={map.defaultZoom}
        zoomControl={false}
        doubleClickZoom={false}
      >
        <GsiTileLayer />
        <ZoomControl position="bottomright" />
        <GsiMapPanToCenterEffect center={center} />
        <GsiEventListener
          popupclose={onReset}
          dragstart={handleDragStart}
          dragend={handleDragEnd}
        />
        {gps && (
          <GsiCurrentLocationMarker position={gps}>
            <Popup>
              <Stack spacing={1}>
                <CurrentLocationHeader />
                <CurrentLocationContent />
              </Stack>
            </Popup>
          </GsiCurrentLocationMarker>
        )}
        {data
          .filter((item) => {
            if (state.markerId === undefined && state.bicycleId === undefined) return true;
            if (state.markerId === item.id) return true;
            if (state.bicycleId === item.id) return true;
            return false;
          })
          .map((item) =>
            item.model === 'BicycleMarker' ? (
              <GsiMarkerItem
                key={item.id}
                marker={item}
                state={state}
                onClickMarkerItem={onClickMarkerItem}
              />
            ) : (
              <GsiBicycleItem
                key={item.id}
                bicycle={item}
                state={state}
                onClickBicycleItem={onClickBicycleItem}
              />
            ),
          )}
      </MapContainer>
    </>
  );
};
