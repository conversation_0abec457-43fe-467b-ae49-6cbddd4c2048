import { parseValidLatLng } from 'common';

import { usePopupSync } from '@/components/map/gsi/usePopupSync';
import type { MapPageState } from '@/features/map/types';
import type { Bicycles } from 'lambda-api';
import { icon } from 'leaflet';
import { Marker, Popup } from 'react-leaflet';
import { BicycleItemInfoContent } from '../common/bicycle/BicycleItemInfoContent';
import { BicycleItemInfoHeader } from '../common/bicycle/BicycleItemInfoHeader';

type Props = {
  bicycle: Bicycles[number];
  state: MapPageState;
  onClickBicycleItem: (id: string | undefined) => void;
};

export const GsiBicycleItem = ({ bicycle, state, onClickBicycleItem }: Props) => {
  const ref = usePopupSync(state.bicycleId === bicycle.id);

  const latlng = parseValidLatLng(bicycle.location?.last);
  if (!latlng) return null;
  const open = state.bicycleId === bicycle.id;

  return (
    <Marker
      icon={icon({
        iconUrl: '/img/map/leaflet/marker-icon.png',
        iconSize: [24, 40],
        iconAnchor: [12, 40],
        popupAnchor: [0, -36],
      })}
      position={latlng}
      eventHandlers={{
        click: () => onClickBicycleItem(!open ? bicycle.id : undefined),
      }}
    >
      <Popup ref={ref}>
        <BicycleItemInfoHeader bicycle={bicycle} />
        <BicycleItemInfoContent bicycle={bicycle} />
      </Popup>
    </Marker>
  );
};
