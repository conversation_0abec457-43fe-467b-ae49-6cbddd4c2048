import { parseValidLatLng } from 'common';

import { Marker, Popup } from 'react-leaflet';

import { usePopupSync } from '@/components/map/gsi/usePopupSync';
import type { MapPageState } from '@/features/map/types';
import type { Markers } from 'lambda-api';
import { icon } from 'leaflet';
import { MarkerItemInfoContent } from '../common/marker/MarkerItemInfoContent';
import { MarkerItemInfoHeader } from '../common/marker/MarkerItemInfoHeader';

type Props = {
  marker: Markers[number];
  state: MapPageState;
  onClickMarkerItem: (id: string | undefined) => void;
};

export const GsiMarkerItem = ({ marker, state, onClickMarkerItem }: Props) => {
  const ref = usePopupSync(state.markerId === marker.id);

  const latlng = parseValidLatLng(marker);
  if (!latlng) return null;
  const open = state.markerId === marker.id;

  return (
    <Marker
      icon={icon({
        iconUrl: '/img/map/leaflet/marker-icon.png',
        iconSize: [24, 40],
        iconAnchor: [12, 40],
        popupAnchor: [0, -36],
      })}
      position={latlng}
      eventHandlers={{
        click: () => onClickMarkerItem(!open ? marker.id : undefined),
      }}
    >
      <Popup ref={ref}>
        <MarkerItemInfoHeader marker={marker} />
        <MarkerItemInfoContent marker={marker} />
      </Popup>
    </Marker>
  );
};
