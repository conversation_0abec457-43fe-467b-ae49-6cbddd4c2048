import React from 'react';

import { FormControlLabel, Switch } from '@mui/material';
import type { GridFilterItem } from '@mui/x-data-grid';

import { isDate } from 'common';
import { startOfDay } from 'date-fns';
import type { Bicycles, Markers } from 'lambda-api';
import { useFilterModelContext } from 'mui-ex';

const field = 'createdAt';
const operator = 'is';
const isToday = (now: Date) => (item: GridFilterItem) => {
  if (item.field !== field) return false;
  if (item.operator !== operator) return false;
  if (!isDate(item.value)) return false;
  return item.value.getTime() === now.getTime();
};

type Props = {
  rawData: (Markers[number] | Bicycles[number])[];
};

export const TodaySwitch = ({ rawData }: Props) => {
  const {
    filterModel: { items },
    setFilterItems,
  } = useFilterModelContext();
  const [now] = React.useState(startOfDay(new Date()));

  const open = items.some(isToday(now));

  const handleChange = (_: unknown, checked: boolean) => {
    if (checked) return setFilterItems([...items, { field, operator, value: now }]);
    setFilterItems(items.filter((item) => !isToday(now)(item)));
  };
  const count = rawData.filter(
    (d) => startOfDay(new Date(d.createdAt)).getTime() === now.getTime(),
  ).length;

  return (
    <FormControlLabel
      control={<Switch checked={open} onChange={handleChange} />}
      label={`本日のみ (${count})`}
    />
  );
};
