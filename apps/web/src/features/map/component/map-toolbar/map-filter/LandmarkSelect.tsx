import { useAppContext } from '@/contexts/app-context';
import { Checkbox, FormControl, FormControlLabel, FormLabel } from '@mui/material';
import type { GridFilterItem } from '@mui/x-data-grid';
import type { Bicycles, Markers } from 'lambda-api';
import { isAnyOfOperator, useFilterModelContext } from 'mui-ex';
import { landmarkValueGetter } from '../../useMapFilterColumns';

const field = 'landmark';
const operator = isAnyOfOperator;
const isLandmarkFilterItem = (item: GridFilterItem) => {
  if (item.field !== field) return false;
  if (item.operator !== operator) return false;
  return true;
};

type Props = {
  rawData: (Markers[number] | Bicycles[number])[];
};

export const LandmarkSelect = ({ rawData }: Props) => {
  const { landmarks, labels } = useAppContext();
  const {
    filterModel: { items },
    setFilterItems,
  } = useFilterModelContext();

  const options = landmarks.map(({ name }) => name);

  const value: string[] = items.find(isLandmarkFilterItem)?.value ?? [];

  const handleChange = (landmarkName: string) => (_: unknown, checked: boolean) => {
    const found = items.find((item) => isLandmarkFilterItem(item));
    if (found === undefined)
      return setFilterItems([...items, { field, operator, value: [landmarkName] }]);

    const value: string[] = checked
      ? [...found.value, landmarkName]
      : found.value.filter((v: string) => v !== landmarkName);
    if (value.length === 0) {
      setFilterItems(items.filter((item) => !isLandmarkFilterItem(item)));
    } else {
      setFilterItems(items.map((item) => (isLandmarkFilterItem(item) ? { ...item, value } : item)));
    }
  };

  return (
    <FormControl>
      <FormLabel>{labels.domain.landmark}</FormLabel>
      {options.map((option) => {
        const checked = value.includes(option);
        const count = rawData.filter((item) => landmarkValueGetter(item) === option).length;
        return (
          <FormControlLabel
            key={option}
            control={<Checkbox checked={checked} onChange={handleChange(option)} />}
            label={`${option} (${count})`}
          />
        );
      })}
    </FormControl>
  );
};
