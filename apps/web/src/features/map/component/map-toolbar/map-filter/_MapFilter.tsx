import { FilterList } from '@mui/icons-material';
import { Box, Button, Grid, Paper, Popover, Tab, Tabs } from '@mui/material';
import { useFilterModelContext } from 'mui-ex';
import FilterPanel from 'mui-ex/src/grid-filter/filter-panel/FilterPanel';
import React from 'react';

import { useAppContext } from '@/contexts/app-context';
import type { Bicycles, Markers } from 'lambda-api';
import { LandmarkSelect } from './LandmarkSelect';
import { StatusSelect } from './StatusSelect';
import { TodaySwitch } from './TodaySwitch';

type FilterType = 'quick' | 'full';

type Props = {
  rawData: (Markers[number] | Bicycles[number])[];
};

export const MapFilter = ({ rawData }: Props) => {
  const { tenant } = useAppContext();
  const { columns } = useFilterModelContext();

  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) =>
    setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);

  const [filterType, setFilterType] = React.useState<FilterType>('quick');
  const tabValue = filterType === 'quick' ? 0 : 1;
  const handleChangeTab = (_: React.SyntheticEvent, value: 0 | 1) =>
    setFilterType(value === 0 ? 'quick' : 'full');

  const open = Boolean(anchorEl);

  return (
    <>
      <Button variant="text" endIcon={<FilterList />} onClick={handleClick} sx={{ ml: 'auto' }}>
        フィルター
      </Button>
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Paper sx={{ minWidth: { xs: 400, md: 660 } }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              variant="fullWidth"
              value={tabValue}
              onChange={handleChangeTab}
              sx={{ display: { xs: 'none', md: 'flex' } }}
            >
              <Tab label="クイック・フィルター" />
              <Tab label="高度なフィルター" />
            </Tabs>
          </Box>
          {filterType === 'quick' && (
            <Grid container spacing={2} sx={{ p: 1 }}>
              <Grid size={{ xs: 12 }}>
                <TodaySwitch rawData={rawData} />
              </Grid>
              <Grid size={{ xs: 12, md: 6 }}>
                <LandmarkSelect rawData={rawData} />
              </Grid>
              {tenant.patrol.flow !== 'noPatrol' && (
                <Grid size={{ xs: 12, md: 6 }}>
                  <StatusSelect rawData={rawData} />
                </Grid>
              )}
            </Grid>
          )}
          {filterType === 'full' && <FilterPanel columns={columns} />}
        </Paper>
      </Popover>
    </>
  );
};
