import { useAppContext } from '@/contexts/app-context';
import { Checkbox, FormControl, FormControlLabel, FormLabel } from '@mui/material';
import type { GridFilterItem } from '@mui/x-data-grid';
import type { Bicycles, Markers } from 'lambda-api';
import { isAnyOfOperator, useFilterModelContext } from 'mui-ex';
import { match } from 'ts-pattern';
import { statusValueGetter } from '../../useMapFilterColumns';

const field = 'status';
const operator = isAnyOfOperator;
const isStatus = (item: GridFilterItem) => {
  if (item.field !== field) return false;
  if (item.operator !== operator) return false;
  return true;
};

type Props = {
  rawData: (Markers[number] | Bicycles[number])[];
};

export const StatusSelect = ({ rawData }: Props) => {
  const {
    tenant: { patrol, ensureAbandoned },
    labels,
  } = useAppContext();
  const {
    filterModel: { items },
    setFilterItems,
  } = useFilterModelContext();

  if (patrol.flow === 'noPatrol') throw new Error();

  const options: string[] = match({
    patrol: patrol.flow,
    allowEnsureAbandoned: ensureAbandoned.allow,
  })
    .with({ patrol: 'mark' }, () => [labels.markerStatus.todo, labels.markerStatus.done])
    .with({ patrol: 'find', allowEnsureAbandoned: true }, () => [
      labels.et.find,
      labels.et.ensureAbandoned,
      labels.et.remove,
    ])
    .with({ patrol: 'find', allowEnsureAbandoned: false }, () => [labels.et.find, labels.et.remove])
    .exhaustive();

  const value: string[] = items.find(isStatus)?.value ?? [];

  const handleChange = (status: string) => (_: unknown, checked: boolean) => {
    const found = items.find((item) => isStatus(item));
    if (found === undefined)
      return setFilterItems([...items, { field, operator, value: [status] }]);

    const value: string[] = checked
      ? [...found.value, status]
      : found.value.filter((v: string) => v !== status);
    if (value.length === 0) {
      setFilterItems(items.filter((item) => !isStatus(item)));
    } else {
      setFilterItems(items.map((item) => (isStatus(item) ? { ...item, value } : item)));
    }
  };

  return (
    <FormControl>
      <FormLabel>{labels.system.status}</FormLabel>
      {options.map((option) => {
        const checked = value.includes(option);
        const count = rawData.filter((item) => statusValueGetter(item, labels) === option).length;
        return (
          <FormControlLabel
            key={option}
            control={<Checkbox checked={checked} onChange={handleChange(option)} />}
            label={`${option} (${count})`}
          />
        );
      })}
    </FormControl>
  );
};
