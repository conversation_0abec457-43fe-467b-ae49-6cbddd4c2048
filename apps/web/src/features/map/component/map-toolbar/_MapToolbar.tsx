import { useTheme } from '@/theme';
import { Box, ThemeProvider, Toolbar } from '@mui/material';
import type { Bicycles, Markers } from 'lambda-api';
import { MapFilter } from './map-filter/_MapFilter';

type Props = {
  toolbarHeight: number;
  rawData: (Markers[number] | Bicycles[number])[];
};

export const MapToolbar = ({ toolbarHeight, rawData }: Props) => {
  const t = useTheme();

  return (
    <ThemeProvider
      theme={{
        ...t,
        components: { ...t.components, MuiButton: { defaultProps: { variant: 'text' } } },
      }}
    >
      <Toolbar sx={{ height: toolbarHeight, pt: 2, pb: 1, gap: 2 }}>
        <Box sx={{ flexGrow: 1 }} />
        <MapFilter rawData={rawData} />
      </Toolbar>
    </ThemeProvider>
  );
};
