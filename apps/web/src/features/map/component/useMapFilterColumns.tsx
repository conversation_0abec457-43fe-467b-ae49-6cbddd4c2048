import type { GridColDef } from '@mui/x-data-grid';

import { useAppContext } from '@/contexts/app-context';

import type { BicycleBody, BicycleLocation, Bicycles, Markers } from 'lambda-api';
import type { Dict } from 'models';
import { formatColumns } from 'mui-ex';

type ColDef = GridColDef<Bicycles[number] | Markers[number]> & {
  field: keyof Bicycles[number] | keyof BicycleLocation | `body.${keyof BicycleBody}` | 'images';
};

export const statusValueGetter = (item: Bicycles[number] | Markers[number], labels: Dict) => {
  if (item.model === 'BicycleMarker') return labels.markerStatus[item.status];
  return labels.et[item.status];
};

export const landmarkValueGetter = (item: Bicycles[number] | Markers[number]) => {
  if (item.model === 'BicycleMarker') return item.landmark?.name;
  return item.location?.last?.landmark?.name;
};

export const useMapFilterColumns = () => {
  const { labels, bicycleTypes } = useAppContext();

  /** 日時 */
  const createdAtCol: ColDef = {
    field: 'createdAt',
    headerName: labels.p.createdAt,
    type: 'date',
    valueGetter: (_, row) => new Date(row.createdAt),
  };

  /** ステータス */
  const statusCol: ColDef = {
    field: 'status',
    headerName: labels.system.status,
    valueGetter: (_, row) => statusValueGetter(row, labels),
  };

  /* ランドマーク */
  const landmarkCol: ColDef = {
    field: 'landmark',
    headerName: labels.domain.landmark,
    valueGetter: (_, row) => landmarkValueGetter(row),
  };
  /* 車両区分 */
  const cycleTypeCol: ColDef = {
    field: 'type',
    headerName: labels.body.type,
    valueGetter: (_, row) => {
      if (row.model === 'BicycleMarker') return null;
      return bicycleTypes.find((cycle) => cycle.type === row.type)?.name;
    },
  };
  /* 防犯登録番号 */
  const registrationNumberCol: ColDef = {
    field: 'body.registrationNumber',
    headerName: labels.body.registrationNumber,
    valueGetter: (_, row) => {
      if (row.model === 'BicycleMarker') return null;
      return row.body?.last?.registrationNumber;
    },
  };
  /* ナンバープレートのナンバー */
  const licensePlateNumberCol: ColDef = {
    field: 'body.numberPlate',
    headerName: labels.body.numberPlate,
    valueGetter: (_, row) => {
      if (row.model === 'BicycleMarker') return null;
      return row.body?.last?.numberPlate;
    },
  };
  /* 放置禁止区域 */
  const isNoParkingAreaCol: ColDef = {
    field: 'isNoParkingArea',
    headerName: labels.domain.noParkingArea,
    type: 'boolean',
    valueGetter: (_, row) => {
      if (row.model === 'BicycleMarker') return row.isNoParkingArea;
      return row.location?.last?.isNoParkingArea;
    },
  };
  /* かごの有無 */
  const hasBasketCol: ColDef = {
    field: 'body.hasBasket',
    headerName: labels.p.hasBasket,
    type: 'boolean',
    valueGetter: (_, row) => {
      if (row.model === 'BicycleMarker') return null;
      return row.body?.last?.hasBasket;
    },
  };
  /* 施錠の有無 */
  const isLockedCol: ColDef = {
    field: 'body.isLocked',
    headerName: labels.p.isLocked,
    type: 'boolean',
    valueGetter: (_, row) => {
      if (row.model === 'BicycleMarker') return null;
      return row.body?.last?.isLocked;
    },
  };
  /* 色 */
  const colorCol: ColDef = {
    field: 'body.colors',
    headerName: '色',
    valueGetter: (_, row) => {
      if (row.model === 'BicycleMarker') return null;
      return row.body?.last?.colors?.join('、');
    },
  };
  /* 車両状態 */
  const conditionCol: ColDef = {
    field: 'body.conditions',
    headerName: labels.body.conditions,
  };

  return formatColumns([
    createdAtCol,
    statusCol,
    landmarkCol,
    cycleTypeCol,
    registrationNumberCol,
    licensePlateNumberCol,
    isNoParkingAreaCol,
    hasBasketCol,
    isLockedCol,
    colorCol,
    conditionCol,
  ]);
};
