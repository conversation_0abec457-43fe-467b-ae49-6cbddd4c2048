import { Box } from '@mui/material';

import SPECIAL_SMALL_BIKE_ICON from '@/assets/img/map/special-small-bike.svg';

type Props = {
  borderColor: string;
};

export default function SpecialSmallBikeIconMarker({ borderColor }: Props) {
  const markerSize = 60;

  return (
    <Box
      sx={{
        width: markerSize,
        height: markerSize,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        border: `4px solid ${borderColor}`,
        backgroundColor: '#FFF',
        borderRadius: '50% 50% 50% 0',
        transform: 'rotate(-45deg)',
        boxShadow: '0px 4px 6px rgba(0,0,0,0.3)',
      }}
    >
      <Box
        component="img"
        src={SPECIAL_SMALL_BIKE_ICON}
        sx={{
          width: '70%',
          height: '70%',
          objectFit: 'cover',
          transform: 'rotate(45deg)',
        }}
        alt="Special Small Bicycle Marker"
      />
    </Box>
  );
}
