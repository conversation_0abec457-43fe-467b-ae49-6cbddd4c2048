import { Box, Card, CardActionArea, CardMedia, Chip } from '@mui/material';

import { parseS3Key } from 'common';

import NO_IMAGE from '@/assets/img/no-image-placeholder.svg';
import { BASE_URL } from '@/types/vite-env';
import { enableUrl } from 'models';

type Props = {
  imageUrls: string[];
};

export default function InfoWindowThumbnails({ imageUrls }: Props) {
  const maxThumbnails = 3;
  const isNoImages = imageUrls.length === 0;
  const showHelperText = imageUrls.length > maxThumbnails;
  return (
    <Box sx={{ mt: 1, position: 'relative', display: 'flex', justifyContent: 'start', gap: 1 }}>
      {imageUrls.slice(0, maxThumbnails).map((url) => (
        <Card key={url} elevation={0} sx={{ width: `calc(100% / ${maxThumbnails})` }}>
          <CardActionArea>
            <CardMedia
              component="img"
              image={enableUrl(BASE_URL, url)}
              alt={parseS3Key(url).filename}
              onError={(e) => {
                e.currentTarget.src = NO_IMAGE;
              }}
              sx={{
                aspectRatio: '1/1',
              }}
            />
          </CardActionArea>
        </Card>
      ))}
      {!isNoImages && showHelperText && (
        <Chip
          label={`+${imageUrls.length - maxThumbnails}点`}
          sx={{
            position: 'absolute',
            top: 4,
            right: 4,
            color: 'white',
            bgcolor: (t) => t.palette.background.translucentBlack,
          }}
        />
      )}
    </Box>
  );
}
