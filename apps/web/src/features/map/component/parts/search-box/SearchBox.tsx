import { useAtom } from 'jotai';

import { Close, Search } from '@mui/icons-material';
import { Box, ClickAwayListener, InputBase } from '@mui/material';
import IconButton from '@mui/material/IconButton';

import { useSetLog } from '@/stores/search-logs';

import { searchAtom, searchBoxTextAtom } from './store';

export default function SearchBox() {
  const [{ isActive, text }, setState] = useAtom(searchAtom);
  const setLog = useSetLog();
  const [tmp, setTmp] = useAtom(searchBoxTextAtom);

  const handleChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const next = e.target.value;
    setTmp(next);
    if (next.length === 0) setState((prev) => ({ ...prev, isActive: true, text: '' }));
  };

  const handleClickSearchIcon = () => {
    // Search Icon
    if (text !== tmp) {
      setState((prev) => {
        const next = { ...prev, isActive: true, text: tmp };
        setLog(tmp);
        return next;
      });
    }
    // Close Icon
    if (text === tmp) {
      setState((prev) => ({ ...prev, isActive: false, text: '' }));
      setTmp('');
    }
  };

  const handleSubmit: React.FormEventHandler<HTMLFormElement> = (e) => {
    e.preventDefault();
    setState((prev) => {
      const next = { ...prev, isActive: true, text: tmp };
      if (tmp) {
        setLog(tmp);
      }
      return next;
    });
  };

  const handleFocusSearchText = () => {
    if (!isActive) {
      setState((prev) => ({ ...prev, isActive: true }));
    }
  };

  const handleClickAway = () => {
    if (isActive && tmp.length === 0) {
      setState((prev) => ({ ...prev, isActive: false }));
    }
  };
  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <Box
        component="form"
        sx={{
          px: 1,
          display: 'flex',
          alignItems: 'center',
          width: '100%',
        }}
        onSubmit={handleSubmit}
      >
        <InputBase
          sx={{ ml: 1, flex: 1 }}
          value={tmp}
          placeholder="Google マップで検索"
          inputProps={{ 'aria-label': 'search google maps' }}
          onFocus={handleFocusSearchText}
          onChange={handleChange}
        />
        <IconButton onClick={handleClickSearchIcon} disabled={tmp.length === 0}>
          {text === tmp && tmp.length !== 0 ? <Close /> : <Search />}
        </IconButton>
      </Box>
    </ClickAwayListener>
  );
}
