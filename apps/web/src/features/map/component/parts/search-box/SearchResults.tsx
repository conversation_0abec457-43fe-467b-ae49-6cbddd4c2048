import { useAtom } from 'jotai';

import { PlaceOutlined } from '@mui/icons-material';
import {
  Divider,
  LinearProgress,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
} from '@mui/material';

import useGeocoderByAddress from '@/hooks/useGeocoderByAddress';

import { searchAtom, searchBoxTextAtom } from './store';

export default function SearchResults() {
  const [, setTmp] = useAtom(searchBoxTextAtom);
  const [{ isActive, text }, setState] = useAtom(searchAtom);
  // Search for address
  const { data: results, isLoading } = useGeocoderByAddress(text);

  const handleClick = (address: string) => {
    setState((prev) => ({ ...prev, isActive: true, text: address }));
    setTmp(address);
  };

  return (
    <>
      {isActive && results && results.length > 0 && (
        <>
          <Divider />
          <List>
            {results.map((r) => (
              <ListItem key={r.address} disablePadding>
                <ListItemButton onClick={() => handleClick(r.address)}>
                  <ListItemIcon>
                    <PlaceOutlined />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography sx={{ display: 'inline' }} component="span" variant="body2">
                        {r.address}
                      </Typography>
                    }
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        </>
      )}
      {isLoading && <LinearProgress />}
    </>
  );
}
