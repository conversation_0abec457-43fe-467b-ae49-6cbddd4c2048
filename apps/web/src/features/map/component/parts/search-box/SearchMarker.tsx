import React from 'react';

import { useAtom } from 'jotai';

import type { Map as LeafletMap } from 'leaflet';

import useGeocoderByAddress from '@/hooks/useGeocoderByAddress';

import { searchAtom } from './store';

type Props = {
  map?: google.maps.Map | LeafletMap | null;
};

export default function SearchMarker({ map }: Props) {
  const [{ text, isActive }] = useAtom(searchAtom);

  // Search for address
  const { data } = useGeocoderByAddress(text);

  // latLng が変化すると実行
  React.useEffect(() => {
    if (isActive && data) map?.panTo(data[0]);
  }, [map, isActive, data]);
  return null;
}
