import { useAtom } from 'jotai';

import { AccessTime } from '@mui/icons-material';
import {
  Divider,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
} from '@mui/material';

import { useGetLogs } from '@/stores/search-logs';

import { searchAtom, searchBoxTextAtom } from './store';

export default function SearchLogs() {
  const [tmp, setTmp] = useAtom(searchBoxTextAtom);
  const [{ isActive }, setState] = useAtom(searchAtom);
  const logs = useGetLogs();

  const handleClick = (address: string) => {
    setState((prev) => ({ ...prev, isActive: true, text: address }));
    setTmp(address);
  };

  if (tmp.length === 0 && isActive && logs.length > 0)
    return (
      <>
        <Divider />
        <List>
          {logs.map((h) => (
            <ListItem key={h} disablePadding>
              <ListItemButton onClick={() => handleClick(h)}>
                <ListItemIcon>
                  <AccessTime />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography
                      sx={{ display: 'inline' }}
                      component="span"
                      variant="body2"
                      color="text.primary"
                    >
                      {h}
                    </Typography>
                  }
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </>
    );
  return null;
}
