import React from 'react';

import { ControlPosition, useMap } from '@vis.gl/react-google-maps';

export const useStreetViewControlOptionSettingEffect = (id?: string) => {
  const map = useMap(id);
  React.useEffect(() => {
    if (map) {
      map.getStreetView().setOptions({
        addressControlOptions: {
          position: ControlPosition.BOTTOM_CENTER,
        },
      });
    }
  }, [map]);
};
