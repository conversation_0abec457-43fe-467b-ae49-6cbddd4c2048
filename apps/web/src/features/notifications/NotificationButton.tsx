import type React from 'react';
import { useState } from 'react';

import { Notifications } from '@mui/icons-material';
import { Badge, Box, IconButton, type Theme, useMediaQuery } from '@mui/material';

import { trpc } from '@/api';

import NotificationDialog from './component/NotificationDialog';
import NotificationPopover from './component/NotificationPopover';

export const NotificationButton: React.FC = () => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const { data: notifications = [] } = trpc.notifications.list.useQuery();
  const isMobile = useMediaQuery<Theme>((theme) => theme.breakpoints.down('sm'));

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const unreadCount = notifications.filter((notification) => !notification.readAt).length;

  return (
    <>
      <Box>
        <IconButton color="inherit" onClick={handleClick}>
          <Badge badgeContent={unreadCount} color="secondary">
            <Notifications />
          </Badge>
        </IconButton>
      </Box>
      {isMobile ? (
        <NotificationDialog open={Boolean(anchorEl)} onClose={handleClose} />
      ) : (
        <NotificationPopover open={Boolean(anchorEl)} anchorEl={anchorEl} onClose={handleClose} />
      )}
    </>
  );
};
