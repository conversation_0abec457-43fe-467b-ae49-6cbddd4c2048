import { List } from '@mui/material';

import { trpc } from '@/api';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';

import EmptyState from './EmptyState';
import NotificationItem from './NotificationItem';

const NotificationList = () => {
  const { data: notifications } = trpc.notifications.list.useQuery();
  if (notifications === undefined) return <LoadingSpinner />;
  if (notifications.length === 0) return <EmptyState />;
  return (
    <List>
      {notifications.map((notification) => (
        <NotificationItem
          // データが更新されている場合、コンポーネントの状態をリセットします
          key={`${notification.id}-${notification.updatedAt}`}
          notification={notification}
        />
      ))}
    </List>
  );
};

export default NotificationList;
