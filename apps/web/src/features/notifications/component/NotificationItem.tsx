import React from 'react';

import { Close, OpenInNew } from '@mui/icons-material';
import { Button, Checkbox, IconButton, ListItem, ListItemText, Typography } from '@mui/material';

import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { getQueryKey } from '@trpc/react-query';
import type { RouterOutputs } from 'lambda-api';

import { useAppContext } from '@/contexts/app-context';
import { useTimeout } from '@/hooks/useTimeout';

import { trpc } from '@/api';
import { datetimeFormatter } from '@/funcs/date';
import NotificationTypeChip from './NotificationTypeChip';

type NotificationOnUser = RouterOutputs['notifications']['list'][number];

type Props = {
  notification: NotificationOnUser;
};

const NotificationItem = ({
  notification: { id, title, description, type, readAt, createdAt, inquiryId },
}: Props) => {
  const [updated, setUpdated] = React.useState(false);
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { labels } = useAppContext();

  const onSuccess = () => {
    setUpdated(true);
    queryClient.invalidateQueries({ queryKey: getQueryKey(trpc.notifications.list) });
  };

  const handleNavigateClick = () => {
    if (inquiryId) {
      navigate({ to: '/inquiries/$id', params: { id: inquiryId } });
    }
  };

  // 1秒以内にリクエストが発生した場合は Cloudfront がキャッシュを返すため更新されたデータが取得できません。
  // これはユーザーが既読のチェックボックスを連打した場合などに発生します。
  // この問題を解決するため、同じ1秒後に再取得するようにしています。
  // ただし、更新されたデータが取得された場合は`NotificationItem` のキー設定により
  // コンポーネントはリセットされ、再取得のためのリクエストは発生しません。
  const { start } = useTimeout(onSuccess, 1);

  const read = trpc.notifications.read.useMutation();
  const hide = trpc.notifications.hide.useMutation();
  const handleCheck = (_: unknown, checked: boolean) => {
    read.mutate({ id, isRead: checked }, { onSuccess });
    start();
  };

  const handleOnDelete = () => {
    hide.mutate({ id, hide: true }, { onSuccess });
    start();
  };

  const isRead = readAt !== null;
  const isClickable = Boolean(inquiryId);

  return (
    <ListItem
      sx={{
        bgcolor: (t) => (isRead ? t.palette.background.innerGrey : undefined),
        alignItems: 'flex-start',
        border: (t) => `1px ${t.palette.background.selectedGrey} solid`,
        borderRadius: '4px',
        marginBottom: '4px',
      }}
    >
      <ListItemText
        primary={
          <Typography variant="subtitle1" fontWeight={isRead ? 'normal' : 'bold'}>
            {title}
          </Typography>
        }
        secondary={
          <>
            <Typography variant="body2" color="text.primary" component="span">
              {description}
            </Typography>
            <Typography variant="caption" color="text.secondary" display="block">
              {datetimeFormatter(createdAt)}
            </Typography>
            {isClickable && (
              <Button
                size="small"
                startIcon={<OpenInNew />}
                onClick={handleNavigateClick}
                sx={{ ml: 'auto', mb: 2 }}
              >
                {labels.system.details}
              </Button>
            )}
            <br />
            <NotificationTypeChip
              type={type === 'system' || type === 'inquiryAssignment' ? type : 'system'}
              size="small"
            />
          </>
        }
      />

      <Checkbox onChange={handleCheck} checked={isRead} disabled={updated} />
      <IconButton edge="end" aria-label="delete" onClick={handleOnDelete} disabled={updated}>
        <Close />
      </IconButton>
    </ListItem>
  );
};

export default NotificationItem;
