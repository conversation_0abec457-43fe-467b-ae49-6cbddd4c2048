import { Box, Popover, Typography } from '@mui/material';

import NotificationList from './NotificationList';
import { ReadAllButton } from './ReadAllButton';

type Props = {
  open: boolean;
  anchorEl: HTMLElement | null;
  onClose: () => void;
};

const NotificationPopover = ({ open, anchorEl, onClose }: Props) => {
  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
      slotProps={{
        paper: {
          style: { width: '400px', maxHeight: '70vh' },
          elevation: 4,
        },
      }}
    >
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          {/* TODO: ページ名を取得する */}
          {/* {labels.Page.notifications} */}
          お知らせ
        </Typography>
        <NotificationList />
        <ReadAllButton />
      </Box>
    </Popover>
  );
};

export default NotificationPopover;
