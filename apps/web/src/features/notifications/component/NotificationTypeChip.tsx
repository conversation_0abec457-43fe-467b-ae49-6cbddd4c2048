import { Chip, type ChipProps } from '@mui/material';

import type { NotificationType } from 'common';

import { useAppContext } from '@/contexts/app-context';
import { notificationTypeColor } from '@/models/notification';

type Props = ChipProps & {
  type: NotificationType;
};

export default function NotificationTypeChip({ type, ...props }: Props) {
  const { labels } = useAppContext();
  const color = notificationTypeColor(type);
  const label = labels.notificationType[type];

  return (
    <Chip
      {...props}
      label={label}
      component="span"
      sx={{ ...props.sx, backgroundColor: color[200] }}
    />
  );
}
