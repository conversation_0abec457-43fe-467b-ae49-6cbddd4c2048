import { trpc } from '@/api';
import { Check } from '@mui/icons-material';
import { Button } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { getQuery<PERSON>ey } from '@trpc/react-query';

export const ReadAllButton = () => {
  const { data: notifications = [] } = trpc.notifications.list.useQuery();
  const queryClient = useQueryClient();
  const onSuccess = () => {
    queryClient.invalidateQueries({ queryKey: getQueryKey(trpc.notifications.list) });
  };

  const { mutate, isPending } = trpc.notifications.readAll.useMutation({ onSuccess });
  const handleClick = () => mutate();
  return (
    <Button
      variant="outlined"
      color="primary"
      fullWidth
      disabled={isPending || notifications.every((notification) => notification.readAt)}
      startIcon={<Check />}
      onClick={handleClick}
    >
      すべてを既読にする
    </Button>
  );
};
