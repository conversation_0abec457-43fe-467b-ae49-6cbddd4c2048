import { Close } from '@mui/icons-material';
import { Dialog, DialogActions, DialogContent, DialogTitle, Fade, IconButton } from '@mui/material';

import { useAppContext } from '@/contexts/app-context';
import NotificationList from './NotificationList';
import { ReadAllButton } from './ReadAllButton';

type Props = {
  open: boolean;
  onClose: () => void;
};

const NotificationDialog = ({ open, onClose }: Props) => {
  const { labels } = useAppContext();
  return (
    <Dialog fullScreen open={open} onClose={onClose} TransitionComponent={Fade}>
      <DialogTitle sx={{ textAlign: 'center' }}>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ position: 'absolute', left: 8, top: 8 }}
        >
          <Close />
        </IconButton>
        {labels.da.notify}
      </DialogTitle>
      <DialogContent dividers>
        <NotificationList />
      </DialogContent>
      <DialogActions>
        <ReadAllButton />
      </DialogActions>
    </Dialog>
  );
};

export default NotificationDialog;
