import { Box, Typography } from '@mui/material';

const EmptyState = () => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: (theme) => theme.spacing(4),
      textAlign: 'center',
    }}
  >
    <Box
      component="img"
      src="https://images.unsplash.com/photo-1562592619-908ca07deace"
      alt="No notifications"
      sx={{ width: 200, height: 200, marginBottom: '16px', borderRadius: '8px' }}
    />
    <Typography variant="h6">通知がありません</Typography>
    <Typography variant="body2" color="text.secondary">
      新しい通知が届いたらお知らせします！
    </Typography>
  </Box>
);

export default EmptyState;
