import { zodResolver } from '@hookform/resolvers/zod';
import CloseIcon from '@mui/icons-material/Close';
import { LoadingButton } from '@mui/lab';
import {
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  TextField,
  Typography,
} from '@mui/material';
import { useForm } from 'react-hook-form';

import { useForgotPassword } from '../api/useForgotPassword';
import { useCloseForgotPasswordDialog, useForgotPasswordDialog } from '../store';
import { type ResetPasswordInput, resetPasswordInputSchema } from '../types';

export default function ForgotPasswordDialog() {
  const dialog = useForgotPasswordDialog();
  const closeDialog = useCloseForgotPasswordDialog();

  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
  } = useForm<ResetPasswordInput>({
    defaultValues: { email: '', confirmEmail: '' },
    resolver: zodResolver(resetPasswordInputSchema),
  });

  const { mutate, isError } = useForgotPassword();
  const sendRequest = (data: ResetPasswordInput) => mutate(data.email);

  return (
    <Dialog open={dialog} onClose={closeDialog}>
      <DialogTitle sx={{ p: 2 }}>パスワードを再設定する</DialogTitle>
      <IconButton
        aria-label="close"
        onClick={closeDialog}
        sx={(theme) => ({
          position: 'absolute',
          right: 8,
          top: 8,
          color: theme.palette.grey[500],
        })}
      >
        <CloseIcon />
      </IconButton>

      <form onSubmit={handleSubmit(sendRequest)}>
        <DialogContent sx={{ p: 3, gap: 2 }}>
          <DialogContentText>
            <Typography
              gutterBottom
              variant="subtitle2"
              sx={{ color: (theme) => theme.palette.text.grey }}
            >
              メールを入力してください
            </Typography>
          </DialogContentText>
          <TextField
            {...register('email')}
            label="メール"
            type="email"
            autoFocus
            margin="dense"
            fullWidth
          />
          <TextField
            {...register('confirmEmail')}
            label="メール(確認)"
            type="email"
            margin="dense"
            error={Boolean(errors.confirmEmail)}
            helperText={errors.confirmEmail?.message}
            fullWidth
          />
          {isError && (
            <Alert severity="error">
              サーバーエラーが発生しました。管理者へお問い合わせください。
            </Alert>
          )}
        </DialogContent>
        <DialogActions sx={{ mb: 3, justifyContent: 'center' }}>
          <LoadingButton loading={isSubmitting} type="submit" variant="contained">
            パスワードをリセットする
          </LoadingButton>
        </DialogActions>
      </form>
    </Dialog>
  );
}
