import { useState } from 'react';

import { useAtomValue } from 'jotai';

import { zodResolver } from '@hookform/resolvers/zod';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import CloseIcon from '@mui/icons-material/Close';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { LoadingButton } from '@mui/lab';
import {
  Alert,
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  InputAdornment,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { useForm } from 'react-hook-form';

import { useConfirmResetPassword } from '../api/useForgotPassword';
import { useCloseResetPasswordDialog, useResetPasswordDialog, usernameAtom } from '../store';
import { type UpdatePasswordInput, updatePasswordInputSchema } from '../types';

export default function ResetPasswordDialog() {
  const dialog = useResetPasswordDialog();
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false);
  const handleClickShowPassword = () => setShowPassword((show) => !show);
  const handleClickShowPasswordConfirm = () => setShowPasswordConfirm((show) => !show);

  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
  } = useForm<UpdatePasswordInput>({
    defaultValues: { newPassword: '', confirmPassword: '' },
    resolver: zodResolver(updatePasswordInputSchema),
  });

  const closeDialog = useCloseResetPasswordDialog();
  const { mutate, isError } = useConfirmResetPassword();
  const username = useAtomValue(usernameAtom);
  const sendRequest = (data: UpdatePasswordInput) => {
    if (!username) return;
    mutate({
      confirmationCode: data.confirmationCode,
      newPassword: data.newPassword,
      username,
    });
  };

  const handleCloseDialog = (reason: string) => {
    if (reason === 'backdropClick') return;
    closeDialog();
  };

  return (
    <Dialog
      open={dialog}
      maxWidth="md"
      disableEscapeKeyDown
      onClose={(_, reason) => handleCloseDialog(reason)}
    >
      <DialogTitle sx={{ m: 0, p: 2 }}>新しいパスワードを設定</DialogTitle>
      <IconButton
        aria-label="close"
        onClick={closeDialog}
        sx={(theme) => ({
          position: 'absolute',
          right: 8,
          top: 8,
          color: theme.palette.grey[500],
        })}
      >
        <CloseIcon />
      </IconButton>

      <form onSubmit={handleSubmit(sendRequest)}>
        <DialogContent sx={{ p: 3, gap: 2 }}>
          <DialogContentText>
            <Typography
              gutterBottom
              variant="subtitle2"
              sx={{ color: (theme) => theme.palette.text.grey }}
            >
              新しいパスワードを入力してください
              <Tooltip
                title={
                  <Box>
                    <Typography color="inherit">パスワードポリシー</Typography>
                    <Typography variant="body2">※ 文字数: 8文字以上</Typography>
                    <Typography variant="body2">※ 半角の英大文字</Typography>
                    <Typography variant="body2">※ 半角の英小文字</Typography>
                    <Typography variant="body2">※ 数字</Typography>
                  </Box>
                }
                enterTouchDelay={0}
                leaveTouchDelay={5000}
                placement="right"
              >
                <IconButton>
                  <HelpOutlineIcon />
                </IconButton>
              </Tooltip>
            </Typography>
          </DialogContentText>
          <TextField
            {...register('confirmationCode')}
            label="認証コード"
            type="text"
            autoFocus
            margin="dense"
            fullWidth
            helperText="メールにパスワード再設定の認証コードを送信しています"
          />
          <TextField
            {...register('newPassword')}
            label="新しいパスワード"
            type={showPassword ? 'text' : 'password'}
            autoFocus
            margin="dense"
            error={Boolean(errors.confirmPassword)}
            fullWidth
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleClickShowPassword}
                      size="small"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <TextField
            {...register('confirmPassword')}
            label="新しいパスワード(確認)"
            type={showPasswordConfirm ? 'text' : 'password'}
            margin="dense"
            error={Boolean(errors.confirmPassword)}
            helperText={errors.confirmPassword?.message}
            fullWidth
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleClickShowPasswordConfirm}
                      size="small"
                    >
                      {showPasswordConfirm ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />

          {isError && (
            <Alert severity="error">
              サーバーエラーが発生しました。管理者へお問い合わせください。
            </Alert>
          )}
        </DialogContent>
        <DialogActions sx={{ mb: 3, justifyContent: 'center' }}>
          <LoadingButton loading={isSubmitting} type="submit" variant="contained">
            パスワードを変更する
          </LoadingButton>
        </DialogActions>
      </form>
    </Dialog>
  );
}
