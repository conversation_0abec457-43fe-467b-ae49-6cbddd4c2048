import { useSet<PERSON>tom } from 'jotai';

import {
  type ConfirmResetPasswordInput,
  confirmResetPassword,
  resetPassword,
} from '@aws-amplify/auth';
import { useMutation } from '@tanstack/react-query';

import {
  useCloseForgotPasswordDialog,
  useCloseResetPasswordDialog,
  useOpenResetPasswordDialog,
  usernameAtom,
} from '../store';

export const useForgotPassword = () => {
  const closeForgotPasswordDialog = useCloseForgotPasswordDialog();
  const openResetPasswordDialog = useOpenResetPasswordDialog();
  const setUsername = useSetAtom(usernameAtom);
  return useMutation({
    mutationFn: async (username: string) => {
      setUsername(username);
      await resetPassword({
        username,
      });
      closeForgotPasswordDialog();
      openResetPasswordDialog();
    },
  });
};

export const useConfirmResetPassword = () => {
  const close = useCloseResetPasswordDialog();
  return useMutation({
    mutationFn: async ({ username, newPassword, confirmationCode }: ConfirmResetPasswordInput) => {
      await confirmResetPassword({
        username,
        newPassword,
        confirmationCode,
      });
      close();
    },
  });
};
