import { atom, useAtomValue, useSetAtom } from 'jotai';

export const usernameAtom = atom<string>();
usernameAtom.debugLabel = 'username';

// Reset password dialog
const resetPasswordDialogAtom = atom(false);
resetPasswordDialogAtom.debugLabel = 'reset-password-dialog';
export const useResetPasswordDialog = () => useAtomValue(resetPasswordDialogAtom);
export const useOpenResetPasswordDialog = () => {
  const set = useSetAtom(resetPasswordDialogAtom);
  return () => set(true);
};
export const useCloseResetPasswordDialog = () => {
  const set = useSetAtom(resetPasswordDialogAtom);
  return () => set(false);
};

// Forgot password dialog
const forgotPasswordDialogAtom = atom(false);
forgotPasswordDialogAtom.debugLabel = 'forgot-password-dialog';
export const useForgotPasswordDialog = () => useAtomValue(forgotPasswordDialogAtom);
export const useOpenForgotPasswordDialog = () => {
  const set = useSetAtom(forgotPasswordDialogAtom);
  return () => set(true);
};
export const useCloseForgotPasswordDialog = () => {
  const set = useSetAtom(forgotPasswordDialogAtom);
  return () => set(false);
};
