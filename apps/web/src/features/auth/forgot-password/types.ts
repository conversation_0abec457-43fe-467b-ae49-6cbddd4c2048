import { z } from 'zod';

export const loginInputSchema = z.object({
  // TODO: エラー文言が「メール」で固定なのは一時的な対応です
  loginId: z.string().min(1, 'メールを入力してください'),
  password: z.string().min(1, 'パスワードを入力してください'),
});
export type LoginInput = z.infer<typeof loginInputSchema>;

export const updatePasswordInputSchema = z
  .object({
    confirmationCode: z.string().min(1, '認証コードを入力してください'),
    newPassword: z.string().min(1, '新しいパスワードを入力してください'),
    confirmPassword: z.string().min(1, '確認のため同じパスワードを入力してください'),
  })
  .superRefine((arg, ctx) => {
    if (arg.newPassword !== arg.confirmPassword) {
      ctx.addIssue({
        message: '新しいパスワードと確認用パスワードが一致しません',
        code: z.ZodIssueCode.custom,
        path: ['confirmPassword'],
      });
    }
  });
export type UpdatePasswordInput = z.infer<typeof updatePasswordInputSchema>;

export const resetPasswordInputSchema = z
  .object({
    email: z.string().email('メールの形式で入力してください'),
    confirmEmail: z.string().email('メールの形式で入力してください'),
  })
  .superRefine((arg, ctx) => {
    if (arg.email !== arg.confirmEmail) {
      ctx.addIssue({
        message: 'メールと確認用メールが一致しません',
        code: z.ZodIssueCode.custom,
        path: ['confirmEmail'],
      });
    }
  });
export type ResetPasswordInput = z.infer<typeof resetPasswordInputSchema>;
