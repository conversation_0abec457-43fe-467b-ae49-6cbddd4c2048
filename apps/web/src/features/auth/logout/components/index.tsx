import React from 'react';

import LogoutIcon from '@mui/icons-material/Logout';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  ListItemIcon,
  ListItemText,
  MenuItem,
} from '@mui/material';
import { signOut } from 'aws-amplify/auth';

import { useAppContext } from '@/contexts/app-context';

export const LogoutButton = () => {
  const { labels } = useAppContext();
  const [logoutModalOpen, setLogoutModalOpen] = React.useState(false);

  const handleLogoutClick = () => {
    setLogoutModalOpen(true);
  };

  const handleCloseLogoutModal = () => {
    setLogoutModalOpen(false);
  };

  const handleSignOut = React.useCallback(async () => {
    await signOut();
    setLogoutModalOpen(false);
  }, []);

  return (
    <>
      <MenuItem onClick={handleLogoutClick}>
        <ListItemIcon>
          <LogoutIcon />
        </ListItemIcon>
        <ListItemText primary="ログアウト" />
      </MenuItem>

      <Dialog open={logoutModalOpen} onClose={handleCloseLogoutModal}>
        <DialogTitle>ログアウト</DialogTitle>
        <DialogContent>
          <DialogContentText>ログアウトしてよろしいですか？</DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 0, gap: 2 }}>
          <Button onClick={handleCloseLogoutModal} variant="outlined" fullWidth>
            {labels.action.cancel}
          </Button>
          <Button onClick={handleSignOut} variant="contained" color="primary" fullWidth>
            ログアウト
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
