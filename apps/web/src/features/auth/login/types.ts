import { z } from 'zod';

export const loginInputSchema = z.object({
  // TODO: エラー文言が「メール」で固定なのは一時的な対応です
  loginId: z.string().min(1, 'メールを入力してください'),
  password: z.string().min(1, 'パスワードを入力してください'),
});
export type LoginInput = z.infer<typeof loginInputSchema>;

export const updatePasswordInputSchema = z
  .object({
    newPassword: z.string().min(1, '新しいパスワードを入力してください'),
    confirmPassword: z.string().min(1, '確認のため同じパスワードを入力してください'),
  })
  .superRefine((arg, ctx) => {
    if (arg.newPassword !== arg.confirmPassword) {
      ctx.addIssue({
        message: '新しいパスワードと確認用パスワードが一致しません',
        code: z.ZodIssueCode.custom,
        path: ['confirmPassword'],
      });
    }
  });
export type UpdatePasswordInput = z.infer<typeof updatePasswordInputSchema>;
