import { useAtomValue } from 'jotai';

import { useMutation } from '@tanstack/react-query';
import { Amplify } from 'aws-amplify';
import { signIn } from 'aws-amplify/auth';

import { userPoolAtom } from '@/stores/user-pool';

import { useOpenUpdateFirstPasswordDialog } from '../store';
import type { LoginInput } from '../types';

export const useLogin = () => {
  const open = useOpenUpdateFirstPasswordDialog();
  const userPool = useAtomValue(userPoolAtom);
  return useMutation({
    mutationFn: async (input: LoginInput) => {
      if (!userPool) throw new Error('User pool is not set');
      Amplify.configure({ Auth: { Cognito: userPool } });
      const { nextStep } = await signIn({ username: input.loginId, password: input.password });
      if (nextStep.signInStep === 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED') open();
    },
  });
};
