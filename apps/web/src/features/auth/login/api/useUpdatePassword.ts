import { confirmSignIn } from '@aws-amplify/auth';
import { useMutation } from '@tanstack/react-query';

import { useCloseUpdateFirstPasswordDialog } from '../store';

export const useUpdatePassword = () => {
  const close = useCloseUpdateFirstPasswordDialog();
  return useMutation({
    mutationFn: async (newPassword: string) => confirmSignIn({ challengeResponse: newPassword }),
    onSuccess: () => close(),
  });
};
