import { useState } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { LoadingButton } from '@mui/lab';
import {
  Alert,
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  InputAdornment,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { useForm } from 'react-hook-form';

import { useUpdatePassword } from '../api/useUpdatePassword';
import { useUpdateFirstPasswordDialog } from '../store';
import { type UpdatePasswordInput, updatePasswordInputSchema } from '../types';

export default function UpdateFirstPasswordDialog() {
  const dialog = useUpdateFirstPasswordDialog();
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false);
  const handleClickShowPassword = () => setShowPassword((show) => !show);
  const handleClickShowPasswordConfirm = () => setShowPasswordConfirm((show) => !show);

  const {
    register,
    formState: { errors },
    handleSubmit,
  } = useForm<UpdatePasswordInput>({
    defaultValues: { newPassword: '', confirmPassword: '' },
    resolver: zodResolver(updatePasswordInputSchema),
  });

  const { mutate, isPending, isError, error } = useUpdatePassword();

  return (
    <Dialog open={dialog} onClose={() => null} maxWidth="md">
      <DialogTitle>パスワード変更</DialogTitle>
      <form onSubmit={handleSubmit((data) => mutate(data.newPassword))}>
        <DialogContent sx={{ gap: 2 }}>
          <DialogContentText sx={{ color: (theme) => theme.palette.text.grey }}>
            初期パスワードを変更してください
            <Tooltip
              title={
                <Box>
                  <Typography color="inherit">パスワードポリシー</Typography>
                  <Typography variant="body2">※ 文字数: 8文字以上</Typography>
                  <Typography variant="body2">※ 半角の英大文字</Typography>
                  <Typography variant="body2">※ 半角の英小文字</Typography>
                  <Typography variant="body2">※ 数字</Typography>
                </Box>
              }
              enterTouchDelay={0}
              leaveTouchDelay={5000}
              placement="right"
            >
              <IconButton>
                <HelpOutlineIcon />
              </IconButton>
            </Tooltip>
          </DialogContentText>
          <TextField
            {...register('newPassword')}
            label="新しいパスワード"
            type={showPassword ? 'text' : 'password'}
            autoFocus
            margin="dense"
            fullWidth
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleClickShowPassword}
                      size="small"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <TextField
            {...register('confirmPassword')}
            label="新しいパスワード(確認)"
            type={showPasswordConfirm ? 'text' : 'password'}
            margin="dense"
            error={Boolean(errors.confirmPassword)}
            helperText={errors.confirmPassword?.message}
            fullWidth
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleClickShowPasswordConfirm}
                      size="small"
                    >
                      {showPasswordConfirm ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />

          {isError && (
            <Alert severity="error">
              <Typography sx={{ mr: 1 }}>{error.name}</Typography>
              {/* TODO: ネットワークエラーのケースも考慮してメッセージを表示したい */}
              <Typography variant="body2">
                {error.message ?? 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
              </Typography>
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <LoadingButton loading={isPending} type="submit" variant="contained" fullWidth>
            パスワードを変更する
          </LoadingButton>
        </DialogActions>
      </form>
    </Dialog>
  );
}
