import { useState } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import LoginIcon from '@mui/icons-material/Login';
import { LoadingButton } from '@mui/lab';
import {
  Alert,
  AlertTitle,
  Box,
  Button,
  IconButton,
  InputAdornment,
  Stack,
  TextField,
} from '@mui/material';
import { useForm } from 'react-hook-form';

import { match } from 'ts-pattern';
import { useOpenForgotPasswordDialog } from '../../forgot-password/store';
import { useLogin } from '../api/useLogin';
import { type LoginInput, loginInputSchema } from '../types';

const cognitoErrors = [
  'NotAuthorizedException',
  'UserNotFoundException',
  'UserNotConfirmedException',
  'UserAlreadyAuthenticatedException',
];

type AlertCognitoErrorProps = {
  error: Error;
};
const AlertCognitoError = ({ error }: AlertCognitoErrorProps) => {
  const title = match(error.name)
    .with('UserNotConfirmedException', () => 'このアカウントは有効性が検証されていません')
    .with('NotAuthorizedException', () => 'メール、またはパスワードが間違っています')
    .with('UserNotFoundException', () => 'メール、またはパスワードが間違っています')
    .with('UserAlreadyAuthenticatedException', () => '既にログインしています')
    .otherwise(() => 'アカウントを認証できませんでした');
  return (
    <Alert severity="error" sx={{ maxHeight: '30vh', overflow: 'auto' }}>
      <AlertTitle>{title}</AlertTitle>
      {!cognitoErrors.includes(error.name) && (
        <Box>
          {error.message}
          <br />
          {error.stack}
          <br />
          {JSON.stringify(error)}
        </Box>
      )}
    </Alert>
  );
};

export default function LoginForm() {
  const [showPassword, setShowPassword] = useState(false);
  const handleClickShowPassword = () => setShowPassword((show) => !show);
  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
  } = useForm<LoginInput>({
    defaultValues: { loginId: '', password: '' },
    resolver: zodResolver(loginInputSchema),
  });

  const { mutate, isError, error } = useLogin();

  const openForgotPasswordDialog = useOpenForgotPasswordDialog();
  const handleClickForgotPassword = () => {
    openForgotPasswordDialog();
  };

  return (
    <Stack
      component="form"
      onSubmit={handleSubmit((data) => mutate(data))}
      noValidate
      gap={3}
      sx={{ width: '100%', mt: 1 }}
    >
      <TextField
        {...register('loginId')}
        label="メール"
        required
        autoComplete="email"
        autoFocus
        error={Boolean(errors.loginId)}
        helperText={errors.loginId?.message}
        fullWidth
      />
      <TextField
        {...register('password')}
        label="パスワード"
        type={showPassword ? 'text' : 'password'}
        required
        autoComplete="current-password"
        error={Boolean(errors.password)}
        helperText={errors.password?.message}
        fullWidth
        slotProps={{
          input: {
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  aria-label="toggle password visibility"
                  onClick={handleClickShowPassword}
                  size="small"
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            ),
          },
        }}
      />
      {isError && <AlertCognitoError error={error} />}
      <LoadingButton
        loading={isSubmitting}
        type="submit"
        variant="contained"
        endIcon={
          <LoginIcon
            sx={{
              width: (t) => t.typography.fontSize * 1.5,
              height: (t) => t.typography.fontSize * 1.5,
            }}
          />
        }
      >
        ログインする
      </LoadingButton>
      <Box>
        <Button onClick={handleClickForgotPassword} variant="text">
          パスワードをお忘れですか？
        </Button>
      </Box>
    </Stack>
  );
}
