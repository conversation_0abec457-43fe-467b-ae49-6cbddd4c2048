import { atom, useAtomValue, useSetAtom } from 'jotai';

const dialogAtom = atom(false);
dialogAtom.debugLabel = 'update-first-password-dialog';

export const useUpdateFirstPasswordDialog = () => useAtomValue(dialogAtom);
export const useOpenUpdateFirstPasswordDialog = () => {
  const set = useSetAtom(dialogAtom);
  return () => set(true);
};
export const useCloseUpdateFirstPasswordDialog = () => {
  const set = useSetAtom(dialogAtom);
  return () => set(false);
};
