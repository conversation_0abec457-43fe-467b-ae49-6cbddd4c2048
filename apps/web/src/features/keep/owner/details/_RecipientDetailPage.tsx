import { trpc } from '@/api';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { useParams } from '@tanstack/react-router';
import { isNullish } from 'common';
import { RecipientDetails } from './RecipientDetail';

export const RecipientDetailPage = () => {
  const { id } = useParams({ from: '/app/keep/recipients/$id' });
  const { data: recipient } = trpc.owners.get.useQuery({ id });

  if (isNullish(recipient)) return <LoadingSpinner />;

  return <RecipientDetails recipient={recipient} />;
};
