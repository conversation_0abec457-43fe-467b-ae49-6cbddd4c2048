import { zodResolver } from '@hookform/resolvers/zod';
import { Paper, Stack } from '@mui/material';
import { useForm } from 'react-hook-form';

import { MainLayout } from '@/components/core/layout/MainLayout';
import { recipientDetailMeta } from '@/router/routes/keep/recipient-detail-route';
import type { Recipient } from 'lambda-api';
import {
  type RecipientState,
  RecipientStateSchema,
  RhfRecipient,
  recipientToState,
} from '../common/RhfRecipient';

type Props = {
  recipient: Recipient;
};

export const RecipientDetails = ({ recipient }: Props) => {
  const { control, watch } = useForm<RecipientState>({
    mode: 'onChange',
    defaultValues: recipientToState(recipient),
    resolver: zodResolver(RecipientStateSchema),
  });

  const title = recipientDetailMeta.useTitle();

  return (
    <MainLayout scrollable title={title}>
      <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
        <RhfRecipient control={control} watch={watch} readOnly />
      </Stack>
    </MainLayout>
  );
};
