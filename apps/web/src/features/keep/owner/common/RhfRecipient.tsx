import { useAppContext } from '@/contexts/app-context';
import { Grid } from '@mui/material';

import type { Recipient } from 'lambda-api';
import { RhfTextField } from 'mui-ex';
import type { Control, UseFormWatch } from 'react-hook-form';
import { z } from 'zod';

export const RecipientStateSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  postalCode: z.string().optional(),
  address: z.string().optional(),
  tel: z.string().optional(),
});

export type RecipientState = z.infer<typeof RecipientStateSchema>;

export const recipientToState = (recipient: Recipient): RecipientState => {
  return {
    id: recipient.id,
    name: recipient.name ?? undefined,
    postalCode: recipient.postalCode ?? undefined,
    address: recipient.address ?? undefined,
    tel: recipient.tel ?? undefined,
  };
};

type CommonProps = {
  readOnly?: boolean;
  setValue?: (name: keyof RecipientState, values: string) => void;
};
type Props<T extends RecipientState> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<RecipientState>;
  watch: UseFormWatch<RecipientState>;
};

export const RhfRecipient = <T extends RecipientState>(props: Props<T>) => {
  const { labels } = useAppContext();
  const { control, readOnly } = props as unknown as InnerProps;

  return (
    <Grid container spacing={2}>
      <Grid size={{ xs: 12, sm: 6 }}>
        <RhfTextField
          control={control}
          name="name"
          label={labels.domain.recipient}
          readOnly={readOnly}
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <RhfTextField control={control} name="postalCode" label="郵便番号" readOnly={readOnly} />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <RhfTextField control={control} name="address" label="住所" readOnly={readOnly} />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <RhfTextField control={control} name="tel" label="電話番号" readOnly={readOnly} />
      </Grid>
    </Grid>
  );
};
