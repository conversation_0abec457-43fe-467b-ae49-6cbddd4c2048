import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { recipientsMeta } from '@/router/routes/keep/recipients-route';
import { Paper, Stack, Typography } from '@mui/material';
import type { GridRowParams } from '@mui/x-data-grid';
import type { GridColDef } from '@mui/x-data-grid';
import { useNavigate } from '@tanstack/react-router';
import type { Recipient } from 'lambda-api';
import { DataGrid } from 'mui-ex';

type ColDef = GridColDef<Recipient> & {
  field: keyof Recipient;
};

const useColumns = () => {
  const { labels } = useAppContext();
  const nameCodeCol: ColDef = {
    field: 'name',
    headerName: labels.domain.recipient,
    width: 200,
  };
  const postalCodeCol: ColDef = {
    field: 'postalCode',
    headerName: labels.location.postalCode,
    width: 100,
  };
  const addressCol: ColDef = {
    field: 'address',
    headerName: '住所',
    width: 400,
  };
  const telCol: ColDef = {
    field: 'tel',
    headerName: '電話番号',
    width: 150,
  };
  return [nameCodeCol, postalCodeCol, addressCol, telCol];
};

export const RecipientsPage = () => {
  const { data: recipients = [], isPending } = trpc.owners.list.useQuery();
  const columns = useColumns();
  const navigate = useNavigate();
  const handleRowClick = (params: GridRowParams) => {
    navigate({
      to: '/keep/recipients/$id',
      params: { id: params.id.toString() },
    });
  };
  return (
    <Stack spacing={1} sx={{ p: 2, width: 1, height: 1 }}>
      <Typography variant="h4">{recipientsMeta.useTitle()}</Typography>
      <Paper sx={{ display: 'flex', width: 1, height: 1 }}>
        <DataGrid
          persistent="/keep/recipients"
          columns={columns}
          rows={recipients}
          onRowClick={handleRowClick}
          disableRowSelectionOnClick
          loading={isPending}
          sx={{
            // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
            '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
            '& .MuiTablePagination-select': { mr: 2 },
          }}
        />
      </Paper>
    </Stack>
  );
};
