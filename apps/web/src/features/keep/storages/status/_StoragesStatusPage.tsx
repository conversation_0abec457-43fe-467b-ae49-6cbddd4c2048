import { trpc } from '@/api';
import { createImageColProps } from '@/components/ImageAvatar';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';
import { storagesStatusMeta } from '@/router/routes/keep/storages-status-route';
import { BASE_URL } from '@/types/vite-env';
import { Paper } from '@mui/material';
import type { GridColDef } from '@mui/x-data-grid';
import type { StorageStatus } from 'lambda-api';
import { DataGrid } from 'mui-ex';

type ColDef = GridColDef<StorageStatus> & {
  field: keyof StorageStatus;
};

const useColumns = () => {
  const { labels } = useAppContext();

  const columns: ColDef[] = [
    {
      field: 'images',
      ...createImageColProps<StorageStatus>((storage) => {
        const key = storage.images?.[0]?.key;
        if (!key) return undefined;
        return `${BASE_URL}/${key}`;
      }),
    },
    {
      field: 'name',
      headerName: `${labels.domain.storage}名`,
      width: 400,
    },
    {
      field: 'count',
      headerName: `${labels.et.store}台数`,
      width: 120,
    },
  ];

  return columns;
};

export const StoragesStatusPage = () => {
  const { data: counts = [], isPending } = trpc.storages.count.useQuery();
  const columns = useColumns();

  const title = storagesStatusMeta.useTitle();

  return (
    <MainLayout title={title}>
      <Paper sx={{ width: 1, height: 1 }}>
        <DataGrid
          persistent="/keep/storages/status"
          columns={columns}
          rows={counts}
          disableRowSelectionOnClick
          loading={isPending}
          sx={{
            // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
            '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
            '& .MuiTablePagination-select': { mr: 2 },
          }}
        />
      </Paper>
    </MainLayout>
  );
};
