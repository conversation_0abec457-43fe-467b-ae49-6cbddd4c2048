import { trpc } from '@/api';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';
import { storagesUnprocessedMeta } from '@/router/routes/keep/storages-unprocessed-route';
import { zodResolver } from '@hookform/resolvers/zod';
import BarChartIcon from '@mui/icons-material/BarChart';
import ViewModuleIcon from '@mui/icons-material/ViewModule';
import { Button, Grid, useTheme } from '@mui/material';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { BarChart } from '@mui/x-charts';
import { getJpFiscalYear } from 'common';
import type { Storage } from 'lambda-api';
import { RhfDatePicker, RhfSingleSelect } from 'mui-ex';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const formValueSchema = z.object({
  year: z.date(),
  storageId: z.string().optional(),
});

type FormValues = z.infer<typeof formValueSchema>;

const buttonWidth = 110;

export const StoragesUnprocessedPage = () => {
  const { storages, labels } = useAppContext();
  const theme = useTheme();
  const [viewMode, setViewMode] = React.useState<'chart' | 'cards'>('cards');
  const [searchParams, setSearchParams] = React.useState<FormValues>({
    year: new Date(getJpFiscalYear(), 0, 1),
    storageId: undefined,
  });

  const {
    control,
    watch,
    formState: { errors },
  } = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: {
      year: new Date(getJpFiscalYear(), 0, 1),
      storageId: 'all',
    },
    resolver: zodResolver(formValueSchema),
  });

  const storageId = watch('storageId');
  const year = watch('year');

  const { data: annualStats, isPending } =
    trpc.statistics.countAnnualBicyclesOfStoreStatus.useQuery({
      year: new Date(searchParams.year).getFullYear(),
      storageId: searchParams.storageId,
    });

  const title = storagesUnprocessedMeta.useTitle();

  const handleViewModeChange = (
    _event: React.MouseEvent<HTMLElement>,
    newMode: 'chart' | 'cards' | null,
  ) => {
    if (newMode !== null) {
      setViewMode(newMode);
    }
  };

  const handleSearch = () => {
    const selectedStorage = storageId === 'all' ? undefined : storageId;
    setSearchParams({
      year,
      storageId: selectedStorage,
    });
  };

  const allStoragesLabel = `全ての${labels.domain.storage}`;
  const storageLabel =
    storageId === 'all'
      ? allStoragesLabel
      : (storages.find((s: Storage) => s.id === storageId)?.name ?? '');

  const yearLabel = `(${year.getFullYear()}年)`;

  return (
    <MainLayout scrollable title={title}>
      <Stack spacing={2} sx={{ p: 2 }}>
        {/* Filters */}
        <Stack component={Paper} direction="row" spacing={2} sx={{ p: 2 }}>
          <Stack direction={{ xs: 'column', md: 'row' }} spacing={2} sx={{ flexGrow: 1 }}>
            <Box sx={{ flexGrow: 1 }}>
              <RhfSingleSelect
                name="storageId"
                control={control}
                options={[
                  { label: allStoragesLabel, value: 'all' },
                  ...storages.map((s) => ({
                    label: s.name,
                    value: s.id,
                  })),
                ]}
              />
            </Box>
            <Box sx={{ flexGrow: 1 }}>
              <RhfDatePicker label="表示年度" name="year" views={['year']} control={control} />
            </Box>
          </Stack>
          <Stack sx={{ justifyContent: 'end' }}>
            <Button
              disabled={Boolean(errors.year)}
              onClick={handleSearch}
              sx={{ width: buttonWidth }}
            >
              検索
            </Button>
          </Stack>
        </Stack>
        {/* Chart */}
        <Card>
          <CardContent>
            <Stack spacing={2} direction="row" alignItems="center">
              <Typography variant="h6">
                {`${storageLabel} `}
                <Box component="br" sx={{ display: { xs: 'inherit', sm: 'none' } }} />
                {yearLabel}
              </Typography>
              <Box sx={{ flexGrow: 1 }} />
              <ToggleButtonGroup
                value={viewMode}
                exclusive
                onChange={handleViewModeChange}
                aria-label="表示モード"
                sx={{ width: buttonWidth }}
              >
                <Tooltip title="カード表示">
                  <ToggleButton value="cards" aria-label="カード表示" sx={{ flexGrow: 1 }}>
                    <ViewModuleIcon />
                  </ToggleButton>
                </Tooltip>
                <Tooltip title="グラフ表示">
                  <ToggleButton value="chart" aria-label="グラフ表示" sx={{ flexGrow: 1 }}>
                    <BarChartIcon />
                  </ToggleButton>
                </Tooltip>
              </ToggleButtonGroup>
            </Stack>

            {isPending ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <LoadingSpinner />
              </Box>
            ) : annualStats && annualStats.data.length > 0 ? (
              viewMode === 'chart' ? (
                <Box sx={{ height: 400, width: '100%' }}>
                  <BarChart
                    dataset={annualStats.data.map((item) => ({
                      month: item.month,
                      count: item.count,
                    }))}
                    xAxis={[{ scaleType: 'band', dataKey: 'month' }]}
                    yAxis={[
                      {
                        min: 0,
                        tickLabelInterval: (value) => Number.isInteger(value),
                      },
                    ]}
                    series={[
                      { dataKey: 'count', label: '未処理台数', color: theme.palette.primary.main },
                    ]}
                    height={350}
                  />
                </Box>
              ) : (
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  {annualStats.data.map((item) => (
                    <Grid size={{ xs: 6, sm: 4, md: 2 }} key={item.month}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            {item.month}月
                          </Typography>
                          <Typography variant="h4" color="primary" align="center">
                            {item.count}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              )
            ) : (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <Typography>データがありません</Typography>
              </Box>
            )}
          </CardContent>
        </Card>
      </Stack>
    </MainLayout>
  );
};
