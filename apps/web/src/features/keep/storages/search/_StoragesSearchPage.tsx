import { trpc } from '@/api';
import {
  abandonedAddressCol,
  announcementStatusCol,
  bicycleTypeCol,
  bodySerialNumberCol,
  colorCol,
  conditionCol,
  hasBasketCol,
  imagesCol,
  isCollectedStorageFeeCol,
  isLockedCol,
  isNoParkingAreaCol,
  landmarkCol,
  numberPlateCol,
  ownerAddressCol,
  ownerNameCol,
  ownerPostalCodeCol,
  ownerStatusCol,
  referenceStatusCol,
  registrationNumberCol,
  releaseStatusCol,
  serialNoCol,
  statusCol,
} from '@/components/bicycles/columns';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';
import { createUseColumns } from '@/hooks/useColumns';
import { storagesSearchMeta } from '@/router/routes/keep/storages-search-route';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Paper, Stack } from '@mui/material';
import { BicycleTypeSchema } from 'common';
import { DataGrid, RhfDatePicker, RhfSingleSelect, RhfTextField } from 'mui-ex';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const useColumns = createUseColumns([
  imagesCol,
  serialNoCol,
  statusCol,
  announcementStatusCol,
  referenceStatusCol,
  ownerStatusCol,
  releaseStatusCol,
  landmarkCol,
  bicycleTypeCol,
  registrationNumberCol,
  bodySerialNumberCol,
  numberPlateCol,
  isNoParkingAreaCol,
  hasBasketCol,
  isLockedCol,
  isCollectedStorageFeeCol,
  colorCol,
  conditionCol,
  ownerNameCol,
  ownerPostalCodeCol,
  ownerAddressCol,
  abandonedAddressCol,
]);

const FormSchema = z.object({
  storageId: z.string(),
  cycleType: z.union([BicycleTypeSchema, z.literal('all')]),
  serialTagRange: z.object({
    from: z.string().optional(),
    to: z.string().optional(),
  }),
  removeDate: z.object({
    from: z.date(),
    to: z.date(),
  }),
});

type FormValues = z.infer<typeof FormSchema>;
const createDefaultState = (firstStorageId: string): FormValues => ({
  storageId: firstStorageId,
  cycleType: 'all',
  serialTagRange: {
    from: '',
    to: '',
  },
  removeDate: {
    from: new Date(new Date().setMonth(new Date().getMonth() - 1)),
    to: new Date(),
  },
});

export const StoragesSearchPage = () => {
  const { storages, labels } = useAppContext();
  const buttonWidth = 200;
  const allCycleTypesLabel = `全ての${labels.body.type}`;

  const firstStorageId = storages.length > 0 ? storages[0].id : '';
  const defaultState = React.useMemo(() => createDefaultState(firstStorageId), [firstStorageId]);

  const [state, setState] = React.useState<FormValues>(defaultState);
  const {
    control,
    handleSubmit,
    formState: { isDirty },
    reset,
  } = useForm<FormValues>({
    defaultValues: defaultState,
    resolver: zodResolver(FormSchema),
  });

  const handleSearch = (values: FormValues) => {
    setState(values);
    reset(values, { keepValues: true });
  };

  const {
    data: bicycles = [],
    isPending,
    isError,
  } = trpc.storages.searchBicycles.useQuery(
    {
      storageId: state.storageId,
      cycleType: state.cycleType === 'all' ? undefined : state.cycleType,
      serialTagRange:
        state.serialTagRange.from || state.serialTagRange.to
          ? {
              from: state.serialTagRange.from || undefined,
              to: state.serialTagRange.to || undefined,
            }
          : undefined,
      removeDate: state.removeDate,
    },
    {
      enabled: !!state.storageId,
    },
  );

  const columns = useColumns(bicycles);

  const title = storagesSearchMeta.useTitle();

  return (
    <MainLayout scrollable maxWidth="xl" title={title}>
      <Stack spacing={2} sx={{ p: 2 }}>
        {/* Filters */}
        <Paper sx={{ p: 2 }}>
          <Stack component="form" onSubmit={handleSubmit(handleSearch)} noValidate spacing={2}>
            <Stack spacing={2} direction="row" sx={{ width: '100%' }}>
              <RhfSingleSelect
                control={control}
                name="storageId"
                label={labels.domain.storage}
                options={storages.map((s) => ({ label: s.name, value: s.id }))}
              />
              <RhfSingleSelect
                control={control}
                name="cycleType"
                label={labels.body.type}
                options={[
                  { label: allCycleTypesLabel, value: 'all' },
                  ...BicycleTypeSchema.options.map((o) => ({
                    label: labels.bicycleType[o],
                    value: o,
                  })),
                ]}
              />
            </Stack>
            <Stack direction="row" spacing={2} sx={{ margin: '0 auto' }}>
              <RhfTextField control={control} name="serialTagRange.from" label="整理番号（から）" />
              <RhfTextField control={control} name="serialTagRange.to" label="整理番号（まで）" />
            </Stack>
            <Stack direction="row" spacing={2} sx={{ margin: '0 auto' }}>
              <RhfDatePicker
                control={control}
                name="removeDate.from"
                label="移送日（から）"
                views={['year', 'month', 'day']}
                mui={{
                  disableFuture: true,
                  minDate: new Date('2010-01-01'),
                }}
              />
              <RhfDatePicker
                control={control}
                name="removeDate.to"
                label="移送日（まで）"
                views={['year', 'month', 'day']}
                mui={{
                  disableFuture: true,
                  minDate: new Date('2010-01-01'),
                }}
              />
            </Stack>
            <Stack direction="row" spacing={2} sx={{ justifyContent: 'end' }}>
              <Button
                type="submit"
                variant={isDirty ? 'contained' : 'outlined'}
                color={isDirty ? 'primary' : 'inherit'}
                sx={{ width: buttonWidth }}
              >
                表示する
              </Button>
            </Stack>
          </Stack>
        </Paper>
        {/* Results */}
        <Paper sx={{ display: 'flex', width: 1, flexGrow: 1, overflow: 'auto' }}>
          <DataGrid
            persistent="/keep/storages/search"
            columns={columns}
            rows={bicycles}
            disableRowSelectionOnClick
            loading={isPending}
            serverError={isError}
            sx={{
              // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
              '&.MuiDataGrid-columnHeader:focus-within': { outline: 'none' },
              '&.MuiDataGrid-cell:focus-within': { outline: 'none' },
              '&.MuiTablePagination-select': { mr: 2 },
            }}
          />
        </Paper>
      </Stack>
    </MainLayout>
  );
};
