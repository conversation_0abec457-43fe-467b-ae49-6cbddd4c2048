import { Alert, Paper, Stack } from '@mui/material';
import { enqueueSnackbar } from 'notistack';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { tenantQueryKey, useAppContext } from '@/contexts/app-context';
import { priceSuggestionRouters } from '@/router/routes/settings/price-suggestions';
import { zodResolver } from '@hookform/resolvers/zod';
import { Add, Close } from '@mui/icons-material';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';

import { useErrorMessageCreator } from '@/hooks/useErrorMessageCreator';
import { stringOrNull } from 'common';
import { useForm } from 'react-hook-form';
import {
  type GradeState,
  RhfPriceSuggestion,
  createGradeStateSchema,
} from '../common/RhfPriceSuggestion';

export const PriceSuggestionCreatePage = () => {
  const { labels, priceSuggestions } = useAppContext();
  const errorMessageCreator = useErrorMessageCreator();
  const GradeStateSchema = createGradeStateSchema(labels);
  const { control, handleSubmit, formState } = useForm<GradeState>({
    mode: 'onChange',
    defaultValues: {
      price: 0,
      description: '',
      sortOrder: Math.max(0, ...priceSuggestions.map((s) => s.sortOrder)) + 1,
    } as const satisfies GradeState,
    resolver: zodResolver(GradeStateSchema),
  });
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { error, mutateAsync } = trpc.priceSuggestions.create.useMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: tenantQueryKey });
      navigate({ to: '/settings/price-suggestions' });
      enqueueSnackbar(`${labels.assessment.price}を${labels.action.create}しました`, {
        variant: 'success',
      });
    },
  });
  const title = priceSuggestionRouters.meta.create.useTitle();
  const submit = async (data: GradeState) => {
    await mutateAsync({
      id: crypto.randomUUID(),
      price: data.price,
      description: stringOrNull(data.description),
      sortOrder: data.sortOrder,
    });
  };
  return (
    <MainLayout scrollable title={title}>
      <Stack component="form" spacing={2} noValidate onSubmit={handleSubmit(submit)}>
        <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
          <RhfPriceSuggestion control={control} />
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {errorMessageCreator(error.message)}
            </Alert>
          )}
        </Stack>
        <TwoActionButtons
          primary={{
            type: 'submit',
            icon: Add,
            label: labels.doing.create,
            loading: formState.isSubmitting,
          }}
          secondary={{
            icon: Close,
            end: true,
            label: labels.doing.cancel,
            onClick: () => navigate({ to: '/settings/price-suggestions' }),
          }}
          fullWidth
          spacing={2}
        />
      </Stack>
    </MainLayout>
  );
};
