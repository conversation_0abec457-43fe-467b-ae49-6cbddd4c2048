import { trpc } from '@/api';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { useParams } from '@tanstack/react-router';
import { isNullish } from 'common';
import { PriceSuggestionEdit } from './PriceSuggestionEdit';

export const PriceSuggestionEditPage = () => {
  const { id } = useParams({ from: '/app/settings/price-suggestions/$id/edit' });
  const { data: grade } = trpc.priceSuggestions.get.useQuery({ id });

  if (isNullish(grade)) return <LoadingSpinner />;

  return <PriceSuggestionEdit priceSuggestion={grade} />;
};
