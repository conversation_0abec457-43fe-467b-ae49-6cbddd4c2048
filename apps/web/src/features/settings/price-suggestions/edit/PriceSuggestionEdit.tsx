import { Alert, Paper, Stack } from '@mui/material';
import { enqueueSnackbar } from 'notistack';

import { MainLayout } from '@/components/core/layout/MainLayout';
import { tenantQueryKey, useAppContext } from '@/contexts/app-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { priceSuggestionRouters } from '@/router/routes/settings/price-suggestions';
import { Close, Edit } from '@mui/icons-material';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { getQueryKey } from '@trpc/react-query';
import { stringOrNull } from 'common';
import type { PriceSuggestion } from 'lambda-api';
import {
  type GradeState,
  RhfPriceSuggestion,
  createGradeStateSchema,
  gradeToState,
} from '../common/RhfPriceSuggestion';

import { useErrorMessageCreator } from '@/hooks/useErrorMessageCreator';

type Props = {
  priceSuggestion: PriceSuggestion;
};

export const PriceSuggestionEdit = ({ priceSuggestion }: Props) => {
  const id = priceSuggestion.id;
  const { labels } = useAppContext();
  const errorMessageCreator = useErrorMessageCreator();
  const GradeStateSchema = createGradeStateSchema(labels);
  const { control, formState, handleSubmit } = useForm<GradeState>({
    mode: 'onChange',
    defaultValues: gradeToState(priceSuggestion),
    resolver: zodResolver(GradeStateSchema),
  });

  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { error, mutate } = trpc.priceSuggestions.update.useMutation({
    onSuccess: async () => {
      const queryKeys = [tenantQueryKey, getQueryKey(trpc.priceSuggestions.get, { id })];
      await Promise.all(queryKeys.map((queryKey) => queryClient.invalidateQueries({ queryKey })));
      navigate({ to: '/settings/price-suggestions' });
      enqueueSnackbar(`${labels.assessment.price}を${labels.action.edit}しました`, {
        variant: 'success',
      });
    },
  });
  const title = priceSuggestionRouters.meta.edit.useTitle();
  const submit = async (input: GradeState) => {
    console.log('submit', input);
    mutate({ id, ...input, description: stringOrNull(input.description) });
  };

  return (
    <MainLayout title={title}>
      <Stack component="form" spacing={2} noValidate onSubmit={handleSubmit(submit)}>
        <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
          <RhfPriceSuggestion control={control} />
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {errorMessageCreator(error.message)}
            </Alert>
          )}
        </Stack>
        <TwoActionButtons
          primary={{
            type: 'submit',
            icon: Edit,
            label: labels.doing.save,
            loading: formState.isSubmitting,
          }}
          secondary={{
            icon: Close,
            end: true,
            label: labels.doing.cancel,
            onClick: () => navigate({ to: '/settings/price-suggestions' }),
          }}
          fullWidth
          spacing={2}
        />
      </Stack>
    </MainLayout>
  );
};
