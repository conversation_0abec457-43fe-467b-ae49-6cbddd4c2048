import { trpc } from '@/api';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { useParams } from '@tanstack/react-router';
import { isNullish } from 'common';
import { PriceSuggestionDetails } from './PriceSuggestionDetails';

export const PriceSuggestionDetailsPage = () => {
  const { id } = useParams({ from: '/app/settings/price-suggestions/$id' });
  const { data: priceSuggestion } = trpc.priceSuggestions.get.useQuery({ id });

  if (isNullish(priceSuggestion)) return <LoadingSpinner />;

  return <PriceSuggestionDetails priceSuggestion={priceSuggestion} />;
};
