import { Box, Button, Paper, Stack } from '@mui/material';

import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';
import { useForm } from 'react-hook-form';

import { trpc } from '@/api';
import { DeleteConfirmDialog } from '@/components/DeleteConfirmDialog';
import { RouterButton } from '@/components/RouterButton';
import { priceSuggestionRouters } from '@/router/routes/settings/price-suggestions';
import { Delete, Edit } from '@mui/icons-material';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { getQueryKey } from '@trpc/react-query';
import type { PriceSuggestion } from 'lambda-api';
import { enqueueSnackbar } from 'notistack';
import React from 'react';
import { type GradeState, RhfPriceSuggestion, gradeToState } from '../common/RhfPriceSuggestion';

type Props = {
  priceSuggestion: PriceSuggestion;
};

export const PriceSuggestionDetails = ({ priceSuggestion }: Props) => {
  const { labels } = useAppContext();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [openDialog, setOpenDialog] = React.useState(false);
  const { control } = useForm<GradeState>({
    mode: 'onChange',
    defaultValues: gradeToState(priceSuggestion),
  });

  const { mutateAsync } = trpc.priceSuggestions.delete.useMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getQueryKey(trpc.priceSuggestions.list) });
      navigate({ to: '/settings/price-suggestions' });
      enqueueSnackbar(`${labels.assessment.price}を削除しました`, { variant: 'success' });
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: 'error' });
    },
  });

  const title = priceSuggestionRouters.meta.details.useTitle();

  const handleConfirmDelete = () => mutateAsync({ id: priceSuggestion.id });

  return (
    <MainLayout title={title}>
      <>
        <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
          <RhfPriceSuggestion control={control} readOnly />
        </Stack>
        <Stack alignItems="end" sx={{ mt: 2 }}>
          <Box>
            <Button
              variant="text"
              startIcon={<Delete />}
              onClick={() => setOpenDialog(true)}
              sx={{ mr: 2 }}
            >
              {labels.doing.delete}
            </Button>
            <RouterButton
              to="/settings/price-suggestions/$id/edit"
              params={{ id: priceSuggestion.id }}
              startIcon={<Edit />}
              fullWidth={false}
            >
              {labels.doing.edit}
            </RouterButton>
          </Box>
        </Stack>
        <DeleteConfirmDialog
          open={openDialog}
          onClose={() => setOpenDialog(false)}
          onConfirm={handleConfirmDelete}
        />
      </>
    </MainLayout>
  );
};
