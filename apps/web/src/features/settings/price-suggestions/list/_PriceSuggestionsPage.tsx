import { trpc } from '@/api';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { tenantQueryKey, useAppContext } from '@/contexts/app-context';
import { DataGridToolbar } from '@/libs/mui-x/grid/DataGridToolbar';
import { useGridExport } from '@/libs/mui-x/grid/utils';
import { priceSuggestionRouters } from '@/router/routes/settings/price-suggestions';
import { Paper, Stack } from '@mui/material';
import type { GridRowParams } from '@mui/x-data-grid';
import type { GridColDef } from '@mui/x-data-grid';
import { useNavigate } from '@tanstack/react-router';
import type { PriceSuggestion } from 'lambda-api';
import { DataGrid } from 'mui-ex';

type ColDef = GridColDef<PriceSuggestion> & { field: keyof PriceSuggestion };

// 一覧ページで使用するカラム定義
export const useGradeColumnDefs = () => {
  const { labels } = useAppContext();
  return [
    {
      field: 'price',
      headerName: labels.p.defaultPrice,
      type: 'number',
    },
    {
      field: 'description',
      headerName: labels.system.description,
      width: 200,
    },
    {
      field: 'sortOrder',
      headerName: labels.system.sortOrder,
      type: 'number',
    },
  ] as const satisfies ColDef[];
};

const useGradeExportFormatter = () => {
  const columns = useGradeColumnDefs();
  return useGridExport(columns, priceSuggestionRouters.meta.list.useTitle());
};

const Toolbar = () => {
  const gradeExportFormatter = useGradeExportFormatter();
  return (
    <DataGridToolbar
      to="/settings/price-suggestions/create"
      queryKey={tenantQueryKey}
      exportFormatter={gradeExportFormatter}
    />
  );
};

export const GradesSettingsPage = () => {
  const { data: grades = [], isPending } = trpc.priceSuggestions.list.useQuery();
  const columns = useGradeColumnDefs();
  const navigate = useNavigate();
  const title = priceSuggestionRouters.meta.list.useTitle();
  const handleRowClick = (params: GridRowParams) => {
    navigate({ to: '/settings/price-suggestions/$id', params: { id: params.id.toString() } });
  };
  return (
    <MainLayout title={title} maxWidth="lg">
      <Stack component={Paper} sx={{ height: 1 }}>
        <DataGrid
          columns={columns}
          rows={grades}
          onRowClick={handleRowClick}
          loading={isPending}
          disableRowSelectionOnClick
          slots={{ toolbar: Toolbar }}
          sx={{
            // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
            '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
            '& .MuiTablePagination-select': { mr: 2 },
          }}
        />
      </Stack>
    </MainLayout>
  );
};
