import { Grid } from '@mui/material';

import { useAppContext } from '@/contexts/app-context';

import type { PriceSuggestion } from 'lambda-api';
import type { Dict } from 'models';
import { RhfTextField } from 'mui-ex';
import type { Control } from 'react-hook-form';
import { z } from 'zod';

export const createGradeStateSchema = (labels: Dict) =>
  z.object({
    id: z.string().optional(),
    price: z
      .number()
      .int()
      .nonnegative({ message: `${labels.p.defaultPrice}は0以上の数字で入力してください` }),
    description: z.string(),

    sortOrder: z.number().int().nonnegative({ message: '表示順は0以上の数字で入力してください' }),
  });
export type GradeState = z.infer<ReturnType<typeof createGradeStateSchema>>;

export const gradeToState = (suggestion: PriceSuggestion): GradeState => {
  return {
    id: suggestion.id,
    price: suggestion.price,
    description: suggestion.description ?? '',
    sortOrder: suggestion.sortOrder,
  };
};

type Props = {
  control: Control<GradeState>;
  readOnly?: boolean;
};

export const RhfPriceSuggestion = (props: Props) => {
  const { control, readOnly } = props;
  const { labels } = useAppContext();
  return (
    <Grid container spacing={2}>
      <Grid size={{ xs: 12, sm: 6 }}>
        <RhfTextField
          control={control}
          name="price"
          label={labels.p.defaultPrice}
          number
          unit="円"
          readOnly={readOnly}
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <RhfTextField
          control={control}
          name="description"
          label={labels.system.description}
          readOnly={readOnly}
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <RhfTextField
          control={control}
          name="sortOrder"
          label={labels.system.sortOrder}
          number
          readOnly={readOnly}
        />
      </Grid>
    </Grid>
  );
};
