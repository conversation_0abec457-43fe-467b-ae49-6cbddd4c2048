import { z } from 'zod';

import { BodyFieldSchema, ImageInputSchema } from 'common';
import { type Dict, ImageSettingsSchema } from 'models';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { Collapse, Stack } from '@mui/material';
import { RhfCheckbox } from 'mui-ex';
import { type Control, type UseFormWatch, useForm } from 'react-hook-form';
import { SettingsFormPanel } from '../common/settings-panel/SettingsFormPanel';
import type { ContentsProps } from '../common/settings-panel/types';
import { RhfImagesSettings } from '../components/rhf-images-settings/_RhfImagesSettings';
import { uploadGuideImageToS3AndSort } from '../funcs';
import { RhfAdditionalFields } from './rhf-additional-fields/RhfAdditionalFields';
import {
  stateToAdditionalFieldsInput,
  superFineAdditionalFields,
} from './rhf-additional-fields/funcs';

const useStateSchema = (labels2: Dict) =>
  z
    .object({
      allow: z.boolean(),
      requiredBicycleType: z.boolean(),
      additionalBodyFields: z.array(BodyFieldSchema),
      image: ImageSettingsSchema,
    })
    .superRefine((arg, ctx) => {
      if (!arg.requiredBicycleType) {
        superFineAdditionalFields(arg, ctx, labels2);
      }
    });

type State = z.infer<ReturnType<typeof useStateSchema>>;

const useDefaultValues = () => {
  const { tenant } = useAppContext();
  return {
    allow: tenant.ensureAbandoned.allow,
    requiredBicycleType: tenant.ensureAbandoned.requiredBicycleType,
    additionalBodyFields: tenant.ensureAbandoned.additionalBodyFields,
    image: tenant.ensureAbandoned.image,
  } satisfies State;
};

const useSettingsFrom = () => {
  const { labels } = useAppContext();
  const StateSchema = useStateSchema(labels);
  return useForm({
    mode: 'onChange',
    defaultValues: useDefaultValues(),
    resolver: zodResolver(StateSchema),
  });
};

type InnerProps = ContentsProps & {
  control: Control<State>;
  watch: UseFormWatch<State>;
};

const RhfContents = (props: ContentsProps) => {
  const { control, watch, readOnly } = props as unknown as InnerProps;
  const { labels } = useAppContext();
  const allow = watch('allow');

  return (
    <Stack spacing={2} sx={{ p: 2 }}>
      <RhfCheckbox
        control={control}
        name="allow"
        label={`${labels.et.ensureAbandoned}機能を使用する`}
        helperText={`${labels.et.remove}前に${labels.domain.abandoned}されているかどうかを記録する機能です。`}
      />
      <Collapse in={allow}>
        <RhfAdditionalFields control={control} watch={watch} readOnly={readOnly} />
        <RhfImagesSettings control={control} watch={watch} readOnly={readOnly} />
      </Collapse>
    </Stack>
  );
};

export const EnsureAbandonedSettings = () => {
  const { labels } = useAppContext();
  const title = `${labels.et.ensureAbandoned}${labels.system.settings}` as const;

  const { mutateAsync } = trpc.tenant.settings.patrol.ensureAbandoned.useMutation();
  const handleSubmit = async (data: State) => {
    if (!data.allow) {
      return mutateAsync({
        allow: false,
        requiredBicycleType: false,
        additionalBodyFields: [],
        image: { min: 0, max: 0, guides: [] },
      });
    }
    const images = await uploadGuideImageToS3AndSort('ensureAbandoned', data.image.guides);
    return mutateAsync({
      allow: true,
      ...stateToAdditionalFieldsInput(data),
      image: { ...data.image, guides: z.array(ImageInputSchema).parse(images) },
    });
  };

  return (
    <SettingsFormPanel
      title={title}
      contents={RhfContents}
      useSettingsFrom={useSettingsFrom}
      onSubmit={handleSubmit}
    />
  );
};
