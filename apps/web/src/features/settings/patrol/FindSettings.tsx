import { z } from 'zod';

import { BodyFieldSchema, ImageInputSchema } from 'common';
import { type Dict, ImageSettingsSchema } from 'models';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { Stack } from '@mui/material';
import { type Control, type UseFormWatch, useForm } from 'react-hook-form';
import { SettingsFormPanel } from '../common/settings-panel/SettingsFormPanel';
import type { ContentsProps } from '../common/settings-panel/types';
import { RhfImagesSettings } from '../components/rhf-images-settings/_RhfImagesSettings';
import { uploadGuideImageToS3AndSort } from '../funcs';
import { RhfAdditionalFields } from './rhf-additional-fields/RhfAdditionalFields';
import {
  stateToAdditionalFieldsInput,
  superFineAdditionalFields,
} from './rhf-additional-fields/funcs';

const useStateSchema = (labels: Dict) =>
  z
    .object({
      requiredBicycleType: z.boolean(),
      additionalBodyFields: z.array(BodyFieldSchema),
      image: ImageSettingsSchema,
    })
    .superRefine((arg, ctx) => {
      if (!arg.requiredBicycleType) {
        superFineAdditionalFields(arg, ctx, labels);
      }
    });
type State = z.infer<ReturnType<typeof useStateSchema>>;

const useDefaultValues = () => {
  const { tenant } = useAppContext();
  return {
    requiredBicycleType: tenant.find.requiredBicycleType,
    additionalBodyFields: tenant.find.additionalBodyFields,
    image: tenant.find.image,
  } satisfies State;
};

const useSettingsFrom = () => {
  const { labels } = useAppContext();
  const StateSchema = useStateSchema(labels);
  return useForm({
    mode: 'onChange',
    defaultValues: useDefaultValues(),
    resolver: zodResolver(StateSchema),
  });
};

type InnerProps = ContentsProps & {
  control: Control<State>;
  watch: UseFormWatch<State>;
};

const RhfContents = (props: ContentsProps) => {
  const { control, watch, readOnly } = props as unknown as InnerProps;
  return (
    <Stack spacing={2} sx={{ p: 2 }}>
      <RhfAdditionalFields control={control} watch={watch} readOnly={readOnly} />
      <RhfImagesSettings control={control} watch={watch} readOnly={readOnly} />
    </Stack>
  );
};

export const FindSettings = () => {
  const { labels } = useAppContext();
  const title = `${labels.et.find}${labels.system.settings}` as const;

  const { mutateAsync } = trpc.tenant.settings.patrol.find.useMutation();
  const handleSubmit = async (data: State) => {
    const images = await uploadGuideImageToS3AndSort('find', data.image.guides);
    await mutateAsync({
      ...stateToAdditionalFieldsInput(data),
      image: { ...data.image, guides: z.array(ImageInputSchema).parse(images) },
    });
  };

  return (
    <SettingsFormPanel
      title={title}
      contents={RhfContents}
      useSettingsFrom={useSettingsFrom}
      onSubmit={handleSubmit}
    />
  );
};
