import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';

import { patrolMeta } from '@/router/routes/settings/patrol-route';
import { Collapse, Stack } from '@mui/material';
import { BaseSettings } from './BaseSettings';
import { EnsureAbandonedSettings } from './EnsureAbandonedSettings';
import { FindSettings } from './FindSettings';
import { MarkSettings } from './MarkSettings';
import { RemoveSettings } from './RemoveSettings';

export const PatrolSettingsPage = () => {
  const { tenant } = useAppContext();

  const title = patrolMeta.useTitle();

  return (
    <MainLayout scrollable title={title}>
      <Stack spacing={2}>
        <BaseSettings />
        <Collapse in={tenant.patrol.flow === 'find'}>
          <Stack spacing={2}>
            <FindSettings />
            <EnsureAbandonedSettings />
          </Stack>
        </Collapse>
        <Collapse in={tenant.patrol.flow === 'mark'}>
          <MarkSettings />
        </Collapse>
        <RemoveSettings />
      </Stack>
    </MainLayout>
  );
};
