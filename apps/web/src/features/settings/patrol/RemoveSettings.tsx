import { z } from 'zod';

import { BodyFieldSchema, ImageInputSchema, ImageSchema } from 'common';
import type { Dict } from 'models';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { Stack } from '@mui/material';
import { type Control, type UseFormWatch, useForm } from 'react-hook-form';
import { SettingsFormPanel } from '../common/settings-panel/SettingsFormPanel';
import type { ContentsProps } from '../common/settings-panel/types';
import { RhfImagesSettings } from '../components/rhf-images-settings/_RhfImagesSettings';
import { uploadGuideImageToS3AndSort } from '../funcs';
import { RhfAdditionalFields } from './rhf-additional-fields/RhfAdditionalFields';
import {
  stateToAdditionalFieldsInput,
  superFineAdditionalFields,
} from './rhf-additional-fields/funcs';

const ImageSettingsSchema = z.object({
  min: z.number(),
  max: z.number(),
  guides: z.array(ImageSchema),
});

const useStateSchema = (labels: Dict) =>
  z
    .object({
      requiredBicycleType: z.boolean(),
      additionalBodyFields: z.array(BodyFieldSchema),
      image: ImageSettingsSchema,
    })
    .superRefine((arg, ctx) => {
      if (!arg.requiredBicycleType) {
        superFineAdditionalFields(arg, ctx, labels);
      }
    });

type State = z.infer<ReturnType<typeof useStateSchema>>;

const useDefaultValues = () => {
  const { tenant } = useAppContext();
  return {
    requiredBicycleType: tenant.remove.requiredBicycleType,
    additionalBodyFields: tenant.remove.additionalBodyFields,
    image: tenant.remove.image,
  } satisfies State;
};

const useSettingsFrom = () => {
  const { labels } = useAppContext();
  const StateSchema = useStateSchema(labels);
  return useForm({
    mode: 'onChange',
    defaultValues: useDefaultValues(),
    resolver: zodResolver(StateSchema),
  });
};

type InnerProps = ContentsProps & {
  control: Control<State>;
  watch: UseFormWatch<State>;
};

const RhfContents = (props: ContentsProps) => {
  const { control, watch, readOnly } = props as unknown as InnerProps;
  return (
    <Stack spacing={2} sx={{ p: 2 }}>
      <RhfAdditionalFields control={control} watch={watch} readOnly={readOnly} />
      <RhfImagesSettings control={control} watch={watch} readOnly={readOnly} />
    </Stack>
  );
};

export const RemoveSettings = () => {
  const { labels } = useAppContext();
  const title = `${labels.et.remove}${labels.system.settings}` as const;

  const { mutateAsync } = trpc.tenant.settings.patrol.remove.useMutation();
  const handleSubmit = async (data: State) => {
    const images = await uploadGuideImageToS3AndSort('remove', data.image.guides);
    return mutateAsync({
      ...stateToAdditionalFieldsInput(data),
      image: { ...data.image, guides: z.array(ImageInputSchema).parse(images) },
    });
  };

  return (
    <SettingsFormPanel
      title={title}
      contents={RhfContents}
      useSettingsFrom={useSettingsFrom}
      onSubmit={handleSubmit}
    />
  );
};
