import { z } from 'zod';

import { ImageInputSchema } from 'common';
import { ImageSettingsSchema } from 'models';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { type Control, type UseFormWatch, useForm } from 'react-hook-form';
import { SettingsFormPanel } from '../common/settings-panel/SettingsFormPanel';
import type { ContentsProps } from '../common/settings-panel/types';
import { RhfImagesSettings } from '../components/rhf-images-settings/_RhfImagesSettings';
import { uploadGuideImageToS3AndSort } from '../funcs';

const StateSchema = z.object({ image: ImageSettingsSchema });

type State = z.infer<typeof StateSchema>;

const useDefaultValues = () => {
  const { tenant } = useAppContext();
  return {
    image: tenant.mark.image,
  } satisfies State;
};

const useSettingsFrom = () =>
  useForm({
    mode: 'onChange',
    defaultValues: useDefaultValues(),
    resolver: zodResolver(StateSchema),
  });

type InnerProps = ContentsProps & {
  control: Control<State>;
  watch: UseFormWatch<State>;
};

const RhfContents = (props: ContentsProps) => {
  const { control, watch, readOnly } = props as unknown as InnerProps;
  return <RhfImagesSettings control={control} watch={watch} readOnly={readOnly} />;
};

export const MarkSettings = () => {
  const { labels } = useAppContext();
  // 開発側としては find と mark を区別するが、ユーザー側では「巡回」の設定として提供される
  const title = `${labels.et.find}${labels.system.settings}` as const;

  const { mutateAsync } = trpc.tenant.settings.patrol.mark.useMutation();
  const handleSubmit = async (data: State) => {
    const images = await uploadGuideImageToS3AndSort('mark', data.image.guides);
    return mutateAsync({ ...data.image, guides: z.array(ImageInputSchema).parse(images) });
  };

  return (
    <SettingsFormPanel
      title={title}
      contents={RhfContents}
      useSettingsFrom={useSettingsFrom}
      onSubmit={handleSubmit}
    />
  );
};
