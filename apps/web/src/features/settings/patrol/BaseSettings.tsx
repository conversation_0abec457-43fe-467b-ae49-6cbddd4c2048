import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Stack } from '@mui/material';
import { type Control, type UseFormWatch, useForm } from 'react-hook-form';
import { z } from 'zod';

import { PatrolFlowSchema } from 'common';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { RhfRadioGroup } from 'mui-ex';
import { SettingsFormPanel } from '../common/settings-panel/SettingsFormPanel';
import type { ContentsProps } from '../common/settings-panel/types';

const StateSchema = z.object({ flow: PatrolFlowSchema });

type State = z.infer<typeof StateSchema>;

const useDefaultValues = () => {
  const { tenant } = useAppContext();
  return { flow: tenant.patrol.flow } satisfies State;
};

const useSettingsFrom = () =>
  useForm({
    mode: 'onChange',
    defaultValues: useDefaultValues(),
    resolver: zodResolver(StateSchema),
  });

type InnerProps = ContentsProps & {
  control: Control<State>;
  watch: UseFormWatch<State>;
};

const RhfContents = (props: ContentsProps) => {
  const { control, readOnly } = props as unknown as InnerProps;
  const { labels } = useAppContext();
  return (
    <Stack spacing={2} sx={{ p: 2 }}>
      <RhfRadioGroup
        control={control}
        name="flow"
        readOnly={readOnly}
        options={PatrolFlowSchema.options.map((value) => ({
          value,
          label: labels.patrolFlow[value],
        }))}
      />
    </Stack>
  );
};

export const BaseSettings = () => {
  const { labels } = useAppContext();
  const title = `${labels.da.patrol}基本${labels.system.settings}` as const;

  const { mutateAsync } = trpc.tenant.settings.patrol.base.useMutation();
  const handleSubmit = async (data: State) => mutateAsync(data);

  return (
    <SettingsFormPanel
      title={title}
      contents={RhfContents}
      useSettingsFrom={useSettingsFrom}
      onSubmit={handleSubmit}
    />
  );
};
