import type { Dict } from 'models';
import { z } from 'zod';

import type { BodyField } from 'common';

export type AdditionalFields = {
  requiredBicycleType: boolean;
  additionalBodyFields: BodyField[];
};

export const superFineAdditionalFields = <T extends AdditionalFields>(
  arg: T,
  ctx: z.RefinementCtx,
  labels: Dict,
) => {
  if (!arg.requiredBicycleType) {
    if (arg.additionalBodyFields.includes('registrationNumber')) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['additionalBodyFields'],
        message: [
          labels.body.type,
          'を選択しない場合は',
          labels.body.registrationNumber,
          'を追加できません。',
        ].join(''),
      });
    }
    if (arg.additionalBodyFields.includes('serialNumber')) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['additionalBodyFields'],
        message: [
          labels.body.type,
          'を選択しない場合は',
          labels.body.serialNumber,
          'を追加できません。',
        ].join(''),
      });
    }
    if (arg.additionalBodyFields.includes('numberPlate')) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['additionalBodyFields'],
        message: [
          labels.body.type,
          'を選択しない場合は',
          labels.body.numberPlate,
          'を追加できません。',
        ].join(''),
      });
    }
  }
};

export const stateToAdditionalFieldsInput = <T extends AdditionalFields>({
  requiredBicycleType,
  additionalBodyFields,
}: T): AdditionalFields => {
  if (!requiredBicycleType)
    return {
      requiredBicycleType,
      additionalBodyFields,
    };
  return {
    requiredBicycleType,
    additionalBodyFields,
  };
};
