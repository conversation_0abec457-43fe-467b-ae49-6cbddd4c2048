import { useAppContext } from '@/contexts/app-context';

import { Stack } from '@mui/material';

import { RhfCheckbox, RhfMultipleSelect } from 'mui-ex';
import type { Control, UseFormWatch } from 'react-hook-form';

import { type BodyField, BodyFieldSchema } from 'common';
import type { AdditionalFields } from './funcs';

type Props<T extends AdditionalFields> = {
  control: Control<T>;
  watch: UseFormWatch<T>;
  readOnly: boolean;
};
type InnerProps = {
  control: Control<AdditionalFields>;
  watch: UseFormWatch<AdditionalFields>;
  readOnly: boolean;
};

export const RhfAdditionalFields = <T extends AdditionalFields>(props: Props<T>) => {
  const { control, watch, readOnly } = props as unknown as InnerProps;
  const { labels } = useAppContext();

  const requiredBicycleType = watch('requiredBicycleType');

  return (
    <Stack spacing={2}>
      <RhfCheckbox
        control={control}
        name="requiredBicycleType"
        label={`${labels.body.type}を入力する`}
        helperText={`${labels.body.type}を入力する場合のみ、防犯登録番号、ナンバープレートなどの項目が入力可能です。`}
        readOnly={readOnly}
      />
      <RhfMultipleSelect
        control={control}
        name="additionalBodyFields"
        label={`${labels.p.bodyInfo}の入力項目`}
        options={BodyFieldSchema.options
          .filter((o) => {
            if (requiredBicycleType) return true;
            const requiredTypeField: BodyField[] = [
              'registrationNumber',
              'serialNumber',
              'numberPlate',
            ];
            return !requiredTypeField.includes(o);
          })
          .map((o) => ({
            label: labels.bodyField[o],
            value: o,
          }))}
        readOnly={readOnly}
      />
    </Stack>
  );
};
