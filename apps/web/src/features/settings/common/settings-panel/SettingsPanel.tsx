import { ExpandLess, ExpandMore } from '@mui/icons-material';
import { Box, Collapse, Divider, Fade, IconButton, Paper, Stack, Typography } from '@mui/material';
import React from 'react';

type Props = {
  title: string;
  actions?: React.ReactElement<unknown, any>;
  children: React.ReactNode;
};

export const SettingsPanel = ({ title, children, actions }: Props) => {
  const [expanded, setExpanded] = React.useState(false);
  return (
    <Paper>
      <Stack direction="row" alignItems="center" spacing={1} sx={{ p: 2 }}>
        <Stack direction="row" alignItems="end" spacing={1}>
          <Typography variant="h5">{title}</Typography>
        </Stack>
        <Box sx={{ flexGrow: 1 }} />
        {actions && <Fade in={expanded}>{actions}</Fade>}
        <Box>
          <IconButton onClick={() => setExpanded(!expanded)}>
            {!expanded ? <ExpandMore /> : <ExpandLess />}
          </IconButton>
        </Box>
      </Stack>
      <Collapse in={expanded}>
        <Divider />
        {children}
      </Collapse>
    </Paper>
  );
};
