import { enqueueSnackbar } from 'notistack';

import { LoadingButton } from '@mui/lab';
import { <PERSON>ton, Collapse, Stack } from '@mui/material';
import type React from 'react';
import type { FieldValues, UseFormReturn } from 'react-hook-form';

import { tenantQueryKey, useAppContext } from '@/contexts/app-context';
import { useQueryClient } from '@tanstack/react-query';
import type { ContentsProps } from './types';

type Props<S extends FieldValues> = {
  title: string;
  contents: React.JSXElementConstructor<ContentsProps>;
  open: boolean;
  onClose: () => void;
  useSettingsFrom: () => UseFormReturn<S>;
  onSubmit: (data: S) => Promise<any>;
};

export const SettingsForm = <S extends FieldValues>({
  title,
  contents,
  open,
  onClose,
  useSettingsFrom,
  onSubmit,
}: Props<S>) => {
  const { labels } = useAppContext();
  const { control, watch, formState, handleSubmit, reset } = useSettingsFrom();

  const queryClient = useQueryClient();
  const submit = async (data: any) => {
    await onSubmit(data);
    await queryClient.invalidateQueries({ queryKey: tenantQueryKey });
    reset(data);
    onClose();
    enqueueSnackbar(`${title}を${labels.action.save}しました。`, { variant: 'success' });
  };
  const handleCancel = () => {
    reset();
    onClose();
  };

  const Contents = contents;
  return (
    <Stack component="form" noValidate onSubmit={handleSubmit(submit)}>
      <Contents control={control} watch={watch} readOnly={!open} />
      <Collapse in={open}>
        <Stack direction="row" justifyContent="flex-end" spacing={1} sx={{ p: 2, pt: 0 }}>
          <Button variant="outlined" onClick={handleCancel}>
            {labels.doing.cancel}
          </Button>
          <LoadingButton variant="contained" type="submit" loading={formState.isSubmitting}>
            {labels.doing.save}
          </LoadingButton>
        </Stack>
      </Collapse>
    </Stack>
  );
};
