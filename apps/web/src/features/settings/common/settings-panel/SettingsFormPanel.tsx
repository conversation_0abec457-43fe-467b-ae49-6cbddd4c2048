import { useAppContext } from '@/contexts/app-context';
import { Edit, ExpandLess, ExpandMore } from '@mui/icons-material';
import {
  Box,
  Button,
  Collapse,
  Divider,
  Fade,
  IconButton,
  Paper,
  Stack,
  Typography,
} from '@mui/material';
import React from 'react';
import type { FieldValues, UseFormReturn } from 'react-hook-form';
import { SettingsForm } from './SettingsForm';
import type { ContentsProps } from './types';

type Props<S extends FieldValues> = {
  title: string;
  contents: React.JSXElementConstructor<ContentsProps>;
  useSettingsFrom: () => UseFormReturn<S>;
  onSubmit: (data: S) => Promise<any>;
};

export const SettingsFormPanel = <S extends FieldValues>({
  title,
  contents,
  useSettingsFrom,
  onSubmit,
}: Props<S>) => {
  const { labels } = useAppContext();
  const [expanded, setExpanded] = React.useState(false);
  const [editing, setEditing] = React.useState(false);

  const handleStartEditing = () => setEditing(true);
  const handleEndEditing = () => setEditing(false);

  return (
    <Paper>
      <Stack direction="row" alignItems="center" spacing={1} sx={{ p: 2 }}>
        <Stack direction="row" alignItems="end" spacing={1}>
          <Typography variant="h5">{title}</Typography>
        </Stack>
        <Box sx={{ flexGrow: 1 }} />
        <Fade in={expanded && !editing}>
          <Box>
            <Button endIcon={<Edit />} onClick={handleStartEditing}>
              {labels.doing.edit}
            </Button>
          </Box>
        </Fade>
        <Fade in={!editing}>
          <Box>
            <IconButton onClick={() => setExpanded(!expanded)}>
              {!expanded ? <ExpandMore /> : <ExpandLess />}
            </IconButton>
          </Box>
        </Fade>
      </Stack>
      <Collapse in={expanded}>
        <Divider />
        <SettingsForm
          title={title}
          contents={contents}
          open={editing}
          onClose={handleEndEditing}
          useSettingsFrom={useSettingsFrom}
          onSubmit={onSubmit}
        />
      </Collapse>
    </Paper>
  );
};
