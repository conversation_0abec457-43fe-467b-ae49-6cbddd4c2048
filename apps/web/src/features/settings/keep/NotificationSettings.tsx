import { type Control, type UseFormWatch, useForm } from 'react-hook-form';
import { z } from 'zod';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { Stack } from '@mui/material';
import { RhfStepNumber } from 'mui-ex';
import { SettingsFormPanel } from '../common/settings-panel/SettingsFormPanel';
import type { ContentsProps } from '../common/settings-panel/types';

const StateSchema = z.object({
  deadlineDays: z.number().int(),
});

type State = z.infer<typeof StateSchema>;

const useDefaultValues = () => {
  const { tenant } = useAppContext();
  return {
    deadlineDays: tenant.notification.deadlineDays,
  } satisfies State;
};

const useSettingsFrom = () =>
  useForm({
    mode: 'onChange',
    defaultValues: useDefaultValues(),
    resolver: zodResolver(StateSchema),
  });

type InnerProps = ContentsProps & {
  control: Control<State>;
  watch: UseFormWatch<State>;
};

const RhfContents = (props: ContentsProps) => {
  const { control, readOnly } = props as unknown as InnerProps;
  return (
    <Stack spacing={2} sx={{ p: 2 }}>
      <RhfStepNumber
        control={control}
        name="deadlineDays"
        label="通知後の最低保管日数"
        helperText="通知書を発行した日付からこの日数を最小の保管期限として扱います。"
        readOnly={readOnly}
      />
    </Stack>
  );
};

export const NotificationSettings = () => {
  const { labels } = useAppContext();
  const title = `${labels.domain.storageNotice}${labels.system.settings}` as const;

  const { mutateAsync } = trpc.tenant.settings.keep.notification.useMutation();
  const handleSubmit = async (data: State) => mutateAsync(data);

  return (
    <SettingsFormPanel
      title={title}
      contents={RhfContents}
      useSettingsFrom={useSettingsFrom}
      onSubmit={handleSubmit}
    />
  );
};
