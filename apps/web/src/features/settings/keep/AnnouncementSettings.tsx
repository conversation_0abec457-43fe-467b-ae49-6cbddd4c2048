import { type Control, type UseFormWatch, useForm } from 'react-hook-form';
import { z } from 'zod';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { Stack, Typography } from '@mui/material';
import { AnnouncementFlowSchema } from 'common';
import { RhfRadioGroup, RhfStepNumber } from 'mui-ex';
import { SettingsFormPanel } from '../common/settings-panel/SettingsFormPanel';
import type { ContentsProps } from '../common/settings-panel/types';

const StateSchema = z.object({
  flow: AnnouncementFlowSchema,
  days: z.number().int().min(0),
});

type State = z.infer<typeof StateSchema>;

const useDefaultValues = () => {
  const { tenant } = useAppContext();
  return {
    flow: tenant.announcement.flow,
    days: tenant.announcement.days,
  } satisfies State;
};

const useSettingsFrom = () =>
  useForm({
    mode: 'onChange',
    defaultValues: useDefaultValues(),
    resolver: zodResolver(StateSchema),
  });

type InnerProps = ContentsProps & {
  control: Control<State>;
  watch: UseFormWatch<State>;
};

const RhfContents = (props: ContentsProps) => {
  const { control, readOnly } = props as unknown as InnerProps;
  const { labels } = useAppContext();
  return (
    <Stack spacing={2} sx={{ p: 2 }}>
      <Stack spacing={1}>
        <Typography variant="h6">{`${labels.da.announce}の方法`}</Typography>
        <RhfRadioGroup
          control={control}
          name="flow"
          readOnly={readOnly}
          options={AnnouncementFlowSchema.options.map((value) => ({
            value,
            label: labels.announcementFlow[value],
          }))}
        />
      </Stack>
      <Stack spacing={1}>
        <RhfStepNumber
          control={control}
          name="days"
          label={`${labels.da.announce}期間`}
          helperText={`${labels.da.announce}期間は${labels.da.announce}した当日を含む日数を${labels.system.settings}してください。`}
          readOnly={readOnly}
        />
      </Stack>
    </Stack>
  );
};

export const AnnouncementSettings = () => {
  const { labels } = useAppContext();
  const title = `${labels.da.announce}${labels.system.settings}` as const;

  const { mutateAsync } = trpc.tenant.settings.keep.announcement.useMutation();
  const handleSubmit = async (data: State) => mutateAsync(data);

  return (
    <SettingsFormPanel
      title={title}
      contents={RhfContents}
      useSettingsFrom={useSettingsFrom}
      onSubmit={handleSubmit}
    />
  );
};
