import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { Stack, Typography } from '@mui/material';
import { type PoliceReferenceFileType, PoliceReferenceFileTypeSchema } from 'common';
import { RhfMultiNumberField, RhfRadioGroup } from 'mui-ex';
import { type Control, type UseFormWatch, useForm } from 'react-hook-form';
import { z } from 'zod';
import { SettingsFormPanel } from '../common/settings-panel/SettingsFormPanel';
import type { ContentsProps } from '../common/settings-panel/types';

const StateSchema = z.object({
  fileType: PoliceReferenceFileTypeSchema,
  csvIgnoreIndexes: z.array(z.number()),
  csvIgnoreLastIndexes: z.array(z.number()),
  xlsxIgnoreIndexes: z.array(z.number()),
  xlsxIgnoreLastIndexes: z.array(z.number()),
});

type State = z.infer<typeof StateSchema>;

const useDefaultValues = () => {
  const { tenant } = useAppContext();
  return {
    fileType: tenant.policeReference.fileType,
    csvIgnoreIndexes: tenant.policeReference.csvIgnoreIndexes,
    csvIgnoreLastIndexes: tenant.policeReference.csvIgnoreLastIndexes,
    xlsxIgnoreIndexes: tenant.policeReference.xlsxIgnoreIndexes,
    xlsxIgnoreLastIndexes: tenant.policeReference.xlsxIgnoreLastIndexes,
  } satisfies State;
};

const useSettingsFrom = () =>
  useForm({
    mode: 'onChange',
    defaultValues: useDefaultValues(),
    resolver: zodResolver(StateSchema),
  });

type InnerProps = ContentsProps & {
  control: Control<State>;
  watch: UseFormWatch<State>;
};

const requiredCsv = (fileType: PoliceReferenceFileType) => fileType === 'any' || fileType === 'csv';

const requiredExcel = (fileType: PoliceReferenceFileType) =>
  fileType === 'any' || fileType === 'xlsx';

const RhfContents = (props: ContentsProps) => {
  const { control, watch, readOnly } = props as unknown as InnerProps;
  const { labels } = useAppContext();
  const fileType = watch('fileType') as PoliceReferenceFileType;
  return (
    <Stack spacing={2} sx={{ p: 2 }}>
      <RhfRadioGroup
        control={control}
        name="fileType"
        label={`${labels.p.referPolice}に使用するファイル形式`}
        options={PoliceReferenceFileTypeSchema.options.map((option) => ({
          label: labels.policeReferenceFileType[option],
          value: option,
        }))}
        row
        readOnly={readOnly}
      />
      {requiredCsv(fileType) && (
        <Stack spacing={1}>
          <Typography variant="subtitle1">{`回答${labels.system.csv}の無視する行の指定`}</Typography>
          <Stack direction="row" spacing={2}>
            <RhfMultiNumberField
              control={control}
              name="csvIgnoreIndexes"
              label="先頭から数えた行（複数可）"
              readOnly={readOnly}
            />
            <RhfMultiNumberField
              control={control}
              name="csvIgnoreLastIndexes"
              label="末尾から数えた行（複数可）"
              readOnly={readOnly}
            />
          </Stack>
        </Stack>
      )}
      {requiredExcel(fileType) && (
        <Stack spacing={1}>
          <Typography variant="subtitle1">{`回答${labels.system.xlsx}の無視する行の指定`}</Typography>
          <Stack direction="row" spacing={2}>
            <RhfMultiNumberField
              control={control}
              name="xlsxIgnoreIndexes"
              label="先頭から数えた行（複数可）"
              readOnly={readOnly}
            />
            <RhfMultiNumberField
              control={control}
              name="xlsxIgnoreLastIndexes"
              label="末尾から数えた行（複数可）"
              readOnly={readOnly}
            />
          </Stack>
        </Stack>
      )}
    </Stack>
  );
};

export const PoliceReferenceSettings = () => {
  const { labels } = useAppContext();
  const title = `${labels.p.referPolice}${labels.system.settings}` as const;

  const { mutateAsync } = trpc.tenant.settings.keep.policeReference.useMutation();
  const handleSubmit = async ({ fileType, ...data }: State) =>
    mutateAsync({
      fileType,
      csvIgnoreIndexes: requiredCsv(fileType) ? data.csvIgnoreIndexes : [],
      csvIgnoreLastIndexes: requiredCsv(fileType) ? data.csvIgnoreLastIndexes : [],
      xlsxIgnoreIndexes: requiredExcel(fileType) ? data.xlsxIgnoreIndexes : [],
      xlsxIgnoreLastIndexes: requiredExcel(fileType) ? data.xlsxIgnoreLastIndexes : [],
    });

  return (
    <SettingsFormPanel
      title={title}
      contents={RhfContents}
      useSettingsFrom={useSettingsFrom}
      onSubmit={handleSubmit}
    />
  );
};
