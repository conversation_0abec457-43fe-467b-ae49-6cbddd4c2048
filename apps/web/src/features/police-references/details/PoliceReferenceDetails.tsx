import React from 'react';

import { Close, Upload } from '@mui/icons-material';
import { Button, Dialog, Grid, Paper, Stack } from '@mui/material';

import type { PoliceReference } from 'lambda-api';
import { DataGrid, ReadOnlyField } from 'mui-ex';

import { imagesCol, removedAtCol, serialNoCol, statusCol } from '@/components/bicycles/columns';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { createUseColumns } from '@/hooks/useColumns';
import {
  downloadPoliceRequestCsv,
  downloadPoliceRequestExcel,
  downloadPoliceResponseCsv,
  downloadPoliceResponseExcel,
} from '@/models/reference';
import { policeReferenceDetailsRoute } from '@/router/routes/keep/police-references';

import { useAppContext } from '@/contexts/app-context';
import { datetimeFormatter } from '@/funcs/date';
import { referenceByCol } from '../columns';
import { ReferenceStatusChip } from '../common/ReferenceStatusChip';
import { CancelDialog } from './CancelDialog';
import { DownloadMenuButton } from './DownloadMenuButton';
import { UploadResponse } from './UploadResponse';

const useColumns = createUseColumns([
  imagesCol,
  removedAtCol,
  statusCol,
  serialNoCol,
  referenceByCol,
]);

type Props = {
  reference: PoliceReference;
};

export const PoliceReferenceDetails = ({ reference }: Props) => {
  const { policeStations } = useAppContext();
  const columns = useColumns(reference.bicycles);
  const [open, setOpen] = React.useState(false);
  const [cancelOpen, setCancelOpen] = React.useState(false);

  const title = policeReferenceDetailsRoute.meta.useTitle();

  const police = policeStations.find((p) => p.prefectureCode === reference.prefectureCode);
  if (police === undefined) throw new Error('都道府県が見つかりません');
  return (
    <MainLayout scrollable title={title} maxWidth="xl">
      <Stack component={Paper} spacing={2} sx={{ height: 1, p: 2 }}>
        <Grid container spacing={2}>
          <Grid size={{ xs: 6, sm: 6, md: 4 }}>
            <ReadOnlyField label="都道府県" value={police.prefecture} />
          </Grid>
          <Grid size={{ xs: 6, sm: 6, md: 4 }} sx={{ display: 'flex', alignItems: 'flex-end' }}>
            <ReferenceStatusChip status={reference.status} />
          </Grid>
          <Grid size={{ xs: 12 }} />
          <Grid size={{ xs: 12, sm: 6, md: 4 }}>
            <ReadOnlyField label="依頼日時" value={datetimeFormatter(reference.requestedAt)} />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4 }} sx={{ display: 'flex', alignItems: 'flex-end' }}>
            <DownloadMenuButton
              title="依頼ファイルダウンロード"
              csvAction={() => downloadPoliceRequestCsv(reference)}
              excelAction={() => downloadPoliceRequestExcel(reference)}
              disabled={reference.status === 'canceled'}
            />
          </Grid>
          <Grid size={{ xs: 12 }} />
          <Grid size={{ xs: 12, sm: 6, md: 4 }}>
            <ReadOnlyField
              label="回答日時"
              value={datetimeFormatter(reference.respondedAt, '未回答')}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4 }} sx={{ display: 'flex', alignItems: 'flex-end' }}>
            {reference.status === 'request' && (
              <Button
                variant="contained"
                endIcon={<Upload />}
                onClick={() => setOpen(true)}
                fullWidth
              >
                回答ファイルアップロード
              </Button>
            )}
            {reference.status === 'response' && (
              <DownloadMenuButton
                title="回答ファイルダウンロード"
                csvAction={() => downloadPoliceResponseCsv(reference)}
                excelAction={() => downloadPoliceResponseExcel(reference)}
              />
            )}
          </Grid>
        </Grid>
        <Dialog open={open} onClose={() => setOpen(false)} maxWidth={false}>
          <UploadResponse
            reference={reference}
            onClose={() => setOpen(false)}
            prefecture={police.prefecture}
          />
        </Dialog>
        <Stack sx={{ width: 1, flexGrow: 1, overflow: 'auto' }}>
          <DataGrid
            columns={columns}
            rows={reference.bicycles}
            disableRowSelectionOnClick
            hideFooterSelectedRowCount
            sx={{
              // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
              '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
              '& .MuiTablePagination-select': { mr: 2 },
              '& .MuiDataGrid-row:hover': { backgroundColor: 'inherit' },
            }}
          />
        </Stack>
        <Stack alignItems="end">
          <Button
            variant="text"
            startIcon={<Close />}
            onClick={() => setCancelOpen(true)}
            disabled={reference.status !== 'request'}
          >
            警察照会をキャンセルする
          </Button>
          <Dialog open={cancelOpen} onClose={() => setCancelOpen(false)}>
            <CancelDialog reference={reference} onClose={() => setCancelOpen(false)} />
          </Dialog>
        </Stack>
      </Stack>
    </MainLayout>
  );
};
