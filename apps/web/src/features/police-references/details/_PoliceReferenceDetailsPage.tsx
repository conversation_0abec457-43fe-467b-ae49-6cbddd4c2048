import { trpc } from '@/api';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { useParams } from '@tanstack/react-router';
import { isNullish } from 'common';
import { PoliceReferenceDetails } from './PoliceReferenceDetails';

export const PoliceReferenceDetailsPage = () => {
  const { id } = useParams({ from: '/app/police-references/$id' });
  const { data: reference } = trpc.policeReferences.get.useQuery({ id });

  if (isNullish(reference)) return <LoadingSpinner />;

  return <PoliceReferenceDetails reference={reference} />;
};
