import { ArrowDropDown } from '@mui/icons-material';
import { Button, ListItemIcon, ListItemText, Menu, MenuItem } from '@mui/material';

import { Csv, Excel } from 'mui-ex';
import React from 'react';

type Props = {
  title: string;
  disabled?: boolean;
  csvAction: () => void;
  excelAction: () => void;
};

export const DownloadMenuButton = ({ title, disabled, csvAction, excelAction }: Props) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  return (
    <>
      <Button
        variant="outlined"
        endIcon={<ArrowDropDown />}
        onClick={(e) => setAnchorEl(e.currentTarget)}
        fullWidth
        disabled={disabled}
      >
        {title}
      </Button>
      <Menu
        open={open}
        anchorEl={anchorEl}
        onClose={() => setAnchorEl(null)}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        keepMounted
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem
          onClick={() => {
            csvAction();
            setAnchorEl(null);
          }}
        >
          <ListItemIcon>
            <Csv fontSize="small" />
          </ListItemIcon>
          <ListItemText>CSVダウンロード</ListItemText>
        </MenuItem>
        <MenuItem
          onClick={() => {
            excelAction();
            setAnchorEl(null);
          }}
        >
          <ListItemIcon>
            <Excel />
          </ListItemIcon>
          <ListItemText>Excelダウンロード</ListItemText>
        </MenuItem>
      </Menu>
    </>
  );
};
