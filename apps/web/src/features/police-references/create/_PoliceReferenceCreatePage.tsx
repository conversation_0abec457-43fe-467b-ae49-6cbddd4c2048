import { Al<PERSON>, MenuItem, <PERSON>, Stack, TextField, Typography } from '@mui/material';
import { enqueueSnackbar } from 'notistack';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { zodResolver } from '@hookform/resolvers/zod';
import { Add, Close } from '@mui/icons-material';
import { useNavigate } from '@tanstack/react-router';
import { useForm } from 'react-hook-form';

import { imagesCol, removedAtCol, serialNoCol, statusCol } from '@/components/bicycles/columns';
import { RhfBicycleDataGrid } from '@/components/bicycles/react-hook-form/RhfBicycleDataGrid';

import { useAppContext } from '@/contexts/app-context';
import { createUseColumns } from '@/hooks/useColumns';
import { useInvalidateBicycles } from '@/models/bicycle';
import { policeReferenceCreateRoute } from '@/router/routes/keep/police-references';
import { isNonNullish, isNullish } from 'common';
import React from 'react';
import { z } from 'zod';
import { referenceByCol } from '../columns';

const useColumns = createUseColumns([
  imagesCol,
  removedAtCol,
  statusCol,
  serialNoCol,
  referenceByCol,
]);

const StateSchema = z.object({
  bicycleIds: z.array(z.string()).min(1),
  memo: z.string(),
});
type State = z.infer<typeof StateSchema>;

const none = 'none';

export const PoliceReferenceCreatePage = () => {
  const { policeStations } = useAppContext();
  const { control, setValue, handleSubmit, formState } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      bicycleIds: [],
      memo: '',
    } as const satisfies State,
    resolver: zodResolver(StateSchema),
  });
  const [prefectureCode, setPrefectureCode] = React.useState<string>(none);
  const handleSelect = (code: string) => {
    setPrefectureCode(code);
    setValue('bicycleIds', []);
  };

  const { data: bicycles = [] } = trpc.bicycles.list.useQuery({ type: 'police-request' });
  const columns = useColumns(bicycles);

  const invalidate = useInvalidateBicycles();
  const navigate = useNavigate();
  const { error, mutateAsync } = trpc.policeReferences.request.useMutation({
    onSuccess: () => {
      invalidate('police-request');
      navigate({ to: '/police-references' });
      enqueueSnackbar('警察照会依頼を作成しました', { variant: 'success' });
    },
  });

  const title = policeReferenceCreateRoute.meta.useTitle();
  const submit = async (input: State) => {
    if (prefectureCode === none) throw new Error();
    const prefecture = policeStations.find((p) => p.prefectureCode === prefectureCode)?.prefecture;
    if (isNullish(prefecture)) throw new Error();
    await mutateAsync({
      prefecture,
      prefectureCode,
      bicycleIds: input.bicycleIds,
      memo: input.memo,
    });
  };

  const prefectureOptions = Map.groupBy(
    bicycles,
    (b) => b.body?.last?.registrationNumberPrefectureCode,
  )
    .entries()
    .toArray()
    .filter(([code]) => isNonNullish(code))
    .map(([code, group]) => {
      if (isNullish(code)) throw new Error();
      const police = policeStations.find((p) => p.prefectureCode === code);
      if (police === undefined) throw new Error();
      return {
        label: `${police.prefecture} (${group.length})`,
        value: code,
      };
    });

  const rows = bicycles.filter((b) => {
    if (prefectureCode === none) return false;
    const code = b.body?.last?.registrationNumberPrefectureCode;
    if (isNullish(code)) return false;
    return code === prefectureCode;
  });

  return (
    <MainLayout title={title}>
      <Stack
        component="form"
        spacing={2}
        noValidate
        onSubmit={handleSubmit(submit)}
        sx={{ height: 1 }}
      >
        <Stack component={Paper} spacing={2} sx={{ height: 1, p: 2 }}>
          <Typography color="textSecondary">
            都道府県を選択し、自転車を選択してください。
          </Typography>
          <TextField
            select
            label="都道府県"
            value={prefectureCode ?? none}
            onChange={(e) => handleSelect(e.target.value)}
          >
            <MenuItem value={none} disabled>
              都道府県を選択してください
            </MenuItem>
            {prefectureOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </TextField>
          <Stack sx={{ flexGrow: 1 }}>
            <RhfBicycleDataGrid
              control={control}
              columns={columns}
              rows={rows}
              hideFooterSelectedRowCount
            />
          </Stack>
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
          <Stack alignItems="end">
            <TwoActionButtons
              primary={{
                type: 'submit',
                icon: Add,
                label: '新規作成',
                loading: formState.isSubmitting,
              }}
              secondary={{
                icon: Close,
                end: true,
                label: 'キャンセル',
                onClick: () => navigate({ to: '/sell-contracts' }),
              }}
            />
          </Stack>
        </Stack>
      </Stack>
    </MainLayout>
  );
};
