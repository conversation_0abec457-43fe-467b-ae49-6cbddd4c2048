import { Assignment, AssignmentTurnedIn, Cancel } from '@mui/icons-material';
import { Chip, type ChipProps } from '@mui/material';
import { blueGrey, green, lightBlue } from '@mui/material/colors';
import { match } from 'ts-pattern';

import type { ReferenceStatus } from 'common';

import { useAppContext } from '@/contexts/app-context';

const statusToIcon = (status: ReferenceStatus) =>
  match(status)
    .with('request', () => Assignment)
    .with('response', () => AssignmentTurnedIn)
    .with('canceled', () => Cancel)
    .exhaustive();

const statusToColor = (status: ReferenceStatus) =>
  match(status)
    .with('request', () => lightBlue)
    .with('response', () => green)
    .with('canceled', () => blueGrey)
    .exhaustive();

type Props = ChipProps & {
  status: ReferenceStatus;
};

export const ReferenceStatusChip = ({ status, ...props }: Props) => {
  const { labels } = useAppContext();
  const label = labels.referenceStatus[status];
  const Icon = statusToIcon(status);
  const color = statusToColor(status);
  return (
    <Chip
      {...props}
      label={label}
      icon={<Icon style={{ color: color['300'] }} />}
      sx={{ ...props.sx, bgcolor: color['50'] }}
    />
  );
};
