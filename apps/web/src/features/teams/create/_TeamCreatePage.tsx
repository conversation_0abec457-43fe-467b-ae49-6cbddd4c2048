import { Alert, Paper, Stack } from '@mui/material';
import { enqueueSnackbar } from 'notistack';

import { zodResolver } from '@hookform/resolvers/zod';
import { Add, Close } from '@mui/icons-material';
import { useNavigate } from '@tanstack/react-router';
import { useForm } from 'react-hook-form';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';
import { teamRoutes } from '@/router/routes/users/teams';
import { RhfTeam, type TeamState, TeamStateSchema } from '../common/RhfTeam';
import { uploadTeamLogoImageToS3 } from '../common/funcs';

export const TeamCreatePage = () => {
  const { labels } = useAppContext();
  const title = teamRoutes.meta.create.useTitle();
  const { control, watch, setValue, handleSubmit, formState } = useForm<TeamState>({
    mode: 'onChange',
    defaultValues: {
      name: '',
      description: '',
      logoKey: null,
      logoLocalFilename: null,
      logoLocalBase64: null,
      userIds: [],
    } as const satisfies TeamState,
    resolver: zodResolver(TeamStateSchema),
  });

  const { data: users = [] } = trpc.users.list.useQuery();
  const { error, mutate } = trpc.teams.create.useMutation();

  const navigate = useNavigate();
  const submit = async (state: TeamState) => {
    const id = crypto.randomUUID();
    const logoKey = await uploadTeamLogoImageToS3(
      id,
      state.logoLocalFilename,
      state.logoLocalBase64,
    );
    mutate(
      { ...state, id, logoKey },
      {
        onSuccess: () => {
          navigate({ to: '/teams' });
          enqueueSnackbar(`${labels.org.team}を${labels.action.create}しました`, {
            variant: 'success',
          });
        },
      },
    );
  };
  return (
    <MainLayout scrollable title={title}>
      <Stack component="form" spacing={2} noValidate onSubmit={handleSubmit(submit)}>
        <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
          <RhfTeam control={control} watch={watch} setValue={setValue} users={users} />
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
        </Stack>
        <Stack alignItems="end" sx={{ mt: 2 }}>
          <TwoActionButtons
            primary={{
              type: 'submit',
              icon: Add,
              label: '新規作成',
              loading: formState.isSubmitting,
            }}
            secondary={{
              icon: Close,
              end: true,
              label: 'キャンセル',
              onClick: () => navigate({ to: '/teams' }),
            }}
          />
        </Stack>
      </Stack>
    </MainLayout>
  );
};
