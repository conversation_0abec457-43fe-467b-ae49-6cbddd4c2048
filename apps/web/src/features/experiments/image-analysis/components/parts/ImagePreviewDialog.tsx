import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  ImageList,
  ImageListItem,
} from '@mui/material';

type Props = {
  isOpen: boolean;
  toggleDialog: (isOpen: boolean) => void;
  imagePreview: string;
};

export const ImagePreviewDialog = ({ isOpen, toggleDialog, imagePreview }: Props) => (
  <Dialog open={isOpen} onClose={() => toggleDialog(false)} maxWidth="xl">
    <DialogContent>
      <ImageList cols={1} gap={8}>
        <ImageListItem>
          <img src={imagePreview} alt="Preview" />
        </ImageListItem>
      </ImageList>
    </DialogContent>
    <DialogActions>
      <Button variant="outlined" onClick={() => toggleDialog(false)}>
        閉じる
      </Button>
    </DialogActions>
  </Dialog>
);
