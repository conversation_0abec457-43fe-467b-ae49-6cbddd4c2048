import { type ChangeEvent, useState } from 'react';

import { Check, ExpandMore, OpenInNew } from '@mui/icons-material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { LoadingButton } from '@mui/lab';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Alert,
  Box,
  Button,
  Container,
  Fab,
  Grid,
  ImageList,
  ImageListItem,
  Stack,
  Typography,
} from '@mui/material';

import { trpc } from '@/api';
import { FormSection } from '@/components/FormSection';

import { paletteMap, prompt } from '../store';

import { VisuallyHiddenInput } from '@/components/VisuallyHiddenInput';
import { ImagePreviewDialog } from './parts/ImagePreviewDialog';

export const AnalyzeColor = () => {
  const [image, setImage] = useState<string>('');
  const [imagePreview, setImagePreview] = useState<string>('');
  const [bicycleColor, SetBicycleColor] = useState<string>('');
  const [colorJP, setColorJP] = useState<string>('');
  const [isOpen, toggleDialog] = useState(false);

  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files ? event.target.files[0] : null;
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result;
        if (typeof base64String === 'string') {
          setImage(base64String);
          setImagePreview(URL.createObjectURL(file));
        }
      };
      reader.readAsDataURL(file);
    }
  };
  const { mutate, isPending, error: mutateError } = trpc.bicycles.imageAnalysis.useMutation();
  const handleSubmit = () => {
    if (!image) return;
    const input = {
      imageBase64: image.split(',')[1],
      commandText: prompt,
    };
    mutate(input, {
      onSuccess: (data) => {
        const color = data.split(':')[1].trim();
        const isPaletteColor = paletteMap.some((pallet) => pallet.name === color);
        if (isPaletteColor) {
          setColorJP(paletteMap.find((pallet) => pallet.name === color)?.jp ?? '');
          SetBicycleColor(color);
          return;
        }
        SetBicycleColor(color);
      },
    });
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ width: '100%', padding: 2 }}>
        <Stack sx={{ mt: 4 }} spacing={3}>
          <FormSection title="プロンプト">
            <Accordion>
              <AccordionSummary
                expandIcon={<ExpandMore />}
                aria-controls="panel1-content"
                id="panel1-header"
              >
                詳細を見る
              </AccordionSummary>
              <AccordionDetails>{prompt}</AccordionDetails>
            </Accordion>
          </FormSection>
          <FormSection title="写真">
            <Box flexDirection="column">
              <Box>
                <Button
                  component="label"
                  tabIndex={-1}
                  startIcon={<CloudUploadIcon />}
                  variant="text"
                >
                  ファイルアップロード
                  <VisuallyHiddenInput type="file" onChange={handleFileChange} />
                </Button>
              </Box>
              <Box
                id="image-preview"
                sx={{
                  position: 'relative',
                }}
              >
                {imagePreview && (
                  <ImageList
                    cols={1}
                    sx={{
                      width: '30%',
                      objectFit: 'cover',
                      borderRadius: 4,
                    }}
                  >
                    <ImageListItem>
                      <img src={imagePreview} alt="Preview" />
                    </ImageListItem>
                  </ImageList>
                )}
                <Fab
                  size="small"
                  color="primary"
                  sx={{
                    position: 'absolute',
                    top: 1,
                    left: 1,
                    display: !imagePreview ? 'none' : undefined,
                    width: 40,
                    height: 40,
                  }}
                  onClick={() => toggleDialog(true)}
                >
                  <OpenInNew />
                </Fab>
                <ImagePreviewDialog
                  isOpen={isOpen}
                  toggleDialog={toggleDialog}
                  imagePreview={imagePreview}
                />
              </Box>
            </Box>
          </FormSection>
          {mutateError && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              サーバーエラーが発生しました。管理者へお問い合わせください。
            </Alert>
          )}
          <Box sx={{ width: '100%', mt: 3, display: 'flex', justifyContent: 'center' }}>
            <Box sx={{ mt: 2 }}>
              <LoadingButton
                loading={isPending}
                variant="contained"
                color="primary"
                onClick={handleSubmit}
                sx={{ width: '200px' }}
              >
                判定
              </LoadingButton>
            </Box>
          </Box>
          {bicycleColor && (
            <>
              <FormSection title="回答">
                <Box>
                  <Typography variant="h3" sx={{ fontWeight: 700 }}>
                    {colorJP}
                  </Typography>
                </Box>
              </FormSection>
              <FormSection title="色">
                <Grid container spacing={2}>
                  {paletteMap.map((color) => (
                    <Grid size={{ xs: 4, sm: 1.6, md: 1.6 }} key={color.name}>
                      <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                        <Fab
                          color="primary"
                          aria-label="add"
                          onClick={() => SetBicycleColor(color.name)}
                          sx={{
                            backgroundColor: color.hue,
                            width: { xs: 40, sm: 50 },
                            height: { xs: 40, sm: 50 },
                            ...(color.name.toUpperCase() === bicycleColor.toUpperCase() && {
                              outlineColor: 'white',
                              outlineStyle: 'solid',
                              outlineWidth: 3,
                            }),
                          }}
                        >
                          {color.name.toUpperCase() === bicycleColor.toUpperCase() && <Check />}
                        </Fab>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </FormSection>
            </>
          )}
        </Stack>
      </Box>
    </Container>
  );
};
