import React from 'react';

import { Box, Divider, Tab, Tabs } from '@mui/material';

import { AnalyzeColor } from './AnalyzeColor';
import { VehicleCounter } from './VehicleCounter';

function a11yProps(index: number) {
  return {
    id: `marker-type-tab-${index}`,
    'aria-controls': `marker-type-tabpanel-${index}`,
  };
}

const spacing = 2;

export const ImageAnalysisPage = () => {
  const [tabIndex, setTabIndex] = React.useState(0);

  return (
    <Box sx={{ width: '100%', height: '100%', mt: 5, px: 3 }}>
      <Tabs
        variant="fullWidth"
        aria-label="marker type tabs"
        value={tabIndex}
        onChange={(_, index) => setTabIndex(index)}
      >
        <Tab label="色" {...a11yProps(0)} />
        <Tab label="台数" {...a11yProps(3)} />
      </Tabs>
      <Divider />
      {tabIndex === 0 && (
        <Box sx={{ px: spacing, py: 1 }}>
          <AnalyzeColor />
        </Box>
      )}
      {tabIndex === 1 && (
        <Box sx={{ px: spacing, py: 1 }}>
          <VehicleCounter />
        </Box>
      )}
    </Box>
  );
};
