export const paletteMap = [
  { name: 'black', jp: '黒', hue: 'black' },
  { name: 'brown', jp: '茶色', hue: 'brown' },
  { name: 'grey', jp: 'グレー', hue: 'grey' },
  { name: 'blue', jp: '青', hue: 'blue' },
  { name: 'green', jp: '緑', hue: 'green' },
  { name: 'lightGreen', jp: 'ライトグリーン', hue: 'lightGreen' },
  { name: 'yellow', jp: '黄色', hue: 'yellow' },
  { name: 'lime', jp: 'ライム', hue: 'lime' },
  { name: 'orange', jp: 'オレンジ', hue: 'orange' },
  { name: 'pink', jp: 'ピンク', hue: 'pink' },
  { name: 'red', jp: '赤', hue: 'red' },
  { name: 'indigo', jp: 'インディゴ', hue: 'indigo' },
  { name: 'purple', jp: 'パープル', hue: 'purple' },
  { name: 'teal', jp: 'ティール', hue: 'teal' },
];

export const prompt = `以下の色一覧に基づいて、自転車の色に最も近い色を返してください。
  - 次のテンプレートに従って回答します: color:{color}
  - 以下の色一覧に一致する色がない場合は、次のテンプレートを使用します: color:{color}
  black
  brown
  grey
  blue
  green
  lightGreen
  yellow
  lime
  orange
  pink
  red
  indigo
  purple
  teal
    `;

/**
 * 防犯登録番号のフォーマットチェック
 * フォーマット例（東京都内のデータ）: 池袋 30-506-G-11559
 */
export const formatRegistrationNumber = (
  text: string,
): { registrationNumber: string; isMatch: boolean } => {
  const parts = text.split(' ');
  if (parts.length !== 2) return { registrationNumber: text, isMatch: false };
  const [_, part1] = parts;
  if (part1.split('-').length !== 4) return { registrationNumber: text, isMatch: false };

  // フォーマットに一致した場合
  return {
    registrationNumber: text,
    isMatch: true,
  };
};
