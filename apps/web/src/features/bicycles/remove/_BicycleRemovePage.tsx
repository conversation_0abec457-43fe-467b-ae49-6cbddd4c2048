import React from 'react';

import type { BicycleStatus, LocalImage } from 'common';

import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { TakePhotos } from '@/components/take-photos/TakePhotos';
import { useAppContext } from '@/contexts/app-context';
import { useGetBicycleOrNull } from '@/models/bicycle';
import { useSnapShotPositionAndAddress } from '@/stores/position';
import { getRouteApi, useNavigate } from '@tanstack/react-router';
import { PatrolForm } from '../components/PatrolForm';
import { ScanQrcode } from '../components/ScanQrcode';

type State = {
  step: 'takePhotos' | 'form';
  localImages: LocalImage[];
};

const routeApi = getRouteApi('/app/bicycles/remove');

export const BicycleRemovePage = () => {
  const {
    tenant: {
      patrol,
      remove: { image: settings },
    },
    labels,
  } = useAppContext();
  const navigate = useNavigate({ from: '/bicycles/remove' });
  const { bicycleId } = routeApi.useSearch();
  const { data: bicycle } = useGetBicycleOrNull(bicycleId, '/bicycles/remove');
  const initialState: State = {
    step: settings.max !== 0 ? 'takePhotos' : ('form' as const),
    localImages: [],
  };
  const [state, setState] = React.useState(initialState);
  const snapShot = useSnapShotPositionAndAddress();

  if (bicycleId === undefined)
    return (
      <ScanQrcode
        title={`${labels.domain.bicycle}の${labels.et.remove}`}
        validate={(id, bicycle, set, setError) => {
          if (bicycle === null) {
            // マーキング or 即時撤去なら未登録状態でOK
            if (patrol.flow !== 'find') return set({ id, bicycle });
            // 巡回からQRコードを使用するなら未登録状態はエラー
            return setError(`
              まだ${labels.et.find}されていません。
              読み取るQRコードを確認してください`);
          }
          const acceptableStatuses = ['find', 'ensureAbandoned'] as const satisfies BicycleStatus[];
          if (acceptableStatuses.includes(bicycle.status)) return set({ id, bicycle });
          return setError(
            `この${labels.domain.bicycle}は${labels.et.remove}可能な${labels.system.status}ではありません。`,
          );
        }}
        onScan={({ id }) => navigate({ search: { bicycleId: id } })}
        from="/bicycles/remove"
      />
    );

  const handleImageComplete = (localImages: LocalImage[]) =>
    setState((s) => ({ ...s, step: 'form', localImages }));
  if (state.step === 'takePhotos')
    return <TakePhotos settings={settings} onComplete={handleImageComplete} />;

  if (bicycle === undefined) return <LoadingSpinner />;

  const handleReset = () => {
    setState(initialState);
    navigate({ search: { bicycleId: undefined } });
  };

  if (snapShot === undefined) return <LoadingSpinner message="現在位置を取得しています..." />;

  return (
    <PatrolForm
      bicycleId={bicycleId}
      eventType="remove"
      localImages={state.localImages}
      location={bicycle?.location?.last}
      body={bicycle?.body?.last}
      position={snapShot.position}
      address={snapShot.address}
      copied={bicycle?.copiedOwnerInfo ?? false}
      reset={handleReset}
    />
  );
};
