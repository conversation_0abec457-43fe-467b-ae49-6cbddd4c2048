import React from 'react';

import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { TakePhotos } from '@/components/take-photos/TakePhotos';
import { useAppContext } from '@/contexts/app-context';
import { useSnapShotPositionAndAddress } from '@/stores/position';
import { getRouteApi, useNavigate } from '@tanstack/react-router';
import type { LocalImage } from 'common';
import { PatrolForm } from '../components/PatrolForm';
import { ScanQrcode } from '../components/ScanQrcode';

type State = {
  step: 'takePhotos' | 'form';
  localImages: LocalImage[];
};

const routeApi = getRouteApi('/app/bicycles/find');

export const BicycleFindPage = () => {
  const {
    tenant: {
      find: { image: settings },
    },
    labels,
  } = useAppContext();
  const navigate = useNavigate({ from: '/bicycles/find' });
  const { bicycleId } = routeApi.useSearch();
  const initialState: State = {
    step: settings.max !== 0 ? 'takePhotos' : 'form',
    localImages: [],
  };
  const [state, setState] = React.useState(initialState);
  const snapShot = useSnapShotPositionAndAddress();

  if (bicycleId === undefined)
    return (
      <ScanQrcode
        title={`${labels.domain.bicycle}の${labels.et.find}`}
        validate={(id, bicycle, set, setError) => {
          if (bicycle === null) return set({ id, bicycle });
          setError(`
            このQRコードは既に他の${labels.domain.bicycle}に使用されています。
            新しいQRコードを読み取ってください。`);
        }}
        onScan={({ id }) => navigate({ search: { bicycleId: id } })}
        from="/bicycles/find"
      />
    );

  const handleImageComplete = (localImages: LocalImage[]) =>
    setState((s) => ({ ...s, step: 'form', localImages }));
  if (state.step === 'takePhotos')
    return <TakePhotos settings={settings} onComplete={handleImageComplete} />;

  const handleReset = () => {
    setState(initialState);
    navigate({ search: { bicycleId: undefined } });
  };
  if (snapShot === undefined) return <LoadingSpinner message="現在位置を取得しています..." />;
  return (
    <PatrolForm
      bicycleId={bicycleId}
      eventType="find"
      localImages={state.localImages}
      location={undefined}
      body={undefined}
      position={snapShot.position}
      address={snapShot.address}
      copied={false}
      reset={handleReset}
    />
  );
};
