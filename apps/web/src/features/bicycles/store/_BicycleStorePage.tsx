import React from 'react';

import type { LocalImage } from 'common';

import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { TakePhotos } from '@/components/take-photos/TakePhotos';
import { type ContextTenant, useAppContext } from '@/contexts/app-context';
import { useGetBicycleOrNull } from '@/models/bicycle';
import { getRouteApi, useNavigate } from '@tanstack/react-router';
import {
  type StorageLocationState,
  storageLocationToState,
} from '../components/RhfBicycleStorageLocationForm';
import { ScanQrcode } from '../components/ScanQrcode';
import { BicycleStoreForm } from './BicycleStoreForm';
import { BicycleStoreOnlyStorageLocationForm } from './BicycleStoreOnlyStorageLocationForm';

type Step = 'storageLocationForm' | 'takePhotos' | 'bodyForm';

const calcInitialStep = (tenant: ContextTenant): Step => {
  if (tenant.store.allowEntryBodyInfoLater) return 'storageLocationForm';
  if (tenant.store.image.max !== 0) return 'takePhotos';
  return 'bodyForm';
};

type State = {
  step: Step;
  storageLocation: StorageLocationState | undefined;
  localImages: LocalImage[];
};

const routeApi = getRouteApi('/app/bicycles/store');

export const BicycleStorePage = () => {
  const { tenant, labels } = useAppContext();
  const {
    store: { image: settings },
  } = tenant;
  const navigate = useNavigate({ from: '/bicycles/store' });
  const { bicycleId } = routeApi.useSearch();
  const { data: bicycle } = useGetBicycleOrNull(bicycleId, '/bicycles/store');
  const initialStep: Step = calcInitialStep(tenant);
  const initialState: State = {
    step: initialStep,
    storageLocation: undefined,
    localImages: [],
  };
  const [state, setState] = React.useState<State>(initialState);

  if (bicycleId === undefined || bicycle === null)
    return (
      <ScanQrcode
        title={`${labels.domain.bicycle}の${labels.et.store}`}
        validate={(id, bicycle, set, setError) => {
          if (bicycle === null)
            return setError(`
              まだ${labels.et.remove}されていません。
              読み取るQRコードを確認してください。`);

          if (bicycle.status !== 'remove')
            return setError('この車両は移送可能なステータスではありません。');

          return set({ id, bicycle });
        }}
        onScan={({ id, bicycle }) => {
          if (bicycle === null) throw new Error('bicycle is null');
          setState({
            ...state,
            storageLocation: storageLocationToState(
              bicycle.storageLocation?.last,
              bicycle.serialTag,
            ),
          });
          navigate({ search: { bicycleId: id } });
        }}
        from="/bicycles/store"
      />
    );

  if (bicycle === undefined) return <LoadingSpinner />;

  const handleNext = (storageLocation: StorageLocationState) =>
    setState({ ...state, step: 'takePhotos', storageLocation });
  const handleReset = () => {
    setState(initialState);
    navigate({ search: { bicycleId: undefined } });
  };
  if (state.step === 'storageLocationForm')
    return (
      <BicycleStoreOnlyStorageLocationForm
        bicycle={bicycle}
        onNext={handleNext}
        reset={handleReset}
      />
    );

  const handleImageComplete = (localImages: LocalImage[]) =>
    setState((s) => ({ ...s, step: 'bodyForm', localImages }));
  if (state.step === 'takePhotos')
    return <TakePhotos settings={settings} onComplete={handleImageComplete} />;
  if (state.storageLocation === undefined) throw new Error('storageLocation is undefined');
  return (
    <BicycleStoreForm
      bicycle={bicycle}
      storageLocation={state.storageLocation}
      localImages={state.localImages}
      reset={handleReset}
    />
  );
};
