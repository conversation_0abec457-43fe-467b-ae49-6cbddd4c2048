import { zodResolver } from '@hookform/resolvers/zod';
import { FirstPage } from '@mui/icons-material';
import { Alert, Container, Grid, Paper, Stack, Typography } from '@mui/material';
import Divider from '@mui/material/Divider';
import { useNavigate } from '@tanstack/react-router';
import { useForm } from 'react-hook-form';

import { ImageSchema, type LocalImage } from 'common';
import type { Bicycle } from 'lambda-api';
import { RhfTextField } from 'mui-ex';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import { eventTypeToIcon, useInvalidateBicycle } from '@/models/bicycle';
import { z } from 'zod';
import { RhfAiAssistButton } from '../components/RhfAiAssistButton';
import {
  BodyStateSchema,
  RhfBicycleBodyForm,
  bodyStateToInput,
  bodyToState,
} from '../components/RhfBicycleBodyForm';
import {
  RhfBicycleStorageLocationForm,
  type StorageLocationState,
  useStorageLocationStateSchema,
} from '../components/RhfBicycleStorageLocationForm';
import { CopyOwnerStateSchema, RhfCopyOwnerForm } from '../components/RhfCopyOwnerForm';
import { RhfBicycleImages } from '../components/rhf-bicycle-images/_RhfBicycleImages';
import { uploadBicycleImagesToS3 } from '../funcs';

const useStateSchema = () =>
  z.object({
    storageLocation: useStorageLocationStateSchema(),
    body: BodyStateSchema,
    copyOwner: CopyOwnerStateSchema,
    images: z.array(ImageSchema),
    memo: z.string(),
  });
type State = z.infer<ReturnType<typeof useStateSchema>>;

type Props = {
  bicycle: Bicycle;
  storageLocation: StorageLocationState;
  localImages: LocalImage[];
  reset: () => void;
};

export const BicycleStoreForm = ({ bicycle, storageLocation, localImages, reset }: Props) => {
  const { tenant, labels, policeStations } = useAppContext();
  const StateSchema = useStateSchema();
  const { control, handleSubmit, watch, formState } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      storageLocation,
      body: bodyToState(bicycle.body?.last, policeStations, tenant),
      copyOwner: { isCopy: false, ownerInfoId: undefined },
      images: localImages,
      memo: '',
    } as const satisfies State,
    resolver: zodResolver(StateSchema),
  });

  const invalidate = useInvalidateBicycle();
  const navigate = useNavigate();
  const { mutateAsync, error } = trpc.bicycles.store.useMutation({
    onSuccess: () => {
      invalidate(bicycle.id);
      navigate({ to: '/' });
    },
  });
  const submit = async ({ copyOwner, ...state }: State) => {
    const images = await uploadBicycleImagesToS3(bicycle.id, state.images);
    await mutateAsync({
      bicycleId: bicycle.id,
      storageLocation: state.storageLocation,
      body: bodyStateToInput(state.body, policeStations),
      ownerInfoId: copyOwner.ownerInfoId,
      images,
      memo: state.memo,
    });
  };
  const isBicycle = watch('body.type') === 'bicycle';
  return (
    <Container maxWidth="md" sx={{ py: { xs: 3, sm: 2 } }}>
      <form noValidate onSubmit={handleSubmit(submit)}>
        <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
          <Typography variant="h5">{`${labels.domain.bicycle}の${labels.et.store}`}</Typography>
          <RhfBicycleImages control={control} watch={watch} settings={tenant.store.image} />
          <Divider sx={{ my: 3 }} />

          <Typography variant="h6">{labels.domain.storageLocation}</Typography>

          <Grid container spacing={2}>
            <Grid size={{ xs: 12, md: 6 }}>
              <RhfBicycleStorageLocationForm control={control} />
            </Grid>
          </Grid>
          <Divider sx={{ my: 3 }} />
          <Stack direction="row" spacing={2}>
            <Typography variant="h6">{labels.domain.body}</Typography>
            <RhfAiAssistButton control={control} watch={watch} />
          </Stack>
          <RhfBicycleBodyForm control={control} watch={watch} />
          {isBicycle && (
            <RhfCopyOwnerForm
              control={control}
              watch={watch}
              bicycleId={bicycle.id}
              copied={bicycle.copiedOwnerInfo}
            />
          )}
          <Stack spacing={2} sx={{ mt: 2 }}>
            <RhfTextField
              control={control}
              name="memo"
              label={labels.system.memo}
              multiline
              rows={4}
            />
          </Stack>
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
          <TwoActionButtons
            primary={{
              type: 'submit',
              icon: eventTypeToIcon('store'),
              label: labels.et.store,
              loading: formState.isSubmitting,
            }}
            secondary={{
              icon: FirstPage,
              label: '最初からやり直す',
              onClick: reset,
            }}
            fullWidth
            spacing={2}
          />
        </Stack>
      </form>
    </Container>
  );
};
