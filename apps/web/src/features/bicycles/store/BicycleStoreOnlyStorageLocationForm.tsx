import { zodResolver } from '@hookform/resolvers/zod';
import { FirstPage, NavigateNext } from '@mui/icons-material';
import {
  Alert,
  Checkbox,
  Container,
  FormControl,
  FormControlLabel,
  Paper,
  Stack,
  Typography,
} from '@mui/material';
import { useNavigate } from '@tanstack/react-router';
import { useForm } from 'react-hook-form';

import type { Bicycle } from 'lambda-api';
import { RhfTextField } from 'mui-ex';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import { eventTypeToIcon } from '@/models/bicycle';
import React from 'react';
import { z } from 'zod';
import {
  RhfBicycleStorageLocationForm,
  type StorageLocationState,
  storageLocationToState,
  useStorageLocationStateSchema,
} from '../components/RhfBicycleStorageLocationForm';

const useStateSchema = () =>
  z.object({
    storageLocation: useStorageLocationStateSchema(),
    memo: z.string(),
  });
type State = z.infer<ReturnType<typeof useStateSchema>>;

type Props = {
  bicycle: Bicycle;
  onNext: (state: StorageLocationState) => void;
  reset: () => void;
};

export const BicycleStoreOnlyStorageLocationForm = ({ bicycle, onNext, reset }: Props) => {
  const navigate = useNavigate();
  const { labels } = useAppContext();
  const StateSchema = useStateSchema();
  const { control, handleSubmit, formState } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      storageLocation: storageLocationToState(bicycle.storageLocation?.last, bicycle.serialTag),
      memo: '',
    } as const satisfies State,
    resolver: zodResolver(StateSchema),
  });

  const [skip, setSkip] = React.useState(false);
  const handleChangeSkip = (_: React.ChangeEvent<HTMLInputElement>, checked: boolean) =>
    setSkip(checked);
  const { error, mutate } = trpc.bicycles.store.useMutation({
    onSuccess: () => navigate({ to: '/' }),
  });
  const submit = async (state: State) => {
    if (skip) {
      mutate({ bicycleId: bicycle.id, ...state });
    } else {
      onNext(state.storageLocation);
    }
  };

  const cycleType = bicycle.type ?? undefined;
  const landmarkId = bicycle.location?.last?.landmarkId ?? undefined;

  return (
    <Container maxWidth="md" sx={{ py: { xs: 3, sm: 2 } }}>
      <form noValidate onSubmit={handleSubmit(submit)}>
        <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
          <Typography variant="h5">{`${labels.domain.bicycle}の${labels.et.store}`}</Typography>
          <RhfBicycleStorageLocationForm
            control={control}
            bicycleType={cycleType}
            landmarkId={landmarkId}
          />
          <FormControl fullWidth>
            <FormControlLabel
              label="車体情報の入力をスキップする"
              control={<Checkbox onChange={handleChangeSkip} checked={skip} />}
            />
          </FormControl>
          {skip && (
            <Stack spacing={2} sx={{ mt: 2 }}>
              <RhfTextField
                control={control}
                name="memo"
                label={labels.system.memo}
                multiline
                rows={4}
              />
            </Stack>
          )}
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
          <TwoActionButtons
            primary={{
              type: 'submit',
              icon: skip ? eventTypeToIcon('store') : NavigateNext,
              label: skip ? `${labels.et.store}する` : '次へ',
              loading: formState.isSubmitting,
            }}
            secondary={{
              icon: FirstPage,
              label: '最初からやり直す',
              onClick: reset,
            }}
            fullWidth
            spacing={2}
          />
        </Stack>
      </form>
    </Container>
  );
};
