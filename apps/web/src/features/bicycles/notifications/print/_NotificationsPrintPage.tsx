import {
  <PERSON>,
  <PERSON>ton,
  Dialog,
  Paper,
  Stack,
  type Theme,
  ThemeProvider,
  Typography,
} from '@mui/material';
import React from 'react';
import { useController, useForm } from 'react-hook-form';
import { z } from 'zod';

import { trpc } from '@/api';
import { RhfBicycleDataGrid } from '@/components/bicycles/react-hook-form/RhfBicycleDataGrid';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';
import { notificationPrint } from '@/router/routes/keep/notifications';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNotificationColumns } from '../common';
import { NotificationsPrintPreview } from './NotificationsPrintPreview';

const DataGridToolbar = () => {
  const { labels } = useAppContext();
  return (
    <Box sx={{ p: 1 }}>
      <Typography color="textSecondary">
        {`${labels.domain.storageNotice}を${labels.doing.print}${labels.domain.bicycle}を選択してください`}
      </Typography>
    </Box>
  );
};

const a4widthPx = 567;

const createPrintTheme = (t: Theme): Theme => ({
  ...t,
  breakpoints: {
    ...t.breakpoints,
    values: {
      xs: 0,
      sm: 0,
      md: a4widthPx + Number(t.spacing(4).slice(0, -2)),
      lg: 10000,
      xl: 10000,
    },
  },
});

const StateSchema = z.object({
  bicycleIds: z.array(z.string()),
});
type State = z.infer<typeof StateSchema>;

export const NotificationsPrintPage = () => {
  const { data: bicycles = [], isLoading } = trpc.bicycles.list.useQuery({ type: 'notifiable' });
  const { control, watch, handleSubmit } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      bicycleIds: [],
    } satisfies State,
    resolver: zodResolver(StateSchema),
  });

  const [preview, setPreview] = React.useState(false);
  const submit = () => setPreview(true);
  const bicycleIdsCtrl = useController({ control, name: 'bicycleIds' });
  const handlePrint = async () => {
    setPreview(false);
    bicycleIdsCtrl.field.onChange([]);
  };

  const columns = useNotificationColumns(bicycles);

  const title = notificationPrint.meta.useTitle();

  const selectedIds = watch('bicycleIds');
  const selected = bicycles.filter((b) => selectedIds.includes(b.id));

  return (
    <MainLayout title={title}>
      <>
        <Stack
          component="form"
          noValidate
          onSubmit={handleSubmit(submit)}
          sx={{ width: 1, height: 1 }}
        >
          <Stack component={Paper} spacing={2} sx={{ width: 1, height: 1, p: 2 }}>
            <Stack sx={{ flexGrow: 1, overflow: 'auto' }}>
              <RhfBicycleDataGrid
                control={control}
                columns={columns}
                rows={bicycles}
                slots={{ toolbar: DataGridToolbar }}
                hideFooterSelectedRowCount
                loading={isLoading}
              />
            </Stack>
            <Stack alignItems="end">
              <Button type="submit" variant="contained">
                通知書を印刷する
              </Button>
            </Stack>
          </Stack>
        </Stack>
        <ThemeProvider<Theme> theme={createPrintTheme}>
          <Dialog
            open={preview}
            onClose={() => setPreview(false)}
            scroll="paper"
            fullWidth
            maxWidth="md"
          >
            <NotificationsPrintPreview
              bicycles={selected}
              onClose={() => setPreview(false)}
              onPrint={handlePrint}
            />
          </Dialog>
        </ThemeProvider>
      </>
    </MainLayout>
  );
};
