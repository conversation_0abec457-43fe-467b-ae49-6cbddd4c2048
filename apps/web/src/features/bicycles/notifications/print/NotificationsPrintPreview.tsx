import { trpc } from '@/api';
import { Box, Button, DialogActions, DialogContent, DialogTitle, Stack } from '@mui/material';

import { useInvalidateBicycles } from '@/models/bicycle';
import type { Bicycles } from 'lambda-api';
import React from 'react';
import { useReactToPrint } from 'react-to-print';

type Props = { bicycles: Bicycles; onClose: () => void; onPrint: () => void };

export const NotificationsPrintPreview = ({ bicycles, onClose, onPrint }: Props) => {
  const componentRef = React.useRef<HTMLDivElement | null>(null);
  const [printing, setPrinting] = React.useState(false);

  const invalidate = useInvalidateBicycles();
  const { mutate } = trpc.bicycles.notify.useMutation({
    onSuccess: () => {
      invalidate('notified');
      invalidate('notifiable');
      onPrint();
    },
  });
  const startPrint = useReactToPrint({
    contentRef: componentRef,
    onAfterPrint: () => {
      setPrinting(false);
      mutate({ bicycleIds: bicycles.map((b) => b.id) });
    },
  });
  const handlePrint = async () => {
    setPrinting(true);
    window.setTimeout(() => startPrint(), 0);
  };

  return (
    <>
      <DialogTitle>印刷プレビュー</DialogTitle>
      <DialogContent dividers sx={{ p: 2, bgcolor: '#f8f8f8' }}>
        <Box sx={{ zoom: 1.5 }}>
          {/* 1.5倍で表示 */}
          <Stack ref={componentRef}>
            {bicycles.map((bicycle) => {
              return (
                <Box
                  key={bicycle.id}
                  sx={{
                    width: '10cm',
                    height: '14.8cm',
                    pageBreakAfter: 'always',
                  }}
                >
                  ここにハガキの内容を記載
                </Box>
              );
            })}
          </Stack>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={onClose}>
          キャンセル
        </Button>
        <Button onClick={handlePrint} disabled={printing}>
          印刷する
        </Button>
      </DialogActions>
    </>
  );
};
