import { trpc } from '@/api';
import { Box, Paper } from '@mui/material';
import type { GridRowParams } from '@mui/x-data-grid';
import { useNavigate } from '@tanstack/react-router';
import { DataGrid } from 'mui-ex';

import { DataGridToolbar } from '@/libs/mui-x/grid/DataGridToolbar';

import { RouterButton } from '@/components/RouterButton';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { notificationList } from '@/router/routes/keep/notifications';
import { getQueryKey } from '@trpc/react-query';
import { useNotificationColumns } from '../common';

const Toolbar = () => (
  <DataGridToolbar queryKey={getQueryKey(trpc.bicycles.list, { type: 'notified' })} />
);

export const NotificationsPage = () => {
  const title = notificationList.meta.useTitle();
  const { data: bicycles = [], isPending } = trpc.bicycles.list.useQuery({ type: 'notified' });
  const columns = useNotificationColumns(bicycles);
  const navigate = useNavigate();
  const handleRowClick = (params: GridRowParams) => {
    navigate({ to: '/bicycles/$id', params: { id: params.id.toString() } });
  };
  return (
    <MainLayout
      title={title}
      firstRight={
        <>
          <Box sx={{ flexGrow: 1 }} />
          <RouterButton variant="contained" to="/notifications/print">
            通知書印刷
          </RouterButton>
          <RouterButton variant="outlined" to="/notifications/edit">
            通知日編集
          </RouterButton>
        </>
      }
    >
      <Paper sx={{ height: 1 }}>
        <DataGrid
          persistent="/notifications"
          columns={columns}
          rows={bicycles}
          onRowClick={handleRowClick}
          disableRowSelectionOnClick
          slots={{ toolbar: Toolbar }}
          loading={isPending}
          sx={{
            // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
            '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
            '& .MuiTablePagination-select': { mr: 2 },
          }}
        />
      </Paper>
    </MainLayout>
  );
};
