import {
  notificationDateCol,
  notificationStatusCol,
  useBaseBicycleColumns,
} from '@/components/bicycles/columns';
import { createUseColumns } from '@/hooks/useColumns';
import type { Bicycles } from 'lambda-api';

const useAdditionalColumns = createUseColumns([notificationStatusCol, notificationDateCol]);

export const useNotificationColumns = (bicycles: Bicycles) => {
  const baseColumns = useBaseBicycleColumns(bicycles);
  const additionalColumns = useAdditionalColumns(bicycles);
  return [...baseColumns, ...additionalColumns];
};
