import { Box, Button, Divider, Paper, Stack, Typography } from '@mui/material';
import { useNavigate, useRouter } from '@tanstack/react-router';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import { eventTypeToIcon, useInvalidateBicycles } from '@/models/bicycle';
import { Close } from '@mui/icons-material';
import { ImageSchema, isNonNullish } from 'common';
import type { Bicycle } from 'lambda-api';
import { RhfCheckbox, RhfTextField } from 'mui-ex';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  OwnerStateSchema,
  RhfBicycleOwnerForm,
  ownerToState,
} from '../components/RhfBicycleOwnerForm';
import {
  ReturnToOwnerStateSchema,
  RhfBicycleReturnToOwnerForm,
  returnToOwnerToState,
} from '../components/RhfBicycleReturnToOwnerForm';
import { RhfBicycleImages } from '../components/rhf-bicycle-images/_RhfBicycleImages';
import { uploadBicycleImagesToS3 } from '../funcs';

const StateSchema = z.object({
  returnToOwner: ReturnToOwnerStateSchema,
  owner: OwnerStateSchema,
  updateOwnerActive: z.boolean(),
  images: z.array(ImageSchema),
  memo: z.string(),
});

type State = z.infer<typeof StateSchema>;

type Props = {
  bicycle: Bicycle;
};

export const BicycleReturnToOwner = ({ bicycle }: Props) => {
  const {
    tenant: {
      returnToOwner: { image: imageSettings },
    },
    labels,
  } = useAppContext();

  const hasRegistrationNumber = isNonNullish(bicycle.body?.last?.registrationNumber);

  const { control, watch, formState, handleSubmit } = useForm<State>({
    defaultValues: {
      owner: ownerToState(null),
      returnToOwner: returnToOwnerToState(null),
      updateOwnerActive: hasRegistrationNumber,
      images: [],
      memo: '',
    } satisfies State,
  });

  const router = useRouter();

  const invalidate = useInvalidateBicycles();
  const navigate = useNavigate();
  const { mutate } = trpc.bicycles.returnToOwner.useMutation({
    onSuccess: () => {
      invalidate('returnable');
      navigate({ to: '/bicycles/$id', params: { id: bicycle.id } });
    },
  });
  const submit = async (state: State) => {
    const payment = state.returnToOwner.payment === 'yes';
    const reasonId = payment ? undefined : state.returnToOwner.reasonId;
    const images = await uploadBicycleImagesToS3(bicycle.id, state.images);
    mutate({ bicycleId: bicycle.id, ...state, returnToOwner: { payment, reasonId }, images });
  };

  return (
    <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
      <Box>
        <Button>返還受付票を印刷する</Button>
      </Box>
      <Stack component="form" noValidate spacing={2} onSubmit={handleSubmit(submit)}>
        <RhfBicycleReturnToOwnerForm control={control} watch={watch} />
        <Divider />
        <RhfBicycleOwnerForm control={control} watch={watch} />
        {hasRegistrationNumber && (
          <RhfCheckbox
            control={control}
            name="updateOwnerActive"
            label={`${labels.domain.owner}情報を${labels.doing.update}`}
          />
        )}
        <Stack spacing={1}>
          <Typography color="textSecondary">身分証明書の写し</Typography>
          <RhfBicycleImages control={control} watch={watch} settings={imageSettings} />
        </Stack>
        <RhfTextField control={control} name="memo" label={labels.system.memo} multiline rows={4} />
        <TwoActionButtons
          primary={{
            type: 'submit',
            icon: eventTypeToIcon('returnToOwner'),
            label: labels.ed.returnToOwner,
            loading: formState.isSubmitting,
          }}
          secondary={{
            icon: Close,
            label: labels.doing.cancel,
            onClick: () => router.history.go(-1),
          }}
          fullWidth
          spacing={2}
        />
      </Stack>
    </Stack>
  );
};
