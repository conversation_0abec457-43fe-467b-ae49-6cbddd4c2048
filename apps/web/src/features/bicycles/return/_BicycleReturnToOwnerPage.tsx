import { useParams } from '@tanstack/react-router';

import { trpc } from '@/api';

import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { returnToOwnerMeta } from '@/router/routes/keep/return-to-owner-route';
import { isNullish } from 'common';
import { BicycleReturnToOwner } from './BicycleReturnToOwner';

export const BicycleReturnToOwnerPage = () => {
  const { id } = useParams({ from: '/app/bicycles/$id/return' });
  const { data: bicycle } = trpc.bicycles.get.useQuery({ id, from: '/bicycles/$id/return' });

  const title = returnToOwnerMeta.useTitle();

  const content = isNullish(bicycle) ? (
    <LoadingSpinner />
  ) : (
    <BicycleReturnToOwner bicycle={bicycle} />
  );

  return (
    <MainLayout scrollable back={{ to: '/users/$id', params: { id } }} title={title}>
      {content}
    </MainLayout>
  );
};
