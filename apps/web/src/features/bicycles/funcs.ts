import { trpcClient } from '@/api';
import { type Image, base64ToBlob, base64ToContentType, isLocalImage } from 'common';
import zip from 'just-zip-it';

export const uploadBicycleImagesToS3 = async (bicycleId: string, images: Image[]) => {
  const localImages = images.filter(isLocalImage);
  const res = await trpcClient.presignedUrls.bicycle.list.query({
    bicycleId,
    files: localImages.map(({ filename, base64 }) => ({
      contentType: base64ToContentType(base64),
      filename,
    })),
  });
  const uploaded = await Promise.all(
    zip(localImages, res).map(async ([{ base64, description }, { key, url, fields }]) => {
      const body = new FormData();
      for (const [key, value] of Object.entries(fields)) {
        body.append(key, value);
      }
      body.append('file', base64ToBlob(base64));
      const response = await fetch(url, { method: 'POST', body });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Upload failed: ${errorText}`);
      }
      return { key, description, base64 };
    }),
  );
  return images.map((image, sortOrder) => {
    if (isLocalImage(image)) {
      const found = uploaded.find((u) => u.base64 === image.base64);
      if (found === undefined) throw new Error('Upload failed');
      const { key, description } = found;
      return { key, description, sortOrder };
    }
    const { key, description } = image;
    return { key, description, sortOrder };
  });
};
