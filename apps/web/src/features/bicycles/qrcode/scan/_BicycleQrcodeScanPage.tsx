import React from 'react';

import {
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Stack,
} from '@mui/material';
import { z } from 'zod';

import type { Bicycle } from 'lambda-api';

import { trpc } from '@/api';
import { QrcodeScanController } from '@/components/QrcodeScanController';
import { BicycleTimeline } from '@/components/bicycles/BicycleTimeline';
import { UnusedQrcodeActions } from '@/components/bicycles/UnusedQrcodeActions';
import { UsedQrcodeActions } from '@/components/bicycles/UsedQrcodeActions';
import { BackToHomeButton } from '@/components/bicycles/steps/buttons/BackToHomeButton';
import { useAppContext } from '@/contexts/app-context';
import { createSerialNoFormatValidator } from 'models';

export const BicycleQrcodeScanPage = () => {
  const [bicycleId, setBicycleId] = React.useState<string | undefined>();
  const [bicycle, setBicycle] = React.useState<Bicycle | undefined>();
  const [error, setError] = React.useState<string | undefined>();
  const utils = trpc.useUtils();
  const { tenant } = useAppContext();
  const format = tenant.numbering.serialNoFormat;
  const handleScan = React.useCallback(
    async (text: string) => {
      if (z.string().uuid().safeParse(text).success) {
        const bicycle = await utils.bicycles.get.fetch({ id: text, from: '/bicycles/qrcode/scan' });
        if (bicycle === null) return setBicycleId(text);
        return setBicycle(bicycle);
      }
      const serialNoFormatValidator = createSerialNoFormatValidator(format);
      if (serialNoFormatValidator.test(text)) {
        const bicycle = await utils.bicycles.getBySerialNo.fetch({ serialNo: text });
        if (bicycle === null) return setError('この整理番号に該当する自転車が見つかりません');
        return setBicycle(bicycle);
      }

      return setError('不正なQRコードです');
    },
    [utils.bicycles.get.fetch, utils.bicycles.getBySerialNo.fetch, format],
  );

  return (
    <>
      <QrcodeScanController
        title="QRコードの読み取り"
        description="カメラをQRコードに向けてください"
        onScan={handleScan}
        BackButton={<BackToHomeButton variant="text" fullWidth={false} />}
      />
      {/* 未登録のケース */}
      <Dialog open={Boolean(bicycleId)}>
        <Stack alignItems="stretch" spacing={1} p={1}>
          <DialogContent>
            <DialogContentText>このQRコードは未使用です</DialogContentText>
          </DialogContent>

          {bicycleId && <UnusedQrcodeActions bicycleId={bicycleId} />}

          <Button variant="outlined" onClick={() => setBicycleId(undefined)}>
            キャンセル
          </Button>
        </Stack>
      </Dialog>
      {/* 登録済みのケース */}
      <Dialog open={Boolean(bicycle)}>
        {bicycle && (
          <>
            <DialogContent>{bicycle && <BicycleTimeline events={bicycle.events} />}</DialogContent>
            <Stack spacing={1} sx={{ p: 1 }}>
              <UsedQrcodeActions bicycle={bicycle} />
              <Button variant="outlined" onClick={() => setBicycle(undefined)}>
                キャンセル
              </Button>
            </Stack>
          </>
        )}
      </Dialog>
      <Dialog open={Boolean(error)}>
        <DialogTitle>QRコードの読み取り失敗</DialogTitle>
        <DialogContent>
          <DialogContentText>{error}</DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button fullWidth onClick={() => setError(undefined)}>
            OK
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
