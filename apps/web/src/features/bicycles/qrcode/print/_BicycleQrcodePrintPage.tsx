import { useState } from 'react';

import { MainLayout } from '@/components/core/layout/MainLayout';
import { Print, Sync } from '@mui/icons-material';
import { Button, FormControl, Grid, InputLabel, MenuItem, Paper, Select } from '@mui/material';
import { QRCodeSVG } from 'qrcode.react';
import React from 'react';
import { useReactToPrint } from 'react-to-print';

const paperSpec = {
  '40': { cols: 5, rows: 8, px: '22mm', py: '14.5mm' },
  '70': { cols: 7, rows: 10, px: '23mm', py: '30.5mm' },
} as const;

type PaperType = keyof typeof paperSpec;

const generateUuids = (count: number): string[] => [...Array(count)].map(() => crypto.randomUUID());

export const BicycleQrcodePrintPage = () => {
  const [paperType, setPaperType] = useState<PaperType>('40');
  const paperTypeNumber = Number.parseInt(paperType, 10);
  const [quantity, setQuantity] = useState('1');
  const quantityNumber = Number.parseInt(quantity, 10);
  const [uuids, setUuids] = useState<string[]>(generateUuids(paperTypeNumber * quantityNumber));
  const updateUuids = () => setUuids(generateUuids(paperTypeNumber * quantityNumber));

  const componentRef = React.useRef<HTMLDivElement | null>(null);

  const startPrint = useReactToPrint({
    contentRef: componentRef,
    onAfterPrint: () => updateUuids(),
  });
  const handlePrint = async () => window.setTimeout(() => startPrint(), 0);

  const qrSize = paperType === '40' ? '30mm' : '20mm';

  return (
    <MainLayout
      title="QRコード印刷"
      scrollable
      second={
        <>
          <FormControl margin="normal" sx={{ width: '120px' }}>
            <InputLabel id="paper-type-label">台紙選択</InputLabel>
            <Select
              labelId="paper-type-label"
              value={paperType}
              label="paperType"
              onChange={(e) => setPaperType(e.target.value)}
            >
              <MenuItem value="40">A4 40枚</MenuItem>
              <MenuItem value="70">A4 70枚</MenuItem>
            </Select>
          </FormControl>
          <FormControl margin="normal" sx={{ width: '120px' }}>
            <InputLabel id="quantity-label">印刷枚数</InputLabel>
            <Select
              labelId="quantity-label"
              value={quantity}
              label="amount"
              onChange={(e) => setQuantity(e.target.value)}
            >
              <MenuItem value="1">1枚</MenuItem>
              <MenuItem value="10">10枚</MenuItem>
              <MenuItem value="20">20枚</MenuItem>
              <MenuItem value="100">100枚</MenuItem>
            </Select>
          </FormControl>
          <Button variant="outlined" startIcon={<Sync />} onClick={updateUuids}>
            更新
          </Button>
          <Button variant="contained" startIcon={<Print />} onClick={handlePrint} sx={{ ml: 2 }}>
            印刷
          </Button>
        </>
      }
    >
      <Paper
        ref={componentRef}
        sx={{
          width: '21cm',
          height: `calc(29.7cm * ${quantity})`,

          px: paperSpec[paperType].px,
          py: paperSpec[paperType].py,
        }}
      >
        <Grid
          container
          columns={paperSpec[paperType].cols}
          sx={{
            width: 1,
            height: 1,
            '--Grid-borderWidth': '1px',
            borderTop: 'var(--Grid-borderWidth) solid',
            borderLeft: 'var(--Grid-borderWidth) solid',
            borderColor: 'divider',
            '& > div': {
              borderRight: 'var(--Grid-borderWidth) solid',
              borderBottom: 'var(--Grid-borderWidth) solid',
              borderColor: 'divider',
            },
          }}
        >
          {[...Array(paperTypeNumber * quantityNumber).keys()].map((i) => {
            const uuid = uuids[i];
            return (
              <Grid
                key={uuid}
                size={{ xs: 1 }}
                sx={{
                  height: `calc(100% / ${paperSpec[paperType].rows})`,
                  pageBreakBefore: i !== 0 && i % paperTypeNumber === 0 ? 'always' : undefined,
                }}
              >
                <QRCodeSVG
                  key={uuid}
                  value={uuid}
                  style={{
                    width: qrSize,
                    height: qrSize,
                    margin: 'calc(2mm - 1px) 2mm 2mm calc(2mm - 1px)',
                  }}
                />
              </Grid>
            );
          })}
        </Grid>
      </Paper>
    </MainLayout>
  );
};
