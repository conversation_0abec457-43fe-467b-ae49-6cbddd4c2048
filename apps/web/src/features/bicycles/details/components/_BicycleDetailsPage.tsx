import { useParams } from '@tanstack/react-router';

import { isNullish } from 'common';

import { trpc } from '@/api';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { BicycleDetails } from './BicycleDetails';

export const BicycleDetailsPage = () => {
  const { id } = useParams({ from: '/app/bicycles/$id' });
  const { data: bicycle } = trpc.bicycles.get.useQuery({ id, from: '/bicycles/$id' });

  if (isNullish(bicycle)) return <LoadingSpinner />;

  return <BicycleDetails bicycle={bicycle} />;
};
