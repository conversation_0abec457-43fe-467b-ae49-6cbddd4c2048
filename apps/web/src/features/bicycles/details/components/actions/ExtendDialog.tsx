import { Alert, Dialog, DialogActions, DialogContent, DialogTitle, Stack } from '@mui/material';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import {
  OwnerStateSchema,
  RhfBicycleOwnerForm,
  ownerToState,
} from '@/features/bicycles/components/RhfBicycleOwnerForm';
import { RhfBicycleImages } from '@/features/bicycles/components/rhf-bicycle-images/_RhfBicycleImages';
import { uploadBicycleImagesToS3 } from '@/features/bicycles/funcs';
import { useInvalidateBicycle } from '@/models/bicycle';
import { zodResolver } from '@hookform/resolvers/zod';
import { Close, Edit } from '@mui/icons-material';
import { ImageSchema, isNonNullish } from 'common';
import type { Bicycle } from 'lambda-api';
import { RhfCheckbox, RhfDate<PERSON>icker, RhfTextField } from 'mui-ex';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

type Props = {
  open: boolean;
  onClose: () => void;
  bicycle: Bicycle;
};
const today = new Date();
today.setHours(0, 0, 0, 0);
const tomorrow = new Date(today);
tomorrow.setDate(today.getDate() + 1);
const StateSchema = z.object({
  owner: OwnerStateSchema,
  updateOwnerActive: z.boolean(),
  images: z.array(ImageSchema),
  deadline: z.coerce.date().min(tomorrow, { message: '今日以降の日付を入力してください。' }),
  memo: z.string(),
});
type State = z.infer<typeof StateSchema>;

export const ExtendDialog = (props: Props) => {
  const { open, onClose, bicycle } = props;

  const { labels } = useAppContext();
  const { control, watch, handleSubmit } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      owner: ownerToState(bicycle.owner?.last),
      updateOwnerActive: false,
      images: [],
      memo: '',
      deadline: bicycle.deadline?.last?.date ?? new Date(),
    },
    resolver: zodResolver(StateSchema),
  });
  const invalidate = useInvalidateBicycle();
  const { mutate, error, isPending } = trpc.bicycles.extend.useMutation({
    onSuccess: () => {
      invalidate(bicycle.id);
      onClose();
    },
  });

  const submit = async (state: State) => {
    const images = await uploadBicycleImagesToS3(bicycle.id, state.images);
    mutate({ bicycleId: bicycle.id, ...state, images });
  };
  const hasRegistrationNumber = isNonNullish(bicycle.body?.last?.registrationNumber);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      scroll="paper"
      slotProps={{
        paper: {
          component: 'form',
          onSubmit: handleSubmit(submit),
        },
      }}
    >
      <DialogTitle>{`保管期限を${labels.et.extendDeadline}する`}</DialogTitle>
      <DialogContent dividers>
        <Stack spacing={2}>
          <RhfDatePicker
            control={control}
            name="deadline"
            label={`${labels.et.extendDeadline}日`}
          />
          <RhfBicycleOwnerForm control={control} watch={watch} />
          {hasRegistrationNumber && (
            <RhfCheckbox control={control} name="updateOwnerActive" label="所有者情報を更新する" />
          )}
          <RhfBicycleImages
            control={control}
            watch={watch}
            settings={{ min: 0, max: 20, guides: [] }}
          />
          <RhfTextField
            control={control}
            name="memo"
            label={labels.system.memo}
            multiline
            rows={4}
          />
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
        </Stack>
      </DialogContent>
      <DialogActions>
        <TwoActionButtons
          primary={{
            type: 'submit',
            icon: Edit,
            label: '保存する',
            loading: isPending,
          }}
          secondary={{
            icon: Close,
            end: true,
            label: labels.action.cancel,
            onClick: onClose,
          }}
          spacing={1}
        />
      </DialogActions>
    </Dialog>
  );
};
