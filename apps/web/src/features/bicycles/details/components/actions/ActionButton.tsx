import React from 'react';

import {
  <PERSON><PERSON>,
  ListItemIcon,
  ListItemText,
  MenuItem,
  MenuList,
  Paper,
  Popover,
} from '@mui/material';

import type { Bicycle } from 'lambda-api';

import { useAppContext } from '@/contexts/app-context';
import { eventTypeToIcon } from '@/models/bicycle';
import { ArrowDropDown, ContentPasteSearch, EventBusy, MoreTime } from '@mui/icons-material';
import { createLink } from '@tanstack/react-router';
import { AssessmentDialog } from './AssessmentDialog';
import { ExtendDialog } from './ExtendDialog';
import { ResetNotificationDateDialog } from './ResetNotificationDateDialog';

const RouterMenuItem = createLink(MenuItem);

type Props = {
  bicycle: Bicycle;
};

export const ActionButton = ({ bicycle }: Props) => {
  const { labels } = useAppContext();
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) =>
    setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);
  const open = Boolean(anchorEl);
  const [extend, setExtend] = React.useState<boolean>(false);
  const [assessment, setAssessment] = React.useState<boolean>(false);
  const [resetNotificationDate, setResetNotificationDate] = React.useState<boolean>(false);

  const StoreIcon = eventTypeToIcon('store');
  const ReturnIcon = eventTypeToIcon('returnToOwner');
  const disabledClearNotificationDate = !bicycle.events.some(
    (e) => e.type === 'notify' && e.cancelBy === null,
  );
  return (
    <>
      <Button variant="text" endIcon={<ArrowDropDown />} onClick={handleClick}>
        アクション
      </Button>
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Paper sx={{ width: 300, maxWidth: '100%' }}>
          <MenuList>
            <RouterMenuItem
              to="/bicycles/store"
              search={{ bicycleId: bicycle.id }}
              disabled={bicycle.status !== 'remove'}
            >
              <ListItemIcon>
                <StoreIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>{labels.et.store}する</ListItemText>
            </RouterMenuItem>
            <RouterMenuItem
              to="/bicycles/$id/return"
              params={{ id: bicycle.id }}
              disabled={bicycle.status !== 'store'}
            >
              <ListItemIcon>
                <ReturnIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>{labels.et.returnToOwner}</ListItemText>
            </RouterMenuItem>
            <MenuItem onClick={() => setExtend(true)} disabled={bicycle.status !== 'store'}>
              <ListItemIcon>
                <MoreTime fontSize="small" />
              </ListItemIcon>
              <ListItemText>{labels.et.extendDeadline}</ListItemText>
            </MenuItem>
            <MenuItem
              onClick={() => setResetNotificationDate(true)}
              disabled={disabledClearNotificationDate}
            >
              <ListItemIcon>
                <EventBusy fontSize="small" />
              </ListItemIcon>
              <ListItemText>通知日リセット</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => setAssessment(true)} disabled={bicycle.status !== 'store'}>
              <ListItemIcon>
                <ContentPasteSearch fontSize="small" />
              </ListItemIcon>
              <ListItemText>{labels.et.assess}する</ListItemText>
            </MenuItem>
          </MenuList>
        </Paper>
      </Popover>
      <ExtendDialog bicycle={bicycle} open={extend} onClose={() => setExtend(false)} />
      <ResetNotificationDateDialog
        bicycle={bicycle}
        open={resetNotificationDate}
        onClose={() => setResetNotificationDate(false)}
      />
      <AssessmentDialog bicycle={bicycle} open={assessment} onClose={() => setAssessment(false)} />
    </>
  );
};
