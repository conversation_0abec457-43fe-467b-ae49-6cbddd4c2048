import {
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Stack,
  Typography,
} from '@mui/material';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import { useInvalidateBicycle } from '@/models/bicycle';
import { zodResolver } from '@hookform/resolvers/zod';
import { Close, Edit } from '@mui/icons-material';
import type { Bicycle } from 'lambda-api';
import { RhfTextField } from 'mui-ex';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const StateSchema = z.object({
  memo: z.string(),
});
type State = z.infer<typeof StateSchema>;

type Props = {
  open: boolean;
  onClose: () => void;
  bicycle: Bicycle;
};

export const ResetNotificationDateDialog = (props: Props) => {
  const { open, onClose, bicycle } = props;

  const { labels } = useAppContext();
  const { control, handleSubmit } = useForm<State>({
    mode: 'onChange',
    defaultValues: { memo: '' },
    resolver: zodResolver(StateSchema),
  });
  const invalidate = useInvalidateBicycle();
  const { mutateAsync, error, isPending } = trpc.bicycles.resetNotificationDate.useMutation({
    onSuccess: () => {
      invalidate(bicycle.id);
      onClose();
    },
  });

  const submit = async (state: State) => {
    mutateAsync({ bicycleId: bicycle.id, memo: state.memo });
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      scroll="paper"
      slotProps={{
        paper: {
          component: 'form',
          onSubmit: handleSubmit(submit),
        },
      }}
    >
      <DialogTitle>通知日をリセットする</DialogTitle>
      <DialogContent dividers>
        <Stack spacing={2}>
          <Typography>
            返還通知書が届かなかったときは通知日をリセットできます。
            <br />
            通知日をリセットすると、返還通知書の再印刷が可能です。
          </Typography>
          <RhfTextField
            control={control}
            name="memo"
            label={labels.system.memo}
            multiline
            rows={4}
          />
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
        </Stack>
      </DialogContent>
      <DialogActions>
        <TwoActionButtons
          primary={{
            type: 'submit',
            icon: Edit,
            label: `${labels.p.notificationDate}を${labels.action.reset}`,
            loading: isPending,
          }}
          secondary={{
            icon: Close,
            end: true,
            label: labels.action.cancel,
            onClick: onClose,
          }}
          spacing={1}
        />
      </DialogActions>
    </Dialog>
  );
};
