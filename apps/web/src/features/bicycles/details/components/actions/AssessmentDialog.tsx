import { Alert, Dialog, DialogActions, DialogContent, DialogTitle, Stack } from '@mui/material';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import { useInvalidateBicycle } from '@/models/bicycle';
import { zodResolver } from '@hookform/resolvers/zod';
import { Close, Edit } from '@mui/icons-material';
import { numberOrNull } from 'common';
import type { Bicycle } from 'lambda-api';
import { RhfSingleFreeSolo } from 'mui-ex';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

type Props = {
  open: boolean;
  onClose: () => void;
  bicycle: Bicycle;
};
const StateSchema = z.object({
  price: z.coerce.number().int().nonnegative(),
});
type State = z.infer<typeof StateSchema>;

export const AssessmentDialog = (props: Props) => {
  const { open, onClose, bicycle } = props;

  const { labels, priceSuggestions } = useAppContext();
  const { control, handleSubmit } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      price: bicycle.assessment?.last?.price ?? undefined,
    },
    resolver: zodResolver(StateSchema),
  });
  const invalidate = useInvalidateBicycle();
  const { mutate, error, isPending } = trpc.bicycles.assess.useMutation({
    onSuccess: () => {
      invalidate(bicycle.id);
      onClose();
    },
  });

  const submit = async (state: State) => {
    mutate({
      bicycleId: bicycle.id,
      assessment: { price: numberOrNull(state.price) },
    });
  };
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="xs"
      fullWidth
      scroll="paper"
      slotProps={{
        paper: {
          component: 'form',
          onSubmit: handleSubmit(submit),
        },
      }}
    >
      <DialogTitle>{`${labels.domain.bicycle}を${labels.dd.assess}`}</DialogTitle>
      <DialogContent dividers>
        <Stack spacing={2}>
          <RhfSingleFreeSolo
            control={control}
            name="price"
            label={`${labels.assessment.price}(円)`}
            options={priceSuggestions.map((g) => String(g.price))}
          />
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
        </Stack>
      </DialogContent>
      <DialogActions>
        <TwoActionButtons
          primary={{
            type: 'submit',
            icon: Edit,
            label: labels.action.save,
            loading: isPending,
          }}
          secondary={{
            icon: Close,
            end: true,
            label: labels.action.cancel,
            onClick: onClose,
          }}
          spacing={1}
        />
      </DialogActions>
    </Dialog>
  );
};
