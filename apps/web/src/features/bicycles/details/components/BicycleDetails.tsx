import React from 'react';

import {
  Box,
  Button,
  Dialog,
  DialogContent,
  Grid,
  IconButton,
  Popover,
  Stack,
  type Theme,
  useMediaQuery,
} from '@mui/material';
import { QRCodeSVG } from 'qrcode.react';

import type { Bicycle } from 'lambda-api';

import { BicycleStatusChip } from '@/components/bicycles/BicycleStatusChip';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { bicyclesDetailsMeta } from '@/router/routes/bicycles-details-route';
import { ArrowDropDown, QrCode2 } from '@mui/icons-material';
import { ActionButton } from './actions/ActionButton';
import { BodySection } from './sections/BodySection';
import { ImagesSection } from './sections/ImagesSection';
import { KeepSection } from './sections/KeepSection';
import { LocationSection } from './sections/LocationSection';
import { MultiStatusSection } from './sections/MultiStatusSection';
import { OwnerSection } from './sections/OwnerSection';
import { TimelineSection } from './sections/TimelineSection';

type Props = {
  bicycle: Bicycle;
};

const TimelineButton = ({ bicycle }: Props) => {
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) =>
    setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);
  const open = Boolean(anchorEl);
  return (
    <>
      <Button variant="text" endIcon={<ArrowDropDown />} onClick={handleClick}>
        イベント
      </Button>
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
      >
        <Box sx={{ width: (t) => `min(400px, calc(100vw - ${t.spacing(4)}))` }}>
          <TimelineSection bicycle={bicycle} />
        </Box>
      </Popover>
    </>
  );
};

export const BicycleDetails = ({ bicycle }: Props) => {
  const title = bicyclesDetailsMeta.useTitle();
  const mdDown = useMediaQuery<Theme>((theme) => theme.breakpoints.down('md'));
  const lgDown = useMediaQuery<Theme>((theme) => theme.breakpoints.down('lg'));

  const [qrOpen, setQrOpen] = React.useState(false);

  return (
    <MainLayout
      title={title}
      maxWidth="xl"
      scrollable
      firstRight={
        <>
          <BicycleStatusChip status={bicycle.status} />
          <Box>
            <IconButton size="small" onClick={() => setQrOpen(true)}>
              <QrCode2 fontSize="small" />
            </IconButton>
          </Box>
          <Dialog open={qrOpen} onClose={() => setQrOpen(false)}>
            <DialogContent>
              <QRCodeSVG value={bicycle.id} size={100} />
            </DialogContent>
          </Dialog>
          <Box sx={{ flexGrow: 1 }} />
          {lgDown && !mdDown && <TimelineButton bicycle={bicycle} />}
          {!mdDown && <ActionButton bicycle={bicycle} />}
        </>
      }
      second={
        mdDown ? (
          <>
            <Box sx={{ flexGrow: 1 }} />
            <TimelineButton bicycle={bicycle} />
            <ActionButton bicycle={bicycle} />
          </>
        ) : undefined
      }
    >
      <Grid container spacing={2}>
        <Grid size={{ xs: 12, lg: 8, xl: 9 }}>
          <Stack spacing={2}>
            <MultiStatusSection bicycle={bicycle} />
            <KeepSection bicycle={bicycle} />
            <BodySection bicycle={bicycle} key={`body-${bicycle.body?.updatedAt}`} />
            <OwnerSection bicycle={bicycle} key={`owner-${bicycle.owner?.updatedAt}`} />
            <LocationSection
              bicycle={bicycle}
              key={`location-${bicycle.location?.last?.updatedAt}`}
            />
            <ImagesSection bicycle={bicycle} />
          </Stack>
        </Grid>
        {!lgDown && (
          <Grid size={{ xs: 12, lg: 4, xl: 3 }}>
            <TimelineSection bicycle={bicycle} />
          </Grid>
        )}
      </Grid>
    </MainLayout>
  );
};
