import { Box, Divider, IconButton, Paper, Stack, Typography } from '@mui/material';

import { useAppContext } from '@/contexts/app-context';
import {
  LocationStateSchema,
  RhfBicycleLocationForm,
  locationToState,
} from '@/features/bicycles/components/RhfBicycleLocationForm';
import { Edit } from '@mui/icons-material';
import type { Bicycle } from 'lambda-api';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { SectionFooter } from '../SectionFooter';
import { LocationEditor } from './LocationEditor';
import { useBgColor } from './funcs';

type Props = {
  bicycle: Bicycle;
};

const StateSchema = z.object({
  location: LocationStateSchema,
});
type State = z.infer<typeof StateSchema>;

export const LocationSection = ({ bicycle }: Props) => {
  const { location } = bicycle;
  const { labels } = useAppContext();
  const { control, watch } = useForm<State>({
    defaultValues: {
      location: locationToState(location?.last, true),
    } as const satisfies State,
  });
  const [open, setOpen] = React.useState(false);

  const bgcolor = useBgColor(Boolean(location));
  const event = location?.versions?.at(-1)?.event;
  return (
    <Paper sx={{ bgcolor }}>
      <Stack direction="row" spacing={1} sx={{ p: 2, alignItems: 'center' }}>
        <Typography variant="h5">{labels.domain.location}</Typography>
        <Box sx={{ flexGrow: 1 }} />
        <IconButton size="small" color="primary" onClick={() => setOpen(true)}>
          <Edit />
        </IconButton>
      </Stack>
      <Divider />
      <Stack spacing={2} sx={{ p: 2 }}>
        <RhfBicycleLocationForm control={control} watch={watch} readOnly />
        {event && <SectionFooter event={event} />}
      </Stack>
      <LocationEditor open={open} onClose={() => setOpen(false)} bicycle={bicycle} />
    </Paper>
  );
};
