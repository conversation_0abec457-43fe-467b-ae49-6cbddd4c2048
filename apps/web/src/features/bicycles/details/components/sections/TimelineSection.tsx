import { RouterButton } from '@/components/RouterButton';
import { BicycleTimeline } from '@/components/bicycles/BicycleTimeline';
import History from '@mui/icons-material/History';

import { Box, Paper, Stack, Typography } from '@mui/material';
import type { Bicycle } from 'lambda-api';

type Props = {
  bicycle: Bicycle;
};

export const TimelineSection = ({ bicycle: { id, events } }: Props) => {
  return (
    <Paper sx={{ p: 2 }}>
      <Stack direction="row" alignItems="center" spacing={1}>
        <Typography variant="h5">イベント</Typography>
        <Box sx={{ flexGrow: 1 }} />
        <RouterButton
          variant="text"
          to="/bicycles/$id/history"
          params={{ id }}
          color="primary"
          endIcon={<History />}
          size="small"
        >
          詳細の確認
        </RouterButton>
      </Stack>
      <BicycleTimeline events={events} memo />
    </Paper>
  );
};
