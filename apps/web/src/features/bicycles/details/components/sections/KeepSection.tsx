import { Divider, Grid, type GridBaseProps, Paper, Stack, Typography } from '@mui/material';

import type { Bicycle } from 'lambda-api';
import { ReadOnlyField } from 'mui-ex';

import { useAppContext } from '@/contexts/app-context';
import { useBgColor } from './funcs';

type Props = {
  bicycle: Bicycle;
};

export const KeepSection = ({ bicycle }: Props) => {
  const { storageLocation, serialTag } = bicycle;
  const hasData = Boolean(storageLocation) || Boolean(serialTag);
  const bgcolor = useBgColor(hasData);
  const { labels } = useAppContext();

  const outerSize: GridBaseProps['size'] = { xs: 12, sm: 6 };
  const innerSize: GridBaseProps['size'] = { xs: 12, xl: 6 };

  return (
    <Paper sx={{ bgcolor }}>
      <Stack direction="row" spacing={1} sx={{ p: 2 }}>
        <Typography variant="h5">保管情報</Typography>
      </Stack>
      <Divider />
      <Grid container spacing={2} sx={{ p: 2 }}>
        <Grid size={outerSize}>
          <Grid container spacing={2}>
            <Grid size={innerSize}>
              <ReadOnlyField
                label={labels.domain.storageLocation}
                value={storageLocation?.last?.storage.name}
              />
            </Grid>
            <Grid size={innerSize}>
              <ReadOnlyField label="場所" value={storageLocation?.last?.memo} />
            </Grid>
          </Grid>
        </Grid>
        <Grid size={outerSize}>
          <Grid container spacing={2}>
            <Grid size={innerSize}>
              <ReadOnlyField label="整理番号" value={serialTag?.last?.serialNo} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Paper>
  );
};
