import {
  Al<PERSON>,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Stack,
} from '@mui/material';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import { BodyStateSchema, bodyToState } from '@/features/bicycles/components/RhfBicycleBodyForm';
import {
  CopyOwnerStateSchema,
  RhfCopyOwnerForm,
  useOwnerInfoFromRegistrationNumber,
} from '@/features/bicycles/components/RhfCopyOwnerForm';
import { useInvalidateBicycle } from '@/models/bicycle';
import { Close, ContentPasteGo, Edit } from '@mui/icons-material';
import { isNullish } from 'common';
import type { Bicycle } from 'lambda-api';
import { RhfTextField } from 'mui-ex';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

type Props = {
  bicycle: Bicycle;
};

const StateSchema = z.object({
  body: BodyStateSchema,
  copyOwner: CopyOwnerStateSchema,
  memo: z.string(),
});
type State = z.infer<typeof StateSchema>;

export const OwnerCopyButton = (props: Props) => {
  const { bicycle } = props;
  const { labels, policeStations } = useAppContext();
  const [open, setOpen] = React.useState(false);
  const { control, watch, formState, handleSubmit } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      body: bodyToState(bicycle.body?.last, policeStations),
      copyOwner: { isCopy: true, ownerInfoId: undefined },
      memo: '',
    } as const satisfies State,
  });

  const registrationNumber = watch('body.registrationNumber');
  const { data: ownerInfo } = useOwnerInfoFromRegistrationNumber(bicycle.id, registrationNumber);

  const invalidate = useInvalidateBicycle();
  const { mutate, error } = trpc.bicycles.copyOwnerInfo.useMutation({
    onSuccess: () => {
      invalidate(bicycle.id);
      setOpen(false);
    },
  });
  const submit = () => {
    if (ownerInfo?.id) mutate({ bicycleId: bicycle.id, ownerInfoId: ownerInfo.id });
  };

  if (isNullish(ownerInfo) || bicycle.copiedOwnerInfo) return undefined;

  return (
    <>
      <IconButton size="small" color="primary" onClick={() => setOpen(true)}>
        <ContentPasteGo />
      </IconButton>
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        maxWidth="md"
        fullWidth
        scroll="paper"
        PaperProps={{
          component: 'form',
          onSubmit: handleSubmit(submit),
        }}
      >
        <DialogTitle>{`${labels.domain.owner}情報の${labels.action.copy}`}</DialogTitle>
        <DialogContent dividers>
          <Stack spacing={2}>
            <RhfCopyOwnerForm
              control={control}
              watch={watch}
              type="label"
              bicycleId={bicycle.id}
              copied={bicycle.copiedOwnerInfo}
            />
            <RhfTextField
              control={control}
              name="memo"
              label={labels.system.memo}
              multiline
              rows={4}
            />
            {error && (
              <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
                {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
              </Alert>
            )}
          </Stack>
        </DialogContent>
        <DialogActions>
          <TwoActionButtons
            primary={{
              type: 'submit',
              icon: Edit,
              label: labels.action.copy,
              loading: formState.isSubmitting,
            }}
            secondary={{
              icon: Close,
              end: true,
              label: labels.action.cancel,
              onClick: () => setOpen(false),
            }}
            spacing={1}
          />
        </DialogActions>
      </Dialog>
    </>
  );
};
