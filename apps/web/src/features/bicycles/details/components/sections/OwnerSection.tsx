import { Box, Divider, Grid, Paper, Stack, Typography } from '@mui/material';
import { useForm } from 'react-hook-form';
import { match } from 'ts-pattern';
import { z } from 'zod';

import type { Bicycle } from 'lambda-api';
import { ReadOnlyField } from 'mui-ex';

import { useAppContext } from '@/contexts/app-context';
import {
  OwnerStateSchema,
  RhfBicycleOwnerForm,
  ownerToState,
} from '@/features/bicycles/components/RhfBicycleOwnerForm';

import { SectionFooter } from '../SectionFooter';
import { OwnerCopyButton } from './OwnerCopyButton';
import { OwnerEditorButton } from './OwnerEditorButton';
import { useBgColor } from './funcs';

type Props = {
  bicycle: Bicycle;
};

const StateSchema = z.object({
  owner: OwnerStateSchema,
});
type State = z.infer<typeof StateSchema>;

export const OwnerSection = ({ bicycle }: Props) => {
  const owner = bicycle.owner?.last;

  const { labels } = useAppContext();
  const { control, watch } = useForm<State>({ defaultValues: { owner: ownerToState(owner) } });

  const bgcolor = useBgColor(Boolean(owner));
  const event = owner?.event;
  const copied = event?.type === 'copyOwnerInfo' ? '(コピー)' : '';
  const source = match(owner?.sourceType)
    .with('body', () => '車体')
    .with('policeReference', () => `警察照会${copied}`)
    .with('dltbReference', () => `ナンバープレート照会${copied}}`)
    .with('recipient', () => `返還受取人${copied}`)
    .with('keepExtend', () => `保管期限延長${copied}`)
    .with('manual', () => `所有者情報の編集${copied}`)
    .with(undefined, () => '-')
    .exhaustive();

  return (
    <Paper sx={{ bgcolor }}>
      <Stack direction="row" spacing={1} sx={{ p: 2, alignItems: 'center' }}>
        <Typography variant="h5">{`${labels.domain.owner}情報`}</Typography>
        <Box sx={{ flexGrow: 1 }} />
        <OwnerCopyButton bicycle={bicycle} />
        <OwnerEditorButton bicycle={bicycle} />
      </Stack>
      <Divider />
      <Stack spacing={2} sx={{ p: 2 }}>
        <RhfBicycleOwnerForm control={control} watch={watch} readOnly>
          <Grid size={{ xs: 12, md: 6 }}>
            <ReadOnlyField label="情報元" value={source} />
          </Grid>
        </RhfBicycleOwnerForm>
        {event && <SectionFooter event={event} />}
      </Stack>
    </Paper>
  );
};
