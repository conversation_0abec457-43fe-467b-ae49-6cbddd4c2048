import {
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Stack,
} from '@mui/material';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import {
  BodyStateSchema,
  RhfBicycleBodyForm,
  bodyStateToInput,
  bodyToState,
} from '@/features/bicycles/components/RhfBicycleBodyForm';
import { CopyOwnerStateSchema } from '@/features/bicycles/components/RhfCopyOwnerForm';
import { RhfBicycleImages } from '@/features/bicycles/components/rhf-bicycle-images/_RhfBicycleImages';
import { uploadBicycleImagesToS3 } from '@/features/bicycles/funcs';
import { useInvalidateBicycle } from '@/models/bicycle';
import { Close, Edit } from '@mui/icons-material';
import { BicycleTypeSchema, ImageSchema } from 'common';
import type { Bicycle } from 'lambda-api';
import { RhfTextField } from 'mui-ex';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

type Props = {
  bicycle: Bicycle;
};

const StateSchema = z.object({
  type: BicycleTypeSchema,
  body: BodyStateSchema,
  copyOwner: CopyOwnerStateSchema,
  images: z.array(ImageSchema),
  memo: z.string(),
});
type State = z.infer<typeof StateSchema>;

export const BodyEditorButton = (props: Props) => {
  const { bicycle } = props;
  const { labels, policeStations } = useAppContext();
  const [open, setOpen] = React.useState(false);
  const { control, watch, formState, handleSubmit } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      type: bicycle.type ?? 'bicycle',
      body: bodyToState(bicycle.body?.last, policeStations),
      copyOwner: { isCopy: false, ownerInfoId: undefined },
      images: [],
      memo: '',
    } as const satisfies State,
  });
  const invalidate = useInvalidateBicycle();
  const { mutate, error } = trpc.bicycles.edit.body.useMutation({
    onSuccess: () => {
      invalidate(bicycle.id);
      setOpen(false);
    },
  });

  const submit = async (state: State) => {
    const images = await uploadBicycleImagesToS3(bicycle.id, state.images);
    mutate({
      bicycleId: bicycle.id,
      images,
      body: bodyStateToInput(state.body, policeStations),
      ownerInfoId: state.copyOwner.ownerInfoId,
      memo: state.memo,
    });
  };
  return (
    <>
      <IconButton size="small" color="primary" onClick={() => setOpen(true)}>
        <Edit />
      </IconButton>
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        maxWidth="md"
        fullWidth
        scroll="paper"
        PaperProps={{
          component: 'form',
          onSubmit: handleSubmit(submit),
        }}
      >
        <DialogTitle>{`車体情報の${labels.action.edit}`}</DialogTitle>
        <DialogContent dividers>
          <Stack spacing={2}>
            <RhfBicycleBodyForm control={control} watch={watch} />
            <RhfBicycleImages control={control} watch={watch} />
            <RhfTextField
              control={control}
              name="memo"
              label={labels.system.memo}
              multiline
              rows={4}
            />
            {error && (
              <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
                {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
              </Alert>
            )}
          </Stack>
        </DialogContent>
        <DialogActions>
          <TwoActionButtons
            primary={{
              type: 'submit',
              icon: Edit,
              label: labels.action.save,
              loading: formState.isSubmitting,
            }}
            secondary={{
              icon: Close,
              end: true,
              label: labels.action.cancel,
              onClick: () => setOpen(false),
            }}
            spacing={1}
          />
        </DialogActions>
      </Dialog>
    </>
  );
};
