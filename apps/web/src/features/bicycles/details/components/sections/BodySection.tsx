import { Box, Divider, Paper, Stack, Typography } from '@mui/material';

import type { Bicycle } from 'lambda-api';

import {
  BodyStateSchema,
  RhfBicycleBodyForm,
  bodyToState,
} from '@/features/bicycles/components/RhfBicycleBodyForm';

import { useAppContext } from '@/contexts/app-context';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { SectionFooter } from '../SectionFooter';
import { BodyEditorButton } from './BodyEditorButton';
import { useBgColor } from './funcs';

type Props = {
  bicycle: Bicycle;
};
const StateSchema = z.object({
  body: BodyStateSchema,
});
type State = z.infer<typeof StateSchema>;

export const BodySection = ({ bicycle }: Props) => {
  const { body } = bicycle;
  const { policeStations } = useAppContext();
  const { control, watch } = useForm<State>({
    defaultValues: {
      body: bodyToState(body?.last, policeStations),
    } satisfies State,
  });

  const bgcolor = useBgColor(Boolean(body));
  const event = body?.versions?.at(-1)?.event;

  return (
    <Paper sx={{ bgcolor }}>
      <Stack direction="row" spacing={1} sx={{ p: 2, alignItems: 'center' }}>
        <Typography variant="h5">車体情報</Typography>
        <Box sx={{ flexGrow: 1 }} />
        <BodyEditorButton bicycle={bicycle} />
      </Stack>
      <Divider />
      <Stack spacing={2} sx={{ p: 2 }}>
        <RhfBicycleBodyForm control={control} watch={watch} readOnly />
        {event && <SectionFooter event={event} />}
      </Stack>
    </Paper>
  );
};
