import {
  <PERSON><PERSON>,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Stack,
} from '@mui/material';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import {
  OwnerStateSchema,
  RhfBicycleOwnerForm,
  ownerToState,
} from '@/features/bicycles/components/RhfBicycleOwnerForm';
import { RhfBicycleImages } from '@/features/bicycles/components/rhf-bicycle-images/_RhfBicycleImages';
import { uploadBicycleImagesToS3 } from '@/features/bicycles/funcs';
import { useInvalidateBicycle } from '@/models/bicycle';
import { Close, Edit } from '@mui/icons-material';
import { ImageSchema, isNonNullish } from 'common';
import type { Bicycle } from 'lambda-api';
import { RhfCheckbox, RhfTextField } from 'mui-ex';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

type Props = {
  bicycle: Bicycle;
};

const StateSchema = z.object({
  owner: OwnerStateSchema,
  updateOwnerInfo: z.boolean(),
  images: z.array(ImageSchema),
  memo: z.string(),
});
type State = z.infer<typeof StateSchema>;

export const OwnerEditorButton = (props: Props) => {
  const { bicycle } = props;
  const { labels } = useAppContext();
  const [open, setOpen] = React.useState(false);
  const { control, watch, formState, handleSubmit } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      owner: ownerToState(bicycle.owner?.last),
      updateOwnerInfo: true,
      images: [],
      memo: '',
    } as const satisfies State,
  });
  const invalidate = useInvalidateBicycle();
  const { mutate, error } = trpc.bicycles.edit.owner.useMutation({
    onSuccess: () => {
      invalidate(bicycle.id);
      setOpen(false);
    },
  });
  const submit = async (state: State) => {
    const images = await uploadBicycleImagesToS3(bicycle.id, state.images);
    mutate({
      bicycleId: bicycle.id,
      owner: state.owner,
      updateOwnerInfo: state.updateOwnerInfo,
      memo: state.memo,
      images,
    });
  };

  const registrationNumber = bicycle.body?.last?.registrationNumber;
  return (
    <>
      <IconButton size="small" color="primary" onClick={() => setOpen(true)}>
        <Edit />
      </IconButton>
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        maxWidth="md"
        fullWidth
        scroll="paper"
        PaperProps={{
          component: 'form',
          onSubmit: handleSubmit(submit),
        }}
      >
        <DialogTitle>{`${labels.domain.owner}情報の${labels.action.edit}`}</DialogTitle>
        <DialogContent dividers>
          <Stack spacing={2}>
            <RhfBicycleOwnerForm control={control} watch={watch} />
            {isNonNullish(registrationNumber) && (
              <RhfCheckbox
                control={control}
                name="updateOwnerInfo"
                label={`防犯登録番号の所有者情報を${labels.doing.update}`}
              />
            )}
            <RhfBicycleImages
              control={control}
              watch={watch}
              settings={{ min: 0, max: 20, guides: [] }}
            />
            <RhfTextField
              control={control}
              name="memo"
              label={labels.system.memo}
              multiline
              rows={4}
            />
            {error && (
              <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
                {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
              </Alert>
            )}
          </Stack>
        </DialogContent>
        <DialogActions>
          <TwoActionButtons
            primary={{
              type: 'submit',
              icon: Edit,
              label: `${labels.action.save}する`,
              loading: formState.isSubmitting,
            }}
            secondary={{
              icon: Close,
              end: true,
              label: labels.action.cancel,
              onClick: () => setOpen(false),
            }}
            spacing={1}
          />
        </DialogActions>
      </Dialog>
    </>
  );
};
