import { Al<PERSON>, Dialog, DialogActions, DialogContent, DialogTitle, Stack } from '@mui/material';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import {
  LocationStateSchema,
  RhfBicycleLocationForm,
  locationToState,
} from '@/features/bicycles/components/RhfBicycleLocationForm';
import { RhfBicycleImages } from '@/features/bicycles/components/rhf-bicycle-images/_RhfBicycleImages';
import { uploadBicycleImagesToS3 } from '@/features/bicycles/funcs';
import { useInvalidateBicycle } from '@/models/bicycle';
import { Close, Edit } from '@mui/icons-material';
import { ImageSchema } from 'common';
import type { Bicycle } from 'lambda-api';
import { RhfTextField } from 'mui-ex';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

type Props = {
  open: boolean;
  onClose: () => void;
  bicycle: Bicycle;
};

const StateSchema = z.object({
  location: LocationStateSchema,
  images: z.array(ImageSchema),
  memo: z.string(),
});
type State = z.infer<typeof StateSchema>;

export const LocationEditor = (props: Props) => {
  const { open, onClose, bicycle } = props;
  const { labels } = useAppContext();
  const { control, watch, formState, handleSubmit } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      location: locationToState(bicycle.location?.last, false),
      images: [],
      memo: '',
    } as const satisfies State,
  });
  const invalidate = useInvalidateBicycle();
  const { mutate, error } = trpc.bicycles.edit.location.useMutation({
    onSuccess: () => {
      invalidate(bicycle.id);
      onClose();
    },
  });
  const submit = async (state: State) => {
    const images = await uploadBicycleImagesToS3(bicycle.id, state.images);
    mutate({ bicycleId: bicycle.id, ...state, images });
  };
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      scroll="paper"
      PaperProps={{
        component: 'form',
        onSubmit: handleSubmit(submit),
      }}
    >
      <DialogTitle>{`${labels.domain.location}の${labels.action.edit}`}</DialogTitle>
      <DialogContent dividers>
        <Stack spacing={2}>
          <RhfBicycleLocationForm control={control} watch={watch} />
          <RhfBicycleImages
            control={control}
            watch={watch}
            settings={{ min: 0, max: 20, guides: [] }}
          />
          <RhfTextField
            control={control}
            name="memo"
            label={labels.system.memo}
            multiline
            rows={4}
          />
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
        </Stack>
      </DialogContent>
      <DialogActions>
        <TwoActionButtons
          primary={{
            type: 'submit',
            icon: Edit,
            label: labels.doing.save,
            loading: formState.isSubmitting,
          }}
          secondary={{
            icon: Close,
            end: true,
            label: labels.doing.cancel,
            onClick: onClose,
          }}
          spacing={1}
        />
      </DialogActions>
    </Dialog>
  );
};
