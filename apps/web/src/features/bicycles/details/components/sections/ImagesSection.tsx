import type { Bicycle } from 'lambda-api';

import { BicycleThumbnails } from '@/components/bicycles/images/BicycleThumbnails';
import { Box, Paper, Typography } from '@mui/material';
import { sortDate } from 'common';
import { useBgColor } from './funcs';

type Props = {
  bicycle: Bicycle;
};

export const ImagesSection = ({ bicycle }: Props) => {
  const images = bicycle.events
    .toSorted((a, b) => sortDate('desc', a.date, b.date))
    .flatMap((event) => event.images);
  const bgcolor = useBgColor(images.length !== 0);
  if (images.length === 0) return;
  return (
    <Paper sx={{ p: 2, bgcolor }}>
      {images.length !== 0 && <BicycleThumbnails images={images} size="large" chip />}
      {images.length === 0 && (
        <Box sx={{ height: 200, display: 'grid', placeContent: 'center' }}>
          <Typography color="textSecondary">写真がありません</Typography>
        </Box>
      )}
    </Paper>
  );
};
