import { Divider, Grid, type GridBaseProps, Paper, Stack, Typography } from '@mui/material';
import { match } from 'ts-pattern';

import type { Bicycle } from 'lambda-api';
import { ReadOnlyField } from 'mui-ex';

import { RouterButton } from '@/components/RouterButton';
import { useAppContext } from '@/contexts/app-context';
import { dateFormatter } from '@/funcs/date';
import { contractToLinkProps, contractToStatusLabel, referenceStatusLabel } from '@/models/keep';
import { useSidebar } from '@/stores/sidebar-open';
import { Description } from '@mui/icons-material';

type Props = {
  bicycle: Bicycle;
};

export const MultiStatusSection = ({ bicycle }: Props) => {
  const {
    announcementStatus,
    ownerStatus,
    referenceStatus,
    keepStatus,
    releaseContract: contract,
  } = bicycle;
  const { labels } = useAppContext();
  const [open] = useSidebar();

  const keepMessage = match(keepStatus)
    .with(undefined, () => '-')
    .with({ expired: true }, () => '処分可')
    .with({ expired: false, extended: false }, () => '処分不可')
    .with({ expired: false, extended: true }, () => '延長済み')
    .exhaustive();

  const size: GridBaseProps['size'] = { xs: 6, sm: 4, md: 3, lg: !open ? 3 : 4, xl: 3 };
  return (
    <Paper>
      <Stack direction="row" spacing={1} sx={{ p: 2 }}>
        <Typography variant="h5">ステータス</Typography>
      </Stack>
      <Divider />
      <Grid container spacing={2} sx={{ p: 2 }}>
        <Grid size={size}>
          <ReadOnlyField label="照会ステータス" value={referenceStatusLabel[referenceStatus]} />
        </Grid>
        <Grid size={size}>
          <ReadOnlyField
            label={labels.p.announcementStatus}
            value={labels.announcementStatus[announcementStatus]}
          />
        </Grid>
        <Grid size={size}>
          <ReadOnlyField label="保管期限" value={keepMessage} />
        </Grid>
        <Grid size={size}>
          <ReadOnlyField
            label="処分ステータス"
            value={contractToStatusLabel(contract, keepStatus?.expired ?? false)}
          />
        </Grid>
        {contract && (
          <Grid size={size}>
            <ReadOnlyField
              label={contract.status === 'scheduled' ? '処分予定日' : '処分日'}
              value={dateFormatter(contract.date)}
            />
          </Grid>
        )}
        {contract && (
          <Grid size={size} sx={{ display: 'flex', alignItems: 'flex-end' }}>
            <RouterButton
              variant="outlined"
              startIcon={<Description />}
              {...contractToLinkProps(contract)}
              fullWidth
            >
              契約を確認する
            </RouterButton>
          </Grid>
        )}
        <Grid size={size}>
          <ReadOnlyField label={labels.p.ownerStatus} value={labels.ownerStatus[ownerStatus]} />
        </Grid>
      </Grid>
    </Paper>
  );
};
