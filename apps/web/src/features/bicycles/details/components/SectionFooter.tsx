import { Grid } from '@mui/material';

import type { BicycleEvent } from 'lambda-api';
import { ReadOnlyField } from 'mui-ex';

import { BicycleThumbnails } from '@/components/bicycles/images/BicycleThumbnails';
import { useAppContext } from '@/contexts/app-context';

type Props = { event: BicycleEvent };

export const SectionFooter = ({ event }: Props) => {
  const { labels } = useAppContext();
  if (event.memo === null && event.images.length === 0) return;
  return (
    <Grid container spacing={2}>
      <Grid size={{ xs: 12, md: event.images.length !== 0 ? 6 : 12 }}>
        <ReadOnlyField label={labels.system.memo} value={event.memo} autoHeight />
      </Grid>
      <Grid size={{ xs: 12, md: 6 }}>
        <BicycleThumbnails images={event.images} size="medium" minCols={3} />
      </Grid>
    </Grid>
  );
};
