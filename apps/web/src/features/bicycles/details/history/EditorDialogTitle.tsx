import { TimelineDot } from '@mui/lab';
import { DialogTitle, Stack, Typography } from '@mui/material';

import type { Bicycle } from 'lambda-api';

import { useAppContext } from '@/contexts/app-context';
import { eventTypeToColor, eventTypeToIcon } from '@/models/bicycle';

type Props = {
  event: Bicycle['events'][number];
};

export const EditorDialogTitle = (props: Props) => {
  const { type } = props.event;
  const { labels } = useAppContext();
  const color = eventTypeToColor(type);
  const Icon = eventTypeToIcon(type);
  return (
    <DialogTitle component={Stack} direction="row" alignItems="center" spacing={1}>
      <TimelineDot sx={{ backgroundColor: color['300'] }}>
        <Icon />
      </TimelineDot>
      <Typography variant="h5">{`${labels.et[type]}イベントの編集`}</Typography>
    </DialogTitle>
  );
};
