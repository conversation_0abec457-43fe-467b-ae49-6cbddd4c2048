import { Box, Paper, type Theme, useMediaQuery } from '@mui/material';
import type { SystemCssProperties } from '@mui/system';
import React from 'react';

import { isNullish } from 'common';
import type { Bicycle } from 'lambda-api';

import { BicycleTimeline } from '@/components/bicycles/BicycleTimeline';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { useSidebar } from '@/stores/sidebar-open';
import { EventSection } from './EventSection';

import { bicyclesHistoryMeta } from '@/router/routes/bicycles-history-route';
import { useTheme } from '@/theme';
import { Timeline, timelineItemClasses } from '@mui/lab';
import { createEventId } from './types';

const navigationWidth = 320;

type SectionState = { key: string; percent: number; ratio: number };

const calcActiveEventId = (state: SectionState[]) => {
  const first = state.find(({ percent, ratio }) => percent > 50 || ratio > 0.5);
  return first?.key;
};

type Props = {
  bicycle: Bicycle;
};

export const BicycleHistory = ({ bicycle }: Props) => {
  const title = bicyclesHistoryMeta.useTitle();
  const theme = useTheme();
  const lgDown = useMediaQuery<Theme>((theme) => theme.breakpoints.down('lg'));

  const [sidebar] = useSidebar();
  const ref = React.useRef<HTMLDivElement | null>(null);

  const [state, setState] = React.useState<SectionState[]>([
    ...bicycle.events.map((event) => ({ key: event.id, ratio: 0, percent: 0 })),
  ]);

  if (isNullish(bicycle)) return <LoadingSpinner />;

  const handleScroll = (key: string) => (percent: number, ratio: number) => {
    setState((s) => s.map((state) => (state.key === key ? { ...state, percent, ratio } : state)));
  };

  const handleTimelineEventClick = (eventId: string) => {
    const element = document.getElementById(createEventId(eventId));
    if (!element) throw new Error(`Element not found: ${eventId}`);
    element.scrollIntoView();
  };

  const activeEventId = calcActiveEventId(state);
  const pl = `calc(${navigationWidth}px + ${theme.spacing(2)})`;
  const navigationDisplay: SystemCssProperties<Theme>['display'] = {
    xs: 'none',
    lg: sidebar ? 'none' : 'block',
    xl: 'block',
  };
  return (
    <MainLayout title={title} maxWidth="xl" scrollable={lgDown}>
      <Box
        sx={{
          width: 1,
          height: 1,
          position: 'relative',
        }}
      >
        <Paper
          sx={{
            display: navigationDisplay,
            position: 'absolute',
            left: 0,
            top: 0,
            zIndex: 1,
            p: 2,
            width: navigationWidth,
          }}
        >
          <BicycleTimeline
            events={bicycle.events}
            memo
            activeEventId={activeEventId}
            onClick={handleTimelineEventClick}
          />
        </Paper>
        <Box
          ref={ref}
          sx={{
            pl: { xs: 0, lg: sidebar ? 0 : pl, xl: pl },
            width: 1,
            height: 1,
            overflow: 'auto',
            scrollBehavior: 'smooth',
          }}
        >
          <Timeline
            sx={{
              m: 0,
              p: 0,
              // 参考: https://mui.com/material-ui/react-timeline/#left-aligned-with-no-opposite-content
              [`& .${timelineItemClasses.missingOppositeContent}:before`]: { flex: 0, p: 0 },
            }}
          >
            {bicycle.events.map((event, i) => (
              <EventSection
                key={`${event.id}:${event.updatedAt}`}
                root={ref}
                onScroll={handleScroll(event.id)}
                event={event}
                bicycle={bicycle}
                connector={i !== bicycle.events.length - 1}
              />
            ))}
          </Timeline>
          {bicycle.events.length > 1 && (
            <Box sx={{ display: navigationDisplay, height: 'calc(100% - 60px)' }} />
          )}
        </Box>
      </Box>
    </MainLayout>
  );
};
