import type React from 'react';

import { Paper, Stack, type Theme, useMediaQuery } from '@mui/material';

type Props = { children: React.JSX.Element };

export const SectionBodyPaper: React.FC<Props> = ({ children }) => {
  const isXs = useMediaQuery<Theme>((theme) => theme.breakpoints.only('xs'));
  if (!isXs)
    return (
      <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
        {children}
      </Stack>
    );
  return <>{children}</>;
};
