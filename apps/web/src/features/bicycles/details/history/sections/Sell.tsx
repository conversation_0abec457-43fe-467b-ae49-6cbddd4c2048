import { RouterButton } from '@/components/RouterButton';
import { Description } from '@mui/icons-material';

import { SectionContainer } from '../SectionContainer';

type Props = React.ComponentProps<typeof SectionContainer>;

export const Sell: React.FC<Props> = (props) => {
  const contract = props.event.sellContract;
  if (contract === null) throw new Error('Sell contract is null');
  return (
    <SectionContainer
      {...props}
      actions={
        <RouterButton
          variant="outlined"
          startIcon={<Description />}
          size="small"
          to="/sell-contracts/$id"
          params={{ id: contract.id }}
        >
          売却契約を確認する
        </RouterButton>
      }
    />
  );
};
