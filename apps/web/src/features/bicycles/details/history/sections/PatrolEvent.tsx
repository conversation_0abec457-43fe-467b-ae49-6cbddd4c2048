import { Divider } from '@mui/material';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  LocationStateSchema,
  RhfBicycleLocationForm,
  locationToState,
} from '@/features/bicycles/components/RhfBicycleLocationForm';

import { useAppContext } from '@/contexts/app-context';
import { isPatrolEventType } from 'models';
import type React from 'react';
import {
  BodyStateSchema,
  RhfBicycleBodyForm,
  bodyToState,
} from '../../../components/RhfBicycleBodyForm';
import { SectionFooter } from '../../components/SectionFooter';
import { SectionBodyPaper } from '../SectionBodyPaper';
import { SectionContainer } from '../SectionContainer';
import { PatrolEventEditor } from './PatrolEventEditor';

type Props = React.ComponentProps<typeof SectionContainer>;

const StateSchema = z.object({
  location: LocationStateSchema,
  body: BodyStateSchema,
});
type State = z.infer<typeof StateSchema>;

export const PatrolEvent: React.FC<Props> = (props) => {
  const { event, bicycle } = props;
  const { type, location, body } = event;
  const { policeStations } = useAppContext();
  const { control, watch } = useForm<State>({
    defaultValues: {
      location: locationToState(location),
      body: bodyToState(body, policeStations),
    } as const satisfies State,
  });

  if (!isPatrolEventType(type)) throw new Error(`Unsupported event type: ${type}`);

  return (
    <SectionContainer {...props} editor={<PatrolEventEditor event={event} bicycle={bicycle} />}>
      <SectionBodyPaper>
        <>
          <RhfBicycleLocationForm control={control} watch={watch} readOnly />
          {body && <RhfBicycleBodyForm control={control} watch={watch} eventType={type} />}
          <Divider />
          <SectionFooter event={event} />
        </>
      </SectionBodyPaper>
    </SectionContainer>
  );
};
