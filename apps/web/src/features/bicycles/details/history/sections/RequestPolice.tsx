import { RouterButton } from '@/components/RouterButton';
import { Description } from '@mui/icons-material';

import { SectionContainer } from '../SectionContainer';

type Props = React.ComponentProps<typeof SectionContainer>;

export const RequestPolice: React.FC<Props> = (props) => {
  const request = props.event.policeRequest;
  if (request === null) throw new Error('Serial tag is null');
  return (
    <SectionContainer
      {...props}
      actions={
        <RouterButton
          variant="outlined"
          startIcon={<Description />}
          size="small"
          to="/police-references/$id"
          params={{ id: request.referenceId }}
        >
          警察照会を確認する
        </RouterButton>
      }
    />
  );
};
