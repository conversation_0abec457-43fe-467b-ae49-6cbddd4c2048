import { Divider } from '@mui/material';

import { useAppContext } from '@/contexts/app-context';
import {
  BodyStateSchema,
  RhfBicycleBodyForm,
  bodyToState,
} from '@/features/bicycles/components/RhfBicycleBodyForm';
import {
  RhfBicycleStorageLocationForm,
  storageLocationToState,
  useStorageLocationStateSchema,
} from '@/features/bicycles/components/RhfBicycleStorageLocationForm';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { SectionFooter } from '../../components/SectionFooter';
import { SectionBodyPaper } from '../SectionBodyPaper';
import { SectionContainer } from '../SectionContainer';

type Props = React.ComponentProps<typeof SectionContainer>;

const useStateSchema = () =>
  z.object({
    storageLocation: useStorageLocationStateSchema(),
    body: BodyStateSchema,
  });
type State = z.infer<ReturnType<typeof useStateSchema>>;

export const Store: React.FC<Props> = (props) => {
  const { event } = props;
  const { policeStations } = useAppContext();
  const { control, watch } = useForm<State>({
    defaultValues: {
      storageLocation: storageLocationToState(event.storageLocation),
      body: bodyToState(event.body, policeStations),
    } satisfies State,
  });
  return (
    <SectionContainer {...props}>
      <SectionBodyPaper>
        <>
          <RhfBicycleStorageLocationForm control={control} readOnly />
          {event.body && <RhfBicycleBodyForm control={control} watch={watch} readOnly />}
          <Divider />
          <SectionFooter event={event} />
        </>
      </SectionBodyPaper>
    </SectionContainer>
  );
};
