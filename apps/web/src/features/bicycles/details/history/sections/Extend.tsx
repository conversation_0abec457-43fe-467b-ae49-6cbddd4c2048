import { Divider } from '@mui/material';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useAppContext } from '@/contexts/app-context';
import { RhfDatePicker } from 'mui-ex';
import {
  OwnerStateSchema,
  RhfBicycleOwnerForm,
  ownerToState,
} from '../../../components/RhfBicycleOwnerForm';
import { SectionFooter } from '../../components/SectionFooter';
import { SectionBodyPaper } from '../SectionBodyPaper';
import { SectionContainer } from '../SectionContainer';

type Props = React.ComponentProps<typeof SectionContainer>;

const StateSchema = z.object({ owner: OwnerStateSchema, deadline: z.coerce.date() });
type State = z.infer<typeof StateSchema>;

export const Extend: React.FC<Props> = (props) => {
  const { labels } = useAppContext();
  const { event } = props;
  if (!event.deadline?.date) throw new Error('Error: Deadline is missing');
  const { control, watch } = useForm<State>({
    defaultValues: {
      owner: ownerToState(event.owner),
      deadline: new Date(event.deadline.date),
    } as const satisfies State,
  });
  return (
    <SectionContainer {...props}>
      <SectionBodyPaper>
        <>
          <RhfDatePicker
            control={control}
            name="deadline"
            readOnly
            label={labels.et.extendDeadline}
          />
          <RhfBicycleOwnerForm control={control} watch={watch} readOnly />
          <Divider />
          <SectionFooter event={event} />
        </>
      </SectionBodyPaper>
    </SectionContainer>
  );
};
