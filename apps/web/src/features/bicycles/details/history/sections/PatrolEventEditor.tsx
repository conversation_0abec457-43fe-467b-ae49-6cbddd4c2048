import { Alert, Dialog, DialogActions, DialogContent, IconButton, Stack } from '@mui/material';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import {
  LocationStateSchema,
  RhfBicycleLocationForm,
  locationToState,
} from '@/features/bicycles/components/RhfBicycleLocationForm';
import { RhfBicycleImages } from '@/features/bicycles/components/rhf-bicycle-images/_RhfBicycleImages';
import { uploadBicycleImagesToS3 } from '@/features/bicycles/funcs';
import { useInvalidateBicycle } from '@/models/bicycle';
import { Close, Edit } from '@mui/icons-material';
import { ImageSchema } from 'common';
import type { Bicycle } from 'lambda-api';
import { RhfTextField } from 'mui-ex';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { EditorDialogTitle } from '../EditorDialogTitle';
import { LocationCaution } from '../cautions';

type Props = {
  event: Bicycle['events'][number];
  bicycle: Bicycle;
};

const StateSchema = z.object({
  location: LocationStateSchema,
  images: z.array(ImageSchema),
  memo: z.string(),
});
type State = z.infer<typeof StateSchema>;

export const PatrolEventEditor = (props: Props) => {
  const { event, bicycle } = props;
  const { id, bicycleId } = event;
  const { labels } = useAppContext();
  const [open, setOpen] = React.useState(false);
  const { control, watch, formState, handleSubmit } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      location: locationToState(event.location, false),
      images: event.images,
      memo: event.memo ?? '',
    } as const satisfies State,
  });
  const invalidate = useInvalidateBicycle();
  const { mutate, error } = trpc.bicycles.edit.event.useMutation({
    onSuccess: () => {
      invalidate(bicycleId);
      setOpen(false);
    },
  });
  const submit = async (state: State) => {
    const images = await uploadBicycleImagesToS3(bicycleId, state.images);
    mutate({ eventId: id, ...state, images });
  };

  return (
    <>
      <IconButton color="primary" size="small" onClick={() => setOpen(true)}>
        <Edit />
      </IconButton>
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        maxWidth="md"
        fullWidth
        scroll="paper"
        PaperProps={{
          component: 'form',
          onSubmit: handleSubmit(submit),
        }}
      >
        <EditorDialogTitle event={event} />
        <DialogContent dividers>
          <Stack spacing={2}>
            <LocationCaution event={event} bicycle={bicycle} />
            <RhfBicycleLocationForm control={control} watch={watch} />
            <RhfBicycleImages
              control={control}
              watch={watch}
              settings={{ min: 0, max: 20, guides: [] }}
            />
            <RhfTextField
              control={control}
              name="memo"
              label={labels.system.memo}
              multiline
              rows={4}
            />
            {error && (
              <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
                {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
              </Alert>
            )}
          </Stack>
        </DialogContent>
        <DialogActions>
          <TwoActionButtons
            primary={{
              type: 'submit',
              icon: Edit,
              label: `${labels.action.save}する`,
              loading: formState.isSubmitting,
            }}
            secondary={{
              icon: Close,
              end: true,
              label: labels.action.cancel,
              onClick: () => setOpen(false),
            }}
            spacing={1}
          />
        </DialogActions>
      </Dialog>
    </>
  );
};
