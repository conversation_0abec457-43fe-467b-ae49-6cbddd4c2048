import { Grid } from '@mui/material';

import { ReadOnlyField } from 'mui-ex';
import { SectionBodyPaper } from '../SectionBodyPaper';
import { SectionContainer } from '../SectionContainer';

type Props = React.ComponentProps<typeof SectionContainer>;

export const Numbering: React.FC<Props> = (props) => {
  const serialTag = props.event.serialTag;
  if (serialTag === null) throw new Error('Serial tag is null');
  return (
    <SectionContainer {...props}>
      <SectionBodyPaper>
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
            <ReadOnlyField label="整理番号" value={serialTag.serialNo} />
          </Grid>
        </Grid>
      </SectionBodyPaper>
    </SectionContainer>
  );
};
