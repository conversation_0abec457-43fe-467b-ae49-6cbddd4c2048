import { Divider } from '@mui/material';

import { useAppContext } from '@/contexts/app-context';
import {
  BodyStateSchema,
  RhfBicycleBodyForm,
  bodyToState,
} from '@/features/bicycles/components/RhfBicycleBodyForm';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { SectionFooter } from '../../components/SectionFooter';
import { SectionBodyPaper } from '../SectionBodyPaper';
import { SectionContainer } from '../SectionContainer';

type Props = React.ComponentProps<typeof SectionContainer>;

const StateSchema = z.object({
  body: BodyStateSchema,
});
type State = z.infer<typeof StateSchema>;

export const EditBody: React.FC<Props> = (props) => {
  const { event } = props;
  const { policeStations } = useAppContext();
  const { control, watch } = useForm<State>({
    defaultValues: {
      body: bodyToState(event.body, policeStations),
    } satisfies State,
  });
  return (
    <SectionContainer {...props}>
      <SectionBodyPaper>
        <>
          <RhfBicycleBodyForm control={control} watch={watch} readOnly />
          <Divider />
          <SectionFooter event={event} />
        </>
      </SectionBodyPaper>
    </SectionContainer>
  );
};
