import { RouterButton } from '@/components/RouterButton';
import { Description } from '@mui/icons-material';

import { SectionContainer } from '../SectionContainer';

type Props = React.ComponentProps<typeof SectionContainer>;

export const Transfer: React.FC<Props> = (props) => {
  const contract = props.event.transferContract;
  if (contract === null) throw new Error('Transfer contract is null');
  return (
    <SectionContainer
      {...props}
      actions={
        <RouterButton
          variant="outlined"
          startIcon={<Description />}
          size="small"
          to="/transfer-contracts/$id"
          params={{ id: contract.id }}
        >
          譲渡契約を確認する
        </RouterButton>
      }
    />
  );
};
