import { useAppContext } from '@/contexts/app-context';
import { Divider } from '@mui/material';
import { RhfSingleFreeSolo } from 'mui-ex';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { SectionFooter } from '../../components/SectionFooter';
import { SectionBodyPaper } from '../SectionBodyPaper';
import { SectionContainer } from '../SectionContainer';

type Props = React.ComponentProps<typeof SectionContainer>;
const StateSchema = z.object({
  assessment: z.object({
    price: z.number().nullable(),
  }),
});
type State = z.infer<typeof StateSchema>;

export const Assess: React.FC<Props> = (props) => {
  const { event } = props;
  const { labels, priceSuggestions } = useAppContext();
  if (!event.assessment) throw new Error('Event does not have assessment data');
  const { control } = useForm<State>({
    defaultValues: {
      assessment: event.assessment,
    },
  });
  return (
    <SectionContainer {...props}>
      <SectionBodyPaper>
        <>
          <RhfSingleFreeSolo
            control={control}
            name="assessment.price"
            label={labels.assessment.price}
            options={priceSuggestions.map((s) => String(s.price))}
            readOnly
          />
          <Divider />
          <SectionFooter event={event} />
        </>
      </SectionBodyPaper>
    </SectionContainer>
  );
};
