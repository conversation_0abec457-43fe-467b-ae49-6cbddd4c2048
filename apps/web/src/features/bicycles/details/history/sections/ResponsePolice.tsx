import { Grid, Paper } from '@mui/material';

import { RouterButton } from '@/components/RouterButton';
import { Description } from '@mui/icons-material';

import { ReadOnlyField, RhfCheckbox } from 'mui-ex';
import { useForm } from 'react-hook-form';
import { SectionContainer } from '../SectionContainer';

type Props = React.ComponentProps<typeof SectionContainer>;

type State = NonNullable<Props['event']['policeResponse']>;

export const ResponsePolice: React.FC<Props> = (props) => {
  const response = props.event.policeResponse;
  const { control } = useForm<State>({ defaultValues: { ...response } });
  if (response === null) throw new Error('Serial tag is null');
  return (
    <SectionContainer
      {...props}
      actions={
        <RouterButton
          variant="outlined"
          startIcon={<Description />}
          size="small"
          to="/police-references/$id"
          params={{ id: response.referenceId }}
        >
          警察照会を確認する
        </RouterButton>
      }
    >
      <Paper sx={{ p: 2 }}>
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
            <ReadOnlyField label="氏名" value={response.name} />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
            <ReadOnlyField label="郵便番号" value={response.postalCode} />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
            <ReadOnlyField label="住所" value={response.address} />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
            <RhfCheckbox control={control} name="theftReport" label="盗難届" readOnly />
          </Grid>
        </Grid>
      </Paper>
    </SectionContainer>
  );
};
