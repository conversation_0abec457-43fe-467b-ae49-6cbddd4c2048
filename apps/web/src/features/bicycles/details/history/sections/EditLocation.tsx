import { Divider } from '@mui/material';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  LocationStateSchema,
  RhfBicycleLocationForm,
  locationToState,
} from '@/features/bicycles/components/RhfBicycleLocationForm';

import { SectionFooter } from '../../components/SectionFooter';
import { SectionBodyPaper } from '../SectionBodyPaper';
import { SectionContainer } from '../SectionContainer';

type Props = React.ComponentProps<typeof SectionContainer>;

const StateSchema = z.object({ location: LocationStateSchema });
type State = z.infer<typeof StateSchema>;

export const EditLocation: React.FC<Props> = (props) => {
  const { event } = props;
  const { location } = event;
  const { control, watch } = useForm<State>({
    defaultValues: {
      location: locationToState(location),
    } as const satisfies State,
  });

  return (
    <SectionContainer {...props}>
      <SectionBodyPaper>
        <>
          <RhfBicycleLocationForm control={control} watch={watch} readOnly />
          <Divider />
          <SectionFooter event={event} />
        </>
      </SectionBodyPaper>
    </SectionContainer>
  );
};
