import { useAppContext } from '@/contexts/app-context';
import { dateFormatter } from '@/funcs/date';
import { Grid } from '@mui/material';
import { isNullish } from 'common';
import { ReadOnlyField } from 'mui-ex';
import { SectionBodyPaper } from '../SectionBodyPaper';
import { SectionContainer } from '../SectionContainer';

type Props = React.ComponentProps<typeof SectionContainer>;

export const ScheduleAnnounce: React.FC<Props> = (props) => {
  const { labels } = useAppContext();
  const announcement = props.bicycle.announcement?.last;
  if (isNullish(announcement)) throw new Error('Announcement is null');
  return (
    <SectionContainer {...props}>
      <SectionBodyPaper>
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, sm: 6 }}>
            <ReadOnlyField
              label={labels.p.announcementStart}
              value={dateFormatter(announcement.start)}
            />
          </Grid>
        </Grid>
      </SectionBodyPaper>
    </SectionContainer>
  );
};
