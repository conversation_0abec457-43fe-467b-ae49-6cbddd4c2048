import { Divider, Stack, Typography } from '@mui/material';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  OwnerStateSchema,
  RhfBicycleOwnerForm,
  ownerToState,
} from '../../../components/RhfBicycleOwnerForm';
import {
  ReturnToOwnerStateSchema,
  RhfBicycleReturnToOwnerForm,
  returnToOwnerToState,
} from '../../../components/RhfBicycleReturnToOwnerForm';
import { SectionFooter } from '../../components/SectionFooter';
import { SectionBodyPaper } from '../SectionBodyPaper';
import { SectionContainer } from '../SectionContainer';

type Props = React.ComponentProps<typeof SectionContainer>;

const StateSchema = z.object({ owner: OwnerStateSchema, returnToOwner: ReturnToOwnerStateSchema });
type State = z.infer<typeof StateSchema>;

export const ReturnToOwner: React.FC<Props> = (props) => {
  const { event } = props;
  const { control, watch } = useForm<State>({
    defaultValues: {
      returnToOwner: returnToOwnerToState(event),
      owner: ownerToState(event.owner),
    } as const satisfies State,
  });
  return (
    <SectionContainer {...props}>
      <SectionBodyPaper>
        <>
          <RhfBicycleReturnToOwnerForm control={control} watch={watch} readOnly />
          <Stack spacing={1}>
            <Typography color="textSecondary">受取人情報</Typography>
            <RhfBicycleOwnerForm control={control} watch={watch} readOnly />
          </Stack>
          <Divider />
          <SectionFooter event={event} />
        </>
      </SectionBodyPaper>
    </SectionContainer>
  );
};
