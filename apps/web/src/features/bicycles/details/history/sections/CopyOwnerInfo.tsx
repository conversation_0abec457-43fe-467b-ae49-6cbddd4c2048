import { Divider } from '@mui/material';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  OwnerStateSchema,
  RhfBicycleOwnerForm,
  ownerToState,
} from '../../../components/RhfBicycleOwnerForm';
import { SectionFooter } from '../../components/SectionFooter';
import { SectionBodyPaper } from '../SectionBodyPaper';
import { SectionContainer } from '../SectionContainer';

type Props = React.ComponentProps<typeof SectionContainer>;

const StateSchema = z.object({ owner: OwnerStateSchema });
type State = z.infer<typeof StateSchema>;

export const CopyOwnerInfo: React.FC<Props> = (props) => {
  const { event } = props;
  const { control, watch } = useForm<State>({
    defaultValues: {
      owner: ownerToState(event.owner),
    } as const satisfies State,
  });
  return (
    <SectionContainer {...props}>
      <SectionBodyPaper>
        <>
          <RhfBicycleOwnerForm control={control} watch={watch} readOnly />
          <Divider />
          <SectionFooter event={event} />
        </>
      </SectionBodyPaper>
    </SectionContainer>
  );
};
