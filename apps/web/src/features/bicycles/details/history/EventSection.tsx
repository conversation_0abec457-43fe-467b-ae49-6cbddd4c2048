import { match } from 'ts-pattern';

import { SectionContainer } from './SectionContainer';
import { Assess } from './sections/Assess';
import { CancelAnnounceSchedule } from './sections/CancelAnnounceSchedule';
import { CancelDispose } from './sections/CancelDispose';
import { CancelDisposeSchedule } from './sections/CancelDisposeSchedule';
import { CancelPolice } from './sections/CancelPolice';
import { CancelSell } from './sections/CancelSell';
import { CancelSellSchedule } from './sections/CancelSellSchedule';
import { CancelTransfer } from './sections/CancelTransfer';
import { CancelTransferSchedule } from './sections/CancelTransferSchedule';
import { CopyOwnerInfo } from './sections/CopyOwnerInfo';
import { Dispose } from './sections/Dispose';
import { EditBody } from './sections/EditBody';
import { EditLocation } from './sections/EditLocation';
import { EditOwner } from './sections/EditOwner';
import { EditScheduleSell } from './sections/EditScheduleSell';
import { Extend } from './sections/Extend';
import { Numbering } from './sections/Numbering';
import { PatrolEvent } from './sections/PatrolEvent';
import { RequestPolice } from './sections/RequestPolice';
import { ResponsePolice } from './sections/ResponsePolice';
import { ReturnToOwner } from './sections/ReturnToOwner';
import { ScheduleAnnounce } from './sections/ScheduleAnnounce';
import { Sell } from './sections/Sell';
import { Store } from './sections/Store';
import { Transfer } from './sections/Transfer';

type Props = React.ComponentProps<typeof SectionContainer>;

export const EventSection = (props: Props) => {
  const { event } = props;
  return match(event.type)
    .with('find', () => <PatrolEvent {...props} />)
    .with('ensureAbandoned', () => <PatrolEvent {...props} />)
    .with('remove', () => <PatrolEvent {...props} />)
    .with('numbering', () => <Numbering {...props} />)
    .with('printSerialTag', () => <SectionContainer {...props} />)
    .with('store', () => <Store {...props} />)
    .with('returnToOwner', () => <ReturnToOwner {...props} />)
    .with('requestPolice', () => <RequestPolice {...props} />)
    .with('cancelPolice', () => <CancelPolice {...props} />)
    .with('receivePolice', () => <ResponsePolice {...props} />)
    .with('scheduleSell', () => <Sell {...props} />)
    .with('sell', () => <Sell {...props} />)
    .with('cancelSell', () => <CancelSell {...props} />)
    .with('cancelSellSchedule', () => <CancelSellSchedule {...props} />)
    .with('editScheduleSell', () => <EditScheduleSell {...props} />)
    .with('editLocation', () => <EditLocation {...props} />)
    .with('editBody', () => <EditBody {...props} />)
    .with('editOwner', () => <EditOwner {...props} />)
    .with('extendDeadline', () => <Extend {...props} />)
    .with('assess', () => <Assess {...props} />)
    .with('copyOwnerInfo', () => <CopyOwnerInfo {...props} />)
    .with('scheduleTransfer', () => <Transfer {...props} />)
    .with('transfer', () => <Transfer {...props} />)
    .with('cancelTransfer', () => <CancelTransfer {...props} />)
    .with('cancelTransferSchedule', () => <CancelTransferSchedule {...props} />)
    .with('startAnnouncement', () => <ScheduleAnnounce {...props} />)
    .with('updateAnnouncement', () => <ScheduleAnnounce {...props} />)
    .with('cancelAnnouncement', () => <CancelAnnounceSchedule {...props} />)
    .with('scheduleDispose', () => <Dispose {...props} />)
    .with('dispose', () => <Dispose {...props} />)
    .with('cancelDispose', () => <CancelDispose {...props} />)
    .with('cancelDisposeSchedule', () => <CancelDisposeSchedule {...props} />)
    .otherwise(() => `${event.type} は未対応です`);
};
