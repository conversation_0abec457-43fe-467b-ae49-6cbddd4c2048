import type React from 'react';

import {
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
} from '@mui/lab';
import { Box, Divider, Paper, Stack, type Theme, Typography, useMediaQuery } from '@mui/material';
import { useInView } from 'react-intersection-observer';

import type { Bicycle } from 'lambda-api';

import { useAppContext } from '@/contexts/app-context';
import { datetimeFormatter } from '@/funcs/date';
import { eventTypeToColor, eventTypeToIcon } from '@/models/bicycle';
import { createEventId } from './types';

type Props = {
  root: React.RefObject<HTMLDivElement | null>;
  onScroll: (percent: number, ratio: number) => void;
  event: Bicycle['events'][number];
  bicycle: Bicycle;
  children?: React.JSX.Element;
  connector?: boolean;
  actions?: React.JSX.Element;
  editor?: React.JSX.Element;
};

export const SectionContainer: React.FC<Props> = ({
  root,
  onScroll,
  event,
  connector,
  actions,
  editor,
  children,
}) => {
  const { labels } = useAppContext();
  const [ref] = useInView({
    root: root.current,
    threshold: [0, 0.2, 0.4, 0.6, 0.8, 1],
    onChange: (_, { rootBounds, intersectionRect, intersectionRatio }) => {
      if (rootBounds === null) return;
      const percent = (100 * intersectionRect.height) / rootBounds.height;
      onScroll(percent, intersectionRatio);
    },
  });
  const isXs = useMediaQuery<Theme>((theme) => theme.breakpoints.only('xs'));
  const id = createEventId(event.id);
  const color = eventTypeToColor(event.type);
  const Icon = eventTypeToIcon(event.type);
  if (!isXs)
    return (
      <TimelineItem ref={ref} sx={{ p: 0 }}>
        <Box id={id} />
        <TimelineSeparator>
          <TimelineDot sx={{ backgroundColor: color['300'] }}>
            <Icon />
          </TimelineDot>
          {connector && <TimelineConnector />}
        </TimelineSeparator>
        <TimelineContent sx={{ pr: 0, pb: 2 }}>
          <Stack direction="row" spacing={2} alignItems="end" sx={{ py: 1 }}>
            <Typography variant="h5">{labels.et[event.type]}</Typography>
            <Typography color="textSecondary">{datetimeFormatter(event.date)}</Typography>
            <Stack direction="row" spacing={1} alignItems="center" sx={{ pl: 1 }}>
              {actions}
            </Stack>
            <Box sx={{ flexGrow: 1 }} />
            {editor}
          </Stack>
          {children}
        </TimelineContent>
      </TimelineItem>
    );
  return (
    <Stack component={children ? Paper : 'div'} spacing={1} sx={{ p: 2, mb: 2 }}>
      <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
        <TimelineDot sx={{ backgroundColor: color['300'] }}>
          <Icon />
        </TimelineDot>
        <Typography variant="h5">{labels.et[event.type]}</Typography>
        <Box sx={{ flexGrow: 1 }} />
        {editor}
      </Stack>
      <Typography color="textSecondary">{datetimeFormatter(event.date)}</Typography>
      <Stack direction="row" spacing={1} alignItems="center" sx={{ pl: 1 }}>
        {actions}
      </Stack>
      {children && <Divider />}
      {children}
    </Stack>
  );
};
