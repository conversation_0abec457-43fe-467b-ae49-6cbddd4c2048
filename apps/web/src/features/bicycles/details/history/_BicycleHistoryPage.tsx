import { useParams } from '@tanstack/react-router';

import { isNullish } from 'common';

import { trpc } from '@/api';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { BicycleHistory } from './BicycleHistory';

export const BicycleHistoryPage = () => {
  const { id } = useParams({ from: '/app/bicycles/$id/history' });
  const { data: bicycle } = trpc.bicycles.get.useQuery({ id, from: '/bicycles/$id/history' });

  if (isNullish(bicycle)) return <LoadingSpinner />;

  return <BicycleHistory bicycle={bicycle} />;
};
