import { Typography } from '@mui/material';
import type { Bicycle } from 'lambda-api';
import type { StrictExtract } from 'ts-essentials';

const canUpdateOriginal = (
  event: Bicycle['events'][number],
  bicycle: Bicycle,
  model: StrictExtract<keyof Bicycle, 'location' | 'body' | 'owner' | 'storageLocation'>,
) => {
  const modelId = `${model}Id` as const;
  return event[modelId] === bicycle[model]?.versions?.at(-1)?.id;
};

type Props = {
  event: Bicycle['events'][number];
  bicycle: Bicycle;
};

export const LocationCaution = ({ event, bicycle }: Props) => {
  const canUpdate = canUpdateOriginal(event, bicycle, 'location');
  const message = canUpdate ? '最新の位置情報が更新されます' : '最新の位置情報は更新されません';
  const color = canUpdate ? 'warning' : 'textSecondary';

  return (
    <Typography variant="body2" color={color}>
      {message}
    </Typography>
  );
};
