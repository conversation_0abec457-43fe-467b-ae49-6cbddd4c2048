import { <PERSON><PERSON>, Box, Button, Paper, Stack } from '@mui/material';
import React from 'react';
import { z } from 'zod';

import { trpc } from '@/api';
import { RouterButton } from '@/components/RouterButton';
import {
  announcementStartCol,
  announcementStatusCol,
  useBaseBicycleColumns,
} from '@/components/bicycles/columns';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';
import { createUseColumns } from '@/hooks/useColumns';
import { announcementEdit } from '@/router/routes/keep/announcements';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { getQueryKey } from '@trpc/react-query';
import { startOfDay } from 'date-fns';
import { DataGrid, RhfDatePicker, RhfTextField } from 'mui-ex';
import { useForm } from 'react-hook-form';
import { CancelAnnouncementsDialog } from './CancelAnnouncementsDialog';

const StateSchema = z.object({
  date: z.date(),
  memo: z.string(),
});
type State = z.infer<typeof StateSchema>;

const useAdditionalColumns = createUseColumns([announcementStatusCol, announcementStartCol]);

export const AnnouncementEditPage = () => {
  const { labels } = useAppContext();
  const title = announcementEdit.meta.useTitle();
  const [ids, setIds] = React.useState<Set<string>>(new Set());
  const { data: bicycles = [] } = trpc.bicycles.list.useQuery({ type: 'stored' });
  const { control, handleSubmit } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      date: startOfDay(new Date()),
      memo: '',
    },
    resolver: zodResolver(StateSchema),
  });
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { mutateAsync, error, isPending } = trpc.bicycles.announcement.update.useMutation({
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: getQueryKey(trpc.bicycles.list, { type: 'stored' }),
      });
      await queryClient.invalidateQueries({
        queryKey: getQueryKey(trpc.bicycles.list),
      });
      await navigate({ to: '/announcements' });
    },
  });

  const submit = async ({ date, memo }: State) => {
    const bicycleIds = bicycles.map((b) => b.id);
    await mutateAsync({ start: date, memo, bicycleIds });
  };

  const baseColumns = useBaseBicycleColumns(bicycles);
  const additionalColumns = useAdditionalColumns(bicycles);
  const columns = [...baseColumns, ...additionalColumns];
  const disabled = ids.size === 0;

  const [cancelDialog, setCancelDialog] = React.useState(false);

  return (
    <MainLayout title={title}>
      <Paper sx={{ height: 1 }}>
        <Stack
          component="form"
          noValidate
          onSubmit={handleSubmit(submit)}
          spacing={2}
          sx={{ p: 2, height: 1 }}
        >
          <Box sx={{ width: 200 }}>
            <RhfDatePicker control={control} name="date" label={`${labels.da.announce}日`} />
          </Box>
          <Stack sx={{ flexGrow: 1, overflow: 'auto' }}>
            <DataGrid
              columns={columns}
              rows={bicycles}
              checkboxSelection
              rowSelectionModel={{ type: 'include', ids }}
              onRowSelectionModelChange={(model) => setIds(model.ids as Set<string>)}
              disableRowSelectionOnClick
              loading={isPending}
              sx={{
                // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
                '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
                '& .MuiTablePagination-select': { mr: 2 },
              }}
            />
          </Stack>
          <RhfTextField
            control={control}
            name="memo"
            label={labels.system.memo}
            multiline
            rows={4}
          />
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
          <Stack direction="row" spacing={1} justifyContent="flex-end">
            <RouterButton to="/announcements">キャンセル</RouterButton>
            <Button variant="outlined" disabled={disabled} onClick={() => setCancelDialog(true)}>
              {labels.da.announce}を取り消す
            </Button>
            <CancelAnnouncementsDialog
              open={cancelDialog}
              onClose={() => setCancelDialog(false)}
              onSuccess={() => setIds(new Set())}
              bicycles={bicycles.filter((b) => ids.has(b.id))}
            />
            <Button type="submit" variant="contained" disabled={disabled}>
              {labels.da.announce}日を設定する
            </Button>
          </Stack>
        </Stack>
      </Paper>
    </MainLayout>
  );
};
