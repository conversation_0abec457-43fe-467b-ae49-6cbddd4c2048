import React from 'react';

import { getRouteApi, useNavigate } from '@tanstack/react-router';

import type { LocalImage } from 'common';

import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { TakePhotos } from '@/components/take-photos/TakePhotos';
import { useAppContext } from '@/contexts/app-context';
import { useGetBicycleOrNull } from '@/models/bicycle';
import { useSnapShotPositionAndAddress } from '@/stores/position';
import { PatrolForm } from '../components/PatrolForm';
import { ScanQrcode } from '../components/ScanQrcode';

type State = {
  step: 'takePhotos' | 'form';
  localImages: LocalImage[];
};

const routeApi = getRouteApi('/app/bicycles/ensure-abandoned');

export const BicycleEnsureAbandonedPage = () => {
  const {
    tenant: {
      ensureAbandoned: { image: settings },
    },
    labels,
  } = useAppContext();
  const navigate = useNavigate({ from: '/bicycles/ensure-abandoned' });
  const { bicycleId } = routeApi.useSearch();
  const { data: bicycle } = useGetBicycleOrNull(bicycleId, '/bicycles/ensure-abandoned');
  const initialState: State = {
    step: settings.max !== 0 ? 'takePhotos' : 'form',
    localImages: [],
  };
  const [state, setState] = React.useState(initialState);
  const snapShot = useSnapShotPositionAndAddress();

  if (bicycleId === undefined)
    return (
      <ScanQrcode
        title={`${labels.domain.bicycle}の${labels.et.ensureAbandoned}`}
        validate={(id, bicycle, set, setError) => {
          if (bicycle === null)
            return setError(`
              まだ${labels.et.find}されていません。
              読み取るQRコードを確認してください`);

          if (bicycle.status !== 'find')
            return setError(`
              この車両は${labels.et.ensureAbandoned}可能なステータスではありません。`);
          // TODO: 放置認定可能な時間が経過しているかどうかの判定
          return set({ id, bicycle });
        }}
        onScan={({ id }) => navigate({ search: { bicycleId: id } })}
        from="/bicycles/ensure-abandoned"
      />
    );

  const handleImageComplete = (localImages: LocalImage[]) =>
    setState((s) => ({ ...s, step: 'form', localImages }));
  if (state.step === 'takePhotos')
    return <TakePhotos settings={settings} onComplete={handleImageComplete} />;
  if (bicycle === undefined) return <LoadingSpinner />;

  const handleReset = () => {
    setState(initialState);
    navigate({ search: { bicycleId: undefined } });
  };

  if (snapShot === undefined) return <LoadingSpinner message="現在位置を取得しています..." />;

  return (
    <PatrolForm
      bicycleId={bicycleId}
      eventType="ensureAbandoned"
      localImages={state.localImages}
      location={bicycle?.location?.last}
      body={bicycle?.body?.last}
      position={snapShot.position}
      address={snapShot.address}
      copied={bicycle?.copiedOwnerInfo ?? false}
      reset={handleReset}
    />
  );
};
