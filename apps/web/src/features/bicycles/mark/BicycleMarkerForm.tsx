import { zodResolver } from '@hookform/resolvers/zod';
import { FirstPage } from '@mui/icons-material';
import { <PERSON><PERSON>, Container, Stack, Typography } from '@mui/material';
import Divider from '@mui/material/Divider';
import { useNavigate } from '@tanstack/react-router';
import zip from 'just-zip-it';
import { useForm } from 'react-hook-form';

import {
  type Image,
  ImageSchema,
  type LocalImage,
  base64ToBlob,
  base64ToContentType,
  isLocalImage,
} from 'common';
import { RhfTextField } from 'mui-ex';
import { z } from 'zod';

import { trpc, trpcClient } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import { eventTypeToIcon } from '@/models/bicycle';
import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import type { Address } from 'models';
import {
  LocationStateSchema,
  RhfBicycleLocationForm,
  useLocationOrCurrentToState,
} from '../components/RhfBicycleLocationForm';
import { RhfBicycleImages } from '../components/rhf-bicycle-images/_RhfBicycleImages';

const StateSchema = z.object({
  images: z.array(ImageSchema),
  location: LocationStateSchema,
  memo: z.string(),
});
type State = z.infer<typeof StateSchema>;

type Props = {
  localImages: LocalImage[];
  position: google.maps.LatLngLiteral;
  address: Address;
  reset: () => void;
};

const uploadBicycleMarkerImageToS3 = async (images: Image[]) => {
  const localImages = images.filter(isLocalImage);
  const res = await trpcClient.presignedUrls.marker.list.query({
    markerId: crypto.randomUUID(),
    files: localImages.map(({ filename, base64 }) => ({
      contentType: base64ToContentType(base64),
      filename,
    })),
  });

  return await Promise.all(
    zip(localImages, res).map(
      async ([{ base64, description }, { key, url, fields }], sortOrder) => {
        const body = new FormData();
        for (const [key, value] of Object.entries(fields)) {
          body.append(key, value);
        }
        body.append('file', base64ToBlob(base64));
        const response = await fetch(url, { method: 'POST', body });
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Upload failed: ${errorText}`);
        }
        return { key, description, sortOrder };
      },
    ),
  );
};

export const BicycleMarkerForm = ({ localImages, position, address, reset }: Props) => {
  const { tenant, labels } = useAppContext();

  const { control, handleSubmit, watch, formState } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      location: useLocationOrCurrentToState(undefined, position, address),
      images: localImages,
      memo: '',
    },
    resolver: zodResolver(StateSchema),
  });

  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { error, mutate } = trpc.markers.create.useMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getQueryKey(trpc.markers.list) });
      navigate({ to: '/' });
    },
  });
  const submit = async (state: State) => {
    const images = await uploadBicycleMarkerImageToS3(state.images);
    mutate({ ...state, images });
  };
  return (
    <Container maxWidth="md">
      <Stack
        component="form"
        noValidate
        spacing={2}
        sx={{ py: 3, height: 'max-content', overflow: 'auto' }}
        onSubmit={handleSubmit(submit)}
      >
        <Typography variant="h5">マーカー登録</Typography>
        <Divider sx={{ my: 3 }} />
        <RhfBicycleImages control={control} watch={watch} settings={tenant.mark.image} />
        <RhfBicycleLocationForm control={control} watch={watch} />
        <Stack spacing={2} sx={{ mt: 2 }}>
          <RhfTextField
            control={control}
            name="memo"
            label={labels.system.memo}
            multiline
            rows={4}
          />
        </Stack>

        {error && (
          <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
            {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
          </Alert>
        )}
        <TwoActionButtons
          primary={{
            type: 'submit',
            icon: eventTypeToIcon('find'),
            label: labels.et.find,
            loading: formState.isSubmitting,
          }}
          secondary={{
            icon: FirstPage,
            label: '最初からやり直す',
            onClick: reset,
          }}
          fullWidth
          spacing={2}
        />
      </Stack>
    </Container>
  );
};
