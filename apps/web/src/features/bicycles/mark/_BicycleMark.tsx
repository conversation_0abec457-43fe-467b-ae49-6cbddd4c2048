import React from 'react';

import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { TakePhotos } from '@/components/take-photos/TakePhotos';
import { useAppContext } from '@/contexts/app-context';
import { useSnapShotPositionAndAddress } from '@/stores/position';

import type { LocalImage } from 'common';
import { BicycleMarkerForm } from './BicycleMarkerForm';

type State = {
  step: 'takePhotos' | 'form';
  localImages: LocalImage[];
};

export const BicycleMark = () => {
  const {
    tenant: {
      mark: { image: settings },
    },
  } = useAppContext();
  const [state, setState] = React.useState<State>({
    step: settings.max !== 0 ? 'takePhotos' : 'form',
    localImages: [],
  });
  const snapShot = useSnapShotPositionAndAddress();

  const handleImageComplete = (localImages: LocalImage[]) =>
    setState((s) => ({ ...s, step: 'form', localImages }));

  const handleReset = () => setState({ step: 'takePhotos', localImages: [] });

  if (state.step === 'takePhotos')
    return <TakePhotos settings={settings} onComplete={handleImageComplete} />;

  if (snapShot === undefined) return <LoadingSpinner message="現在位置を取得しています..." />;

  return (
    <BicycleMarkerForm
      localImages={state.localImages}
      position={snapShot.position}
      address={snapShot.address}
      reset={handleReset}
    />
  );
};
