import React from 'react';

import { Close, NavigateNext } from '@mui/icons-material';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import { z } from 'zod';

import type { Bicycle } from 'lambda-api';

import { trpc } from '@/api';
import { Multiline } from '@/components/Multiline';
import { QrcodeScanController } from '@/components/QrcodeScanController';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { BackToHomeButton } from '@/components/bicycles/steps/buttons/BackToHomeButton';
import { useAppContext } from '@/contexts/app-context';
import { type Page, createSerialNoFormatValidator } from 'models';

type State = { id: string; bicycle: Bicycle | null } | undefined;

type Props = {
  title: string;
  validate: (
    id: string,
    bicycle: Bicycle | null,
    set: React.Dispatch<React.SetStateAction<State>>,
    setError: React.Dispatch<React.SetStateAction<string | undefined>>,
  ) => void;
  onScan: (state: NonNullable<State>) => void;
  from: Page;
};

export const ScanQrcode = ({ title, validate, onScan, from }: Props) => {
  const { tenant } = useAppContext();
  const [state, set] = React.useState<State>();
  const [error, setError] = React.useState<string | undefined>();
  const utils = trpc.useUtils();
  const format = tenant.numbering.serialNoFormat;
  const handleScan = React.useCallback(
    async (text: string) => {
      if (z.string().uuid().safeParse(text).success) {
        const bicycle = await utils.bicycles.get.fetch({ id: text, from });
        return validate(text, bicycle, set, setError);
      }
      const serialNoFormatValidator = createSerialNoFormatValidator(format);
      if (serialNoFormatValidator.test(text)) {
        const bicycle = await utils.bicycles.getBySerialNo.fetch({ serialNo: text });
        if (bicycle === null) return setError('この整理番号に該当する自転車が見つかりません');
        return validate(bicycle.id, bicycle, set, setError);
      }

      return setError('不正なQRコードです');
    },
    [utils, validate, from, format],
  );

  const handleNext = React.useCallback(() => {
    if (state === undefined) throw new Error();
    onScan(state);
  }, [state, onScan]);

  return (
    <>
      <QrcodeScanController
        title={title}
        description="カメラをQRコードに向けてください"
        onScan={handleScan}
        BackButton={<BackToHomeButton variant="text" fullWidth={false} />}
      />
      <Dialog open={state !== undefined}>
        <DialogTitle>QRコードの読み取り成功</DialogTitle>
        <DialogContent>
          <DialogContentText>QRコードの読み取りに成功しました</DialogContentText>
        </DialogContent>
        <TwoActionButtons
          primary={{
            label: '次へ',
            icon: NavigateNext,
            onClick: handleNext,
          }}
          secondary={{
            label: 'キャンセル',
            icon: Close,
            onClick: () => set(undefined),
            end: true,
          }}
          fullWidth
          sx={{ p: 1, gap: 1 }}
        />
      </Dialog>
      <Dialog open={Boolean(error)}>
        <DialogTitle>QRコードの読み取り失敗</DialogTitle>
        <DialogContent>
          <DialogContentText>{error && <Multiline text={error} />}</DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button fullWidth onClick={() => setError(undefined)}>
            OK
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
