import { Stack } from '@mui/material';
import { type Control, useController } from 'react-hook-form';
import { z } from 'zod';

import type { BicycleSerialTag, BicycleStorageLocation } from 'lambda-api';
import { RhfSingleSelect, RhfTextField } from 'mui-ex';

import { useAppContext } from '@/contexts/app-context';
import type { BicycleType } from 'common';
import React from 'react';

export const useStorageLocationStateSchema = () => {
  const { labels } = useAppContext();
  return z.object({
    storageId: z.string().min(1, `${labels.domain.storage}を選択して下さい。`),
    memo: z.string().nullable(),
  });
};
export type StorageLocationState = z.infer<ReturnType<typeof useStorageLocationStateSchema>>;

export const defaultStorageLocationState: StorageLocationState = {
  storageId: '',
  memo: '',
};

export const storageLocationToState = (
  storageLocation: BicycleStorageLocation | null | undefined,
  serialTag?: BicycleSerialTag | null | undefined,
): StorageLocationState => {
  return {
    storageId: storageLocation?.storageId ?? serialTag?.last?.storageId ?? '',
    memo: storageLocation?.memo ?? '',
  };
};

type CommonProps = {
  readOnly?: boolean;
  requiredMemo?: boolean;
  landmarkId?: string;
  bicycleType?: BicycleType;
};
type State = { storageLocation: StorageLocationState };
type AnyState = { storageLocation?: any };
type Props<T extends State | AnyState> = CommonProps & {
  control: Control<T>;
};
type InnerProps = CommonProps & {
  control: Control<State>;
};

export const RhfBicycleStorageLocationForm = <T extends State | AnyState>(props: Props<T>) => {
  const {
    control,
    readOnly,
    requiredMemo = true,
    landmarkId = null,
    bicycleType,
  } = props as unknown as InnerProps;
  const {
    labels,
    storages,
    tenant: { removeRules },
  } = useAppContext();
  const storageIdCtrl = useController({ name: 'storageLocation.storageId', control });

  const rules = React.useMemo(
    () =>
      removeRules.filter((r) => {
        if (!bicycleType) return false;
        if (r.landmarkId === null) return r.bicycleTypes.includes(bicycleType);
        return r.bicycleTypes.includes(bicycleType) && r.landmarkId === landmarkId;
      }),
    [removeRules, bicycleType, landmarkId],
  );

  React.useEffect(() => {
    const matchedStorages = storages.filter((s) => rules.some((r) => r.storageId === s.id));
    if (matchedStorages.length === 1) storageIdCtrl.field.onChange(rules[0].storageId);
  }, [storages, rules, storageIdCtrl.field.onChange]);

  return (
    <Stack spacing={2}>
      <RhfSingleSelect
        control={control}
        name="storageLocation.storageId"
        label={labels.domain.storageLocation}
        options={storages
          .toSorted((a, b) => {
            const aRuleOrder = Math.min(
              ...rules.filter((r) => r.storageId === a.id).map((r) => r.sortOrder),
              Number.MAX_VALUE,
            );
            const bRuleOrder = Math.min(
              ...rules.filter((r) => r.storageId === b.id).map((r) => r.sortOrder),
              Number.MAX_VALUE,
            );
            if (aRuleOrder !== bRuleOrder) return aRuleOrder - bRuleOrder;
            return a.sortOrder - b.sortOrder;
          })
          .map((storage) => {
            const secondary = rules.some((r) => r.storageId === storage.id);
            return {
              label: storage.name,
              value: storage.id,
              secondary,
            };
          })}
        readOnly={readOnly}
      />
      {requiredMemo && (
        <RhfTextField
          control={control}
          name="storageLocation.memo"
          label="場所メモ"
          sx={{ pt: 0.5 }}
          readOnly={readOnly}
        />
      )}
    </Stack>
  );
};
