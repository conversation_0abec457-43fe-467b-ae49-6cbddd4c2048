import { Box, FormHelperText, Grid, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack, Too<PERSON><PERSON>, Typo<PERSON> } from '@mui/material';
import { type Control, type UseFormWatch, useController } from 'react-hook-form';
import { z } from 'zod';

import { BicycleTypeSchema, type BodyField, stringOrNull } from 'common';
import type { BicycleBody, PoliceStation, Tenant } from 'lambda-api';
import {
  RhfCheckbox,
  RhfMultiAutocomplete,
  RhfSingleAutocomplete,
  RhfSingleSelect,
  RhfTextField,
} from 'mui-ex';

import { QrcodeScanDialog } from '@/components/QrcodeScanDialog';
import { RhfMultiColorAutocomplete } from '@/components/bicycles/react-hook-form/RhfMultiColorAutocomplete';
import { type ContextTenant, useAppContext } from '@/contexts/app-context';
import { parseRegistrationNumber } from '@/models/bicycle';
import { QrCodeScanner } from '@mui/icons-material';
import type { PatrolEventType } from 'models';
import React from 'react';

const checkRegistrationNumberProps = (args: BodyState, ctx: z.RefinementCtx) => {
  const {
    registrationNumberPrefectureCode,
    registrationNumberPoliceCode,
    registrationNumber1,
    registrationNumber2,
  } = args;
  if (
    [
      registrationNumberPrefectureCode,
      registrationNumberPoliceCode,
      registrationNumber1,
      registrationNumber2,
    ].every((p) => p === '')
  )
    return;

  if (
    [registrationNumberPrefectureCode, registrationNumberPoliceCode, registrationNumber1].every(
      (p) => p !== '',
    )
  )
    return;

  ctx.addIssue({
    code: z.ZodIssueCode.custom,
    message: '防犯登録番号を正しく入力してください',
    path: ['registrationNumber'],
  });
  if (registrationNumberPrefectureCode === '')
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: '',
      path: ['registrationNumberPrefectureCode'],
    });
  if (registrationNumberPoliceCode === '')
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: '',
      path: ['registrationNumberPoliceCode'],
    });
  if (registrationNumber1 === '')
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: '',
      path: ['registrationNumber1'],
    });
};

export const BodyStateSchema = z
  .object({
    type: z.union([BicycleTypeSchema, z.literal('')]),
    colors: z.array(z.string()),
    hasBasket: z.boolean(),
    isLocked: z.boolean(),
    conditions: z.array(z.string()),
    free1: z.string(),
    free2: z.string(),
    // 自転車
    registrationNumber: z.string(),
    registrationNumberPrefectureCode: z.string(),
    registrationNumberPoliceCode: z.string(),
    registrationNumber1: z.string(),
    registrationNumber2: z.string(),
    serialNumber: z.string(),
    styleId: z.string(),
    // 自転車以外
    numberPlate: z.string(),
  })
  .superRefine((args, ctx) => {
    if (args.type === 'bicycle') {
      checkRegistrationNumberProps(args, ctx);
    }
  });

export type BodyState = z.infer<typeof BodyStateSchema>;

export const defaultBodyState: BodyState = {
  type: 'bicycle',
  colors: [],
  hasBasket: false,
  isLocked: false,
  conditions: [],
  free1: '',
  free2: '',
  // 自転車
  registrationNumber: '',
  registrationNumberPrefectureCode: '',
  registrationNumberPoliceCode: '',
  registrationNumber1: '',
  registrationNumber2: '',
  serialNumber: '',
  styleId: '',
  // 自転車以外
  numberPlate: '',
};

/**
 * ## 警察署コードCSVにおける北海道の特殊な事情について
 * 警察署コードCSVでは、北海道には4つの都道府県コードが振られています。
 * しかし防犯登録番号からは4つのうちどこかについて目視では読み取れません。
 *
 * そこで北海道を選んだ場合は、4つのエリアすべての警察署を候補として選択させ、
 * 選択後に該当する都道府県コードを決める必要があります。
 * 4つの都道府県コードにおいて「交通課(050)」のみが重複していますが、
 * 交通課は防犯登録番号のコードには使用されていないことを前提として、
 * 警察署名のコードから外しています。
 */
const HOKKAIDO = 'HOKKAIDO';

export const bodyToState = (
  body: BicycleBody | null | undefined,
  polices: PoliceStation[],
  tenant?: ContextTenant,
): BodyState => {
  const prefectureCode =
    body?.registrationNumberPrefectureCode ?? tenant?.defaultPoliceStationPrefectureCode;
  const policeCode = body?.registrationNumberPoliceCode ?? tenant?.defaultPoliceStation?.code;
  const police = polices.find((p) => p.prefectureCode === prefectureCode);
  const isHokkaido = police?.prefecture?.startsWith('北海道') ?? false;
  return {
    type: body?.type ?? '',
    colors: body?.colors?.map((c) => c.colorId) ?? [],
    hasBasket: body?.hasBasket ?? false,
    isLocked: body?.isLocked ?? false,
    conditions: body?.conditions?.map((c) => c.conditionId) ?? [],
    free1: body?.free1 ?? '',
    free2: body?.free2 ?? '',
    // 自転車
    registrationNumber: body?.registrationNumber ?? '',
    registrationNumberPrefectureCode: isHokkaido ? HOKKAIDO : (prefectureCode ?? ''),
    registrationNumberPoliceCode: policeCode ?? '',
    registrationNumber1: body?.registrationNumber1 ?? '',
    registrationNumber2: body?.registrationNumber2 ?? '',
    serialNumber: body?.serialNumber ?? '',
    styleId: body?.styleId ?? '',
    // 自転車以外
    numberPlate: body?.numberPlate ?? '',
  };
};

const calcPrefectureCode = (
  {
    registrationNumberPrefectureCode: prefectureCode,
    registrationNumberPoliceCode: policeCode,
  }: BodyState,
  polices: PoliceStation[],
) => {
  if (prefectureCode === '') return null;
  if (policeCode === '') return null;
  if (prefectureCode !== HOKKAIDO) return prefectureCode;
  const police = polices.find((p) => p.prefecture.startsWith('北海道') && p.code === policeCode);
  if (police === undefined) throw new Error();
  return police.prefectureCode;
};

const joinRegistrationNumber = (body: BodyState, polices: PoliceStation[]): string | null => {
  if (body.registrationNumberPrefectureCode === '') return null;
  if (body.registrationNumberPoliceCode === '') return null;
  if (body.registrationNumber1 === '') return null;
  return [
    calcPrefectureCode(body, polices),
    body.registrationNumberPoliceCode,
    body.registrationNumber1,
    body.registrationNumber2,
  ]
    .filter((str) => str !== '')
    .join('-');
};

export const bodyStateToInput = (body: BodyState, polices: PoliceStation[]) => {
  const registrationNumber = joinRegistrationNumber(body, polices);
  const registrationNumberPrefectureCode = calcPrefectureCode(body, polices);

  const commonFields = {
    type: body.type !== '' ? body.type : null,
    colors: body.colors,
    hasBasket: body.hasBasket,
    isLocked: body.isLocked,
    conditions: body.conditions,
    free1: stringOrNull(body.free1),
    free2: stringOrNull(body.free2),
  };

  if (body.type === 'bicycle') {
    if (registrationNumber === null || registrationNumberPrefectureCode === null)
      return {
        ...commonFields,
        // 自転車
        registrationNumber: null,
        registrationNumberPrefectureCode: null,
        registrationNumberPoliceCode: null,
        registrationNumber1: null,
        registrationNumber2: null,
        serialNumber: stringOrNull(body.serialNumber),
        styleId: stringOrNull(body.styleId),
        // 自転車以外
        numberPlate: null,
      };

    return {
      ...commonFields,
      // 自転車
      registrationNumber,
      registrationNumberPrefectureCode,
      registrationNumberPoliceCode: body.registrationNumberPoliceCode ?? '',
      registrationNumber1: body.registrationNumber1 ?? '',
      registrationNumber2: body.registrationNumber2 ?? '',
      serialNumber: stringOrNull(body.serialNumber),
      styleId: stringOrNull(body.styleId),
      // 自転車以外
      numberPlate: null,
    };
  }
  return {
    ...commonFields,
    // 自転車
    registrationNumber: null,
    registrationNumberPrefectureCode: null,
    registrationNumberPoliceCode: null,
    registrationNumber1: null,
    registrationNumber2: null,
    serialNumber: null,
    styleId: null,
    // 自転車以外
    numberPlate: stringOrNull(body.numberPlate),
  };
};

type FieldVisibilities = Record<'type' | BodyField, boolean>;

type AdditionalSettings = Tenant['find'];
export const useRequireBody = (eventType: PatrolEventType) => {
  const settings = useAppContext().tenant[eventType];
  return settings.requiredBicycleType || settings.additionalBodyFields.length !== 0;
};

const afterPatrolVisibilities = (storeSettings: Tenant['store']): FieldVisibilities => ({
  type: true,
  colors: true,
  hasBasket: true,
  isLocked: true,
  conditions: true,
  style: true,
  registrationNumber: true,
  serialNumber: true,
  numberPlate: true,
  free1: storeSettings.free1,
  free2: storeSettings.free2,
});

const settingsToVisibilities = (settings: AdditionalSettings): FieldVisibilities => ({
  type: settings.requiredBicycleType,
  colors: settings.additionalBodyFields.includes('colors'),
  hasBasket: settings.additionalBodyFields.includes('hasBasket'),
  isLocked: settings.additionalBodyFields.includes('isLocked'),
  conditions: settings.additionalBodyFields.includes('conditions'),
  style: settings.additionalBodyFields.includes('style'),
  registrationNumber: settings.additionalBodyFields.includes('registrationNumber'),
  serialNumber: settings.additionalBodyFields.includes('serialNumber'),
  numberPlate: settings.additionalBodyFields.includes('numberPlate'),
  free1: settings.additionalBodyFields.includes('free1'),
  free2: settings.additionalBodyFields.includes('free2'),
});

type CommonProps = { readOnly?: boolean; eventType?: PatrolEventType };
type State = { body: BodyState };
type AnyState = { body?: any };
type Props<T extends State | AnyState> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & { control: Control<State>; watch: UseFormWatch<State> };

export const RhfBicycleBodyForm = <T extends State | AnyState>(props: Props<T>) => {
  const { control, watch, readOnly, eventType } = props as unknown as InnerProps;
  const { tenant, conditions, bicycleTypes, styles, labels, policeStations } = useAppContext();

  const settings: FieldVisibilities = eventType
    ? settingsToVisibilities(tenant[eventType])
    : afterPatrolVisibilities(tenant.store);

  const [scanDialog, setScanDialog] = React.useState(false);
  const prefectureCodeCtrl = useController({
    control,
    name: 'body.registrationNumberPrefectureCode',
  });
  const policeCodeCtrl = useController({ control, name: 'body.registrationNumberPoliceCode' });
  const registrationNumber1Ctrl = useController({ control, name: 'body.registrationNumber1' });
  const registrationNumber2Ctrl = useController({ control, name: 'body.registrationNumber2' });

  const handleScan = (text: string) => {
    const parsed = parseRegistrationNumber(text, policeStations);
    prefectureCodeCtrl.field.onChange(parsed.registrationNumberPrefectureCode.toString());
    policeCodeCtrl.field.onChange(parsed.registrationNumberPoliceCode.toString());
    registrationNumber1Ctrl.field.onChange(parsed.registrationNumber1);
    registrationNumber2Ctrl.field.onChange(parsed.registrationNumber2);
    setScanDialog(false);
  };

  const type = watch('body.type');
  const prefectureCode = watch('body.registrationNumberPrefectureCode');

  const policeGroups = Map.groupBy(policeStations, (p) =>
    p.prefecture.startsWith('北海道') ? HOKKAIDO : p.prefectureCode,
  );
  const prefectureCodeOptions = policeGroups
    .keys()
    .toArray()
    .map((prefecture) => {
      const group = policeGroups.get(prefecture);
      if (!group) throw new Error();
      const police = group[0];
      if (police.prefecture.startsWith('北海道')) {
        return {
          label: '北海道',
          value: HOKKAIDO,
        };
      }
      return {
        label: police.prefecture,
        value: police.prefectureCode.toString(),
      };
    });
  const policeCodeOptions = (policeGroups.get(prefectureCode) ?? [])
    .filter((p) => p.name !== '交通課')
    .map((p) => ({
      label: p.name,
      value: p.code,
    }));

  return (
    <Grid container spacing={2}>
      {settings.colors && (
        <Grid size={{ xs: 12, md: 6 }}>
          <RhfMultiColorAutocomplete
            control={control}
            name="body.colors"
            label={labels.body.color}
            readOnly={readOnly}
          />
        </Grid>
      )}
      {(settings.hasBasket || settings.isLocked) && (
        <Grid size={{ xs: 12, md: 6 }} sx={{ display: 'flex' }}>
          {settings.hasBasket && (
            <RhfCheckbox
              control={control}
              name="body.hasBasket"
              label={labels.p.hasBasket}
              labelTrue={labels.p.hasBasketTrue}
              readOnly={readOnly}
            />
          )}
          {settings.isLocked && (
            <RhfCheckbox
              control={control}
              name="body.isLocked"
              label={labels.p.isLocked}
              labelTrue={labels.p.isLockedTrue}
              readOnly={readOnly}
            />
          )}
        </Grid>
      )}
      {settings.conditions && (
        <Grid size={{ xs: 12, md: 6 }}>
          <RhfMultiAutocomplete
            control={control}
            name="body.conditions"
            label={labels.body.conditions}
            readOnly={readOnly}
            options={conditions.map((c) => ({ label: c.name, value: c.id }))}
          />
        </Grid>
      )}
      {settings.free1 && (
        <Grid size={{ xs: 12, md: 6 }}>
          <RhfTextField
            control={control}
            name="body.free1"
            label={labels.body.free1}
            readOnly={readOnly}
          />
        </Grid>
      )}
      {settings.free2 && (
        <Grid size={{ xs: 12, md: 6 }}>
          <RhfTextField
            control={control}
            name="body.free2"
            label={labels.body.free2}
            readOnly={readOnly}
          />
        </Grid>
      )}
      <Grid size={{ xs: 12 }} />

      {settings.type && (
        <>
          <Grid size={{ xs: 12, md: 6 }}>
            <RhfSingleSelect
              control={control}
              name="body.type"
              label={labels.body.type}
              options={bicycleTypes.map((c) => ({ label: c.name, value: c.type }))}
              readOnly={readOnly}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            {settings.style && type === 'bicycle' && (
              <RhfSingleSelect
                control={control}
                name="body.styleId"
                label="車種"
                options={styles.map((s) => ({ label: s.name, value: s.id }))}
                readOnly={readOnly}
              />
            )}
          </Grid>
        </>
      )}
      {settings.registrationNumber && type === 'bicycle' && (
        <Grid size={{ xs: 12 }}>
          <Typography variant="caption" sx={{ pb: 1 }}>
            {labels.body.registrationNumber}
            <FormHelperText error>
              {policeCodeCtrl.formState.errors.body?.registrationNumber?.message}
            </FormHelperText>
          </Typography>
          <Grid container spacing={1}>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <Stack direction="row" spacing={1} alignItems="center">
                {!readOnly && (
                  <Box>
                    <Tooltip title={`${labels.body.registrationNumber}のQRコードを読み取る`}>
                      <IconButton
                        sx={{
                          color: '#fff',
                          bgcolor: (t) => t.palette.primary.main,
                          '&:hover': { bgcolor: (t) => t.palette.primary.dark },
                        }}
                        onClick={() => setScanDialog(true)}
                      >
                        <QrCodeScanner />
                      </IconButton>
                    </Tooltip>
                  </Box>
                )}
                <QrcodeScanDialog
                  open={scanDialog}
                  onClose={() => setScanDialog(false)}
                  title={labels.body.registrationNumber}
                  description={`${labels.body.registrationNumber}のQRコードをカメラにかざしてください`}
                  onScan={handleScan}
                />
                <RhfSingleAutocomplete
                  control={control}
                  name="body.registrationNumberPrefectureCode"
                  label="都道府県"
                  options={prefectureCodeOptions}
                  readOnly={readOnly}
                  fullWidth
                />
              </Stack>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <RhfSingleAutocomplete
                control={control}
                name="body.registrationNumberPoliceCode"
                label="警察署名"
                options={policeCodeOptions}
                readOnly={readOnly}
                fullWidth
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <RhfTextField
                control={control}
                name="body.registrationNumber1"
                readOnly={readOnly}
                fullWidth
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 1 }}>
              <RhfTextField
                control={control}
                name="body.registrationNumber2"
                readOnly={readOnly}
                fullWidth
              />
            </Grid>
            <FormHelperText error>
              {policeCodeCtrl.formState.errors.body?.registrationNumber?.message}
            </FormHelperText>
          </Grid>
        </Grid>
      )}
      {settings.serialNumber && type === 'bicycle' && (
        <Grid size={{ xs: 12, md: 6 }}>
          <RhfTextField
            control={control}
            name="body.serialNumber"
            label={labels.body.serialNumber}
            readOnly={readOnly}
          />
        </Grid>
      )}

      {settings.numberPlate && type !== 'bicycle' && type !== '' && (
        <Grid size={{ xs: 12, md: 6 }}>
          <RhfTextField
            control={control}
            name="body.numberPlate"
            label={labels.body.numberPlate}
            readOnly={readOnly}
          />
        </Grid>
      )}
    </Grid>
  );
};
