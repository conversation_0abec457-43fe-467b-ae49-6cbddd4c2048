import { Grid, InputAdornment, Stack, type Theme, useMediaQuery } from '@mui/material';
import type { Control, UseFormWatch } from 'react-hook-form';
import { z } from 'zod';

import { RhfSimpleAddress } from '@/components/react-hook-form/address/RhfSimpleAddress';
import { RhfSyncAddressToPostalCodeButton } from '@/components/react-hook-form/address/RhfSyncAddressToPostalCodeButton';
import { RhfSyncPostalCodeToAddressButton } from '@/components/react-hook-form/address/RhfSyncPostalCodeToAddressButton';
import { ArrowBack, ArrowDownward, ArrowForward, ArrowUpward } from '@mui/icons-material';
import { isNullish } from 'common';
import type { BicycleOwner } from 'lambda-api';
import { RhfTextField } from 'mui-ex';
import type React from 'react';

export const OwnerStateSchema = z.object({
  name: z.string(),
  tel: z.string(),
  postalCode: z.string(),
  address: z.string(),
});

export type OwnerState = z.infer<typeof OwnerStateSchema>;

export const defaultOwnerState: OwnerState = {
  name: '',
  tel: '',
  postalCode: '',
  address: '',
};

export const ownerToState = (owner: BicycleOwner | null | undefined): OwnerState => {
  if (isNullish(owner)) return defaultOwnerState;
  return {
    name: owner.name ?? '',
    tel: owner.tel ?? '',
    postalCode: owner.postalCode ?? '',
    address: owner.address ?? '',
  };
};

type CommonProps = React.PropsWithChildren & { readOnly?: boolean };
type State = { owner: OwnerState };
type AnyState = { owner?: any };
type Props<T extends State | AnyState> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<State>;
  watch: UseFormWatch<State>;
};

export const RhfBicycleOwnerForm = <T extends State | AnyState>(props: Props<T>) => {
  const { control, watch, readOnly, children } = props as unknown as InnerProps;
  const smDown = useMediaQuery<Theme>((theme) => theme.breakpoints.down('sm'));

  const AddressToPostalCode = (
    <RhfSyncAddressToPostalCodeButton
      control={control}
      watch={watch}
      name={{
        postalCode: 'owner.postalCode',
        address: 'owner.address',
      }}
      size="small"
    >
      {smDown ? <ArrowUpward fontSize="inherit" /> : <ArrowBack fontSize="inherit" />}
    </RhfSyncAddressToPostalCodeButton>
  );
  return (
    <Grid container spacing={2}>
      <Grid size={{ xs: 12, sm: 6 }}>
        <RhfTextField control={control} name="owner.name" label="氏名" readOnly={readOnly} />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <RhfTextField control={control} name="owner.tel" label="連絡先" readOnly={readOnly} />
      </Grid>
      <Grid size={{ xs: 12, sm: 3 }}>
        <RhfTextField
          control={control}
          name="owner.postalCode"
          label="郵便番号"
          readOnly={readOnly}
          helperText="ハイフンなし"
          slotProps={{
            input: !readOnly
              ? {
                  endAdornment: (
                    <InputAdornment position="end">
                      <RhfSyncPostalCodeToAddressButton
                        control={control}
                        watch={watch}
                        name={{
                          postalCode: 'owner.postalCode',
                          address: 'owner.address',
                        }}
                        size="small"
                      >
                        {smDown ? (
                          <ArrowDownward fontSize="inherit" />
                        ) : (
                          <ArrowForward fontSize="inherit" />
                        )}
                      </RhfSyncPostalCodeToAddressButton>
                    </InputAdornment>
                  ),
                }
              : undefined,
          }}
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 9 }}>
        <Stack direction="row" spacing={2} alignItems="end">
          <RhfSimpleAddress
            control={control}
            watch={watch}
            name="owner.address"
            label="住所"
            readOnly={readOnly}
            startAdornment={!smDown ? AddressToPostalCode : undefined}
            endAdornment={smDown ? AddressToPostalCode : undefined}
          />
        </Stack>
      </Grid>
      {children}
    </Grid>
  );
};
