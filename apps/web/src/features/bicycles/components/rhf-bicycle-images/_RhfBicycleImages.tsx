import { Box, Card, CardMedia, Grid, Stack } from '@mui/material';

import { ImageUtil } from 'common';

import { BASE_URL } from '@/types/vite-env';
import type { ImageSettings } from 'models';
import { RhfTextField } from 'mui-ex';
import type React from 'react';
import type { Control, UseFormWatch } from 'react-hook-form';
import { AddButton } from './AddButton';
import { EntryButton } from './EntryButton';
import { MenuButton } from './MenuButton';
import type { ImagesState } from './types';

type CommonProps = {
  settings?: ImageSettings;
  size?: React.ComponentProps<typeof Grid>['size'];
  readOnly?: boolean;
};
type Props<T extends ImagesState> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<ImagesState>;
  watch: UseFormWatch<ImagesState>;
};

export const RhfBicycleImages = <T extends ImagesState>(props: Props<T>) => {
  const {
    control,
    watch,
    settings = { min: 0, max: 20, guides: [] },
    size = { xs: 6, md: 4 },
    readOnly,
  } = props as unknown as InnerProps;
  const { min, max } = settings;

  const images = watch('images');
  const requireStart = images.length === 0 && min !== 0;
  const appendable = images.length < max;
  return (
    <Grid container spacing={2}>
      {images.map((image, i) => {
        const img = new ImageUtil(image);
        return (
          <Grid key={img.key} size={size}>
            <Card sx={{ width: 1 }}>
              <Box sx={{ position: 'relative', width: 1, height: 0 }}>
                <Stack
                  direction="row"
                  alignItems="center"
                  sx={{
                    position: 'absolute',
                    width: 1,
                    height: 'max-content',
                    bgcolor: (t) => t.palette.background.translucentBlack,
                    p: 0.5,
                  }}
                >
                  <RhfTextField
                    control={control}
                    name={`images.${i}.description`}
                    color="primary"
                    focused
                    slotProps={{
                      htmlInput: {
                        sx: {
                          color: 'white',
                          fontSize: (t) => t.typography.fontSize,
                          pl: 0.5,
                        },
                      },
                    }}
                    readOnly={readOnly}
                  />
                  <Box sx={{ flexGrow: 1 }} />
                  {!readOnly && (
                    <MenuButton
                      control={control}
                      index={i}
                      description={img.description}
                      removable={images.length > min}
                      image={image}
                    />
                  )}
                </Stack>
              </Box>
              <CardMedia
                image={img.src(BASE_URL)}
                title={img.description}
                sx={{ width: 1, aspectRatio: '1/1' }}
              />
            </Card>
          </Grid>
        );
      })}
      {!readOnly && requireStart && (
        <EntryButton control={control} settings={settings} size={size} />
      )}
      {!readOnly && appendable && !requireStart && (
        <AddButton control={control} watch={watch} size={size} />
      )}
    </Grid>
  );
};
