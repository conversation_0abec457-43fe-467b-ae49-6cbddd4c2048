import { AddAPhoto } from '@mui/icons-material';
import {
  Card,
  CardActionArea,
  CardContent,
  Dialog,
  Grid,
  Slide,
  Stack,
  Typography,
} from '@mui/material';
import React from 'react';
import { type Control, useFieldArray } from 'react-hook-form';

import type { Image, LocalImage } from 'common';
import type { ImageSettings } from 'models';

import { TakePhotos } from '@/components/take-photos/TakePhotos';

type ImagesState = { images: Image[] };

type CommonProps = {
  settings: ImageSettings;
  size: React.ComponentProps<typeof Grid>['size'];
  readOnly?: boolean;
};
type Props<T extends ImagesState> = CommonProps & {
  control: Control<T>;
};
type InnerProps = CommonProps & {
  control: Control<ImagesState>;
};

export const EntryButton = <T extends ImagesState>(props: Props<T>) => {
  const { control, settings, size } = props as unknown as InnerProps;

  const { replace } = useFieldArray({ control, name: 'images' });

  const [starting, setStarting] = React.useState(false);
  const handleCompleteImages = (images: LocalImage[]) => {
    replace(images);
    setStarting(false);
  };
  return (
    <Grid
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      gap={1}
      size={size}
      sx={{ aspectRatio: '1/1' }}
    >
      <Card sx={{ width: 1, height: 1 }}>
        <CardActionArea sx={{ width: 1, height: 1 }} onClick={() => setStarting(true)}>
          <CardContent
            component={Stack}
            justifyContent="center"
            alignItems="center"
            spacing={1}
            sx={{ width: 1, height: 1 }}
          >
            <AddAPhoto color="primary" sx={{ fontSize: 80 }} />
            <Typography color="primary">撮影を開始する</Typography>
          </CardContent>
        </CardActionArea>
      </Card>
      <Dialog
        open={starting}
        onClose={() => setStarting(false)}
        fullScreen
        slots={{ transition: Slide }}
        slotProps={{ transition: { direction: 'up' } }}
      >
        <TakePhotos
          settings={settings}
          onComplete={handleCompleteImages}
          onCancel={() => setStarting(false)}
        />
      </Dialog>
    </Grid>
  );
};
