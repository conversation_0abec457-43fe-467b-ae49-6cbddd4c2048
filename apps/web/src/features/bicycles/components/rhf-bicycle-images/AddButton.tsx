import { Add, CameraAlt, Image } from '@mui/icons-material';
import {
  Card,
  CardActionArea,
  CardContent,
  Grid,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  Stack,
  Typography,
} from '@mui/material';

import { type LocalImage, fileToBase64Url, isString } from 'common';

import { TakePhotoDialog } from '@/components/TakePhotoDialog';

import { takePhotoDescription, takePhotoFilename } from 'models';

import { VisuallyHiddenInput } from '@/components/VisuallyHiddenInput';
import { resizeToSquare } from 'mui-ex';
import React from 'react';
import { type Control, type UseFormWatch, useFieldArray } from 'react-hook-form';
import type { ImagesState } from './types';

type CommonProps = {
  size: React.ComponentProps<typeof Grid>['size'];
};
type Props<T extends ImagesState> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<ImagesState>;
  watch: UseFormWatch<ImagesState>;
};

export const AddButton = <T extends ImagesState>(props: Props<T>) => {
  const { control, watch, size } = props as unknown as InnerProps;

  const { append } = useFieldArray({ control, name: 'images' });
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [takePhoto, setTakePhoto] = React.useState(false);
  const ref = React.useRef<HTMLInputElement | null>(null);

  const images = watch('images');

  const handleClickButton = (event: React.MouseEvent<HTMLButtonElement>) => {
    return setAnchorEl(event.currentTarget);
  };

  const handleTakePhoto = (image: LocalImage) => {
    append(image);
    setAnchorEl(null);
  };

  const handleHiddenInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = e.target;
    if (files === null || files.length === 0) return;
    const file = files[0];
    const { name: filename } = file;
    const raw = await fileToBase64Url(file);
    const base64 = await resizeToSquare(raw);
    if (!isString(base64)) throw new Error();
    const image: LocalImage = { id: base64, filename, base64, description: filename };
    append(image);
    setAnchorEl(null);
  };

  const label = '写真を追加する';
  const Icon = Add;
  const open = Boolean(anchorEl);

  return (
    <Grid
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      gap={1}
      size={size}
      sx={{ aspectRatio: '1/1' }}
    >
      <Card sx={{ width: 1, height: 1 }}>
        <CardActionArea sx={{ width: 1, height: 1 }} onClick={handleClickButton}>
          <CardContent
            component={Stack}
            justifyContent="center"
            alignItems="center"
            spacing={1}
            sx={{ width: 1, height: 1 }}
          >
            <Icon color="primary" sx={{ fontSize: 60 }} />
            <Typography color="primary">{label}</Typography>
          </CardContent>
        </CardActionArea>
      </Card>
      <Menu
        open={open}
        anchorEl={anchorEl}
        onClose={() => setAnchorEl(null)}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
      >
        <MenuItem onClick={() => setTakePhoto(true)}>
          <ListItemIcon>
            <CameraAlt fontSize="small" />
          </ListItemIcon>
          <ListItemText>{'写真を撮る'}</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => ref.current?.click()}>
          <ListItemIcon>
            <Image fontSize="small" />
          </ListItemIcon>
          <ListItemText>{'写真を選択する'}</ListItemText>
        </MenuItem>
      </Menu>
      <VisuallyHiddenInput
        ref={ref}
        type="file"
        accept="image/*"
        onChange={handleHiddenInputChange}
      />
      <TakePhotoDialog
        open={takePhoto}
        onClose={() => setTakePhoto(false)}
        filename={takePhotoFilename(images.length + 1)}
        description={takePhotoDescription(images.length + 1)}
        onTakePhoto={handleTakePhoto}
      />
    </Grid>
  );
};
