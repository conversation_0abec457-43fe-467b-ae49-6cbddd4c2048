import { AdjustPhotoDialog } from '@/components/AdjustPhotoDialog';
import { TakePhotoDialog } from '@/components/TakePhotoDialog';
import { VisuallyHiddenInput } from '@/components/VisuallyHiddenInput';
import { CameraAlt, Delete, Image, MoreVert } from '@mui/icons-material';
import { Box, IconButton, ListItemIcon, ListItemText, Menu, MenuItem } from '@mui/material';
import {
  type LocalImage,
  type Image as LocalOrSavedImage,
  fileToBase64Url,
  isString,
} from 'common';
import { takePhotoFilename } from 'models';
import { resizeToSquare } from 'mui-ex';
import React from 'react';
import { type Control, useFieldArray } from 'react-hook-form';
import type { ImagesState } from './types';

type Props = {
  control: Control<ImagesState>;
  index: number;
  description: string;
  removable: boolean;
  image?: LocalOrSavedImage;
};

export const MenuButton = (props: Props) => {
  const { control, index, description, removable, image } = props;
  const { update, remove } = useFieldArray({ control, name: 'images' });

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [takePhoto, setTakePhoto] = React.useState(false);
  const [adjustPhoto, setAdjustPhoto] = React.useState(false);
  const ref = React.useRef<HTMLInputElement | null>(null);

  const handleHiddenInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = e.target;
    if (files === null || files.length === 0) return;
    const file = files[0];
    const { name: filename } = file;
    const raw = await fileToBase64Url(file);
    const base64 = await resizeToSquare(raw);
    if (!isString(base64)) throw new Error();
    const image: LocalImage = { id: base64, filename, base64, description: filename };
    update(index, image);
    setAnchorEl(null);
  };

  const handleRemove = () => remove(index);

  const open = Boolean(anchorEl);

  return (
    <Box>
      <IconButton
        size="small"
        sx={{
          color: '#fff',
          '&:hover': {
            color: '#fff',
          },
        }}
        onClick={(e) => setAnchorEl(e.currentTarget)}
      >
        <MoreVert />
      </IconButton>
      <VisuallyHiddenInput
        ref={ref}
        type="file"
        accept="image/*"
        onChange={handleHiddenInputChange}
      />
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={() => setAnchorEl(null)}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={() => setTakePhoto(true)}>
          <ListItemIcon>
            <CameraAlt fontSize="small" />
          </ListItemIcon>
          <ListItemText>撮り直す</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => ref.current?.click()}>
          <ListItemIcon>
            <Image fontSize="small" />
          </ListItemIcon>
          <ListItemText>写真を選択する</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => setAdjustPhoto(true)}>
          <ListItemIcon>
            <Image fontSize="small" />
          </ListItemIcon>
          <ListItemText>写真明度、彩度の調整</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleRemove} disabled={!removable}>
          <ListItemIcon>
            <Delete fontSize="small" />
          </ListItemIcon>
          <ListItemText>削除する</ListItemText>
        </MenuItem>
      </Menu>
      <TakePhotoDialog
        open={takePhoto}
        onClose={() => setTakePhoto(false)}
        filename={takePhotoFilename(index)}
        description={description}
        onTakePhoto={(image) => update(index, image)}
      />
      <AdjustPhotoDialog
        open={adjustPhoto}
        onClose={() => setAdjustPhoto(false)}
        image={image}
        onAdjust={(image) => {
          update(index, image);
          setAdjustPhoto(false);
        }}
      />
    </Box>
  );
};
