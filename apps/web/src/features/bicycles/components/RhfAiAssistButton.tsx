import { type Control, type UseFormWatch, useController } from 'react-hook-form';
import { z } from 'zod';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { LoadingButton } from '@mui/lab';
import { ImageSchema, isLocalImage } from 'common';
import { BodyStateSchema } from './RhfBicycleBodyForm';

export const AiAssistStateSchema = z.object({
  body: BodyStateSchema,
  images: z.array(ImageSchema),
});

export type AiAssistState = z.infer<typeof AiAssistStateSchema>;

type CommonProps = { readOnly?: boolean };
type Props<T extends AiAssistState> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<AiAssistState>;
  watch: UseFormWatch<AiAssistState>;
};

export const RhfAiAssistButton = <T extends AiAssistState>(props: Props<T>) => {
  const { control, watch, readOnly } = props as unknown as InnerProps;
  const { colors } = useAppContext();

  const colorsCtrl = useController({ control, name: 'body.colors' });
  const hasBasketCtrl = useController({ control, name: 'body.hasBasket' });
  const isLockedCtrl = useController({ control, name: 'body.isLocked' });

  const { mutate, isPending } = trpc.bicycles.ai.body.useMutation({
    onSuccess: (result) => {
      const colorId = colors.find((c) => c.code === result.colorCode)?.id;
      if (colorId === undefined) throw new Error(`Color not found for code: ${result.colorCode}`);
      // 色のIDを設定
      colorsCtrl.field.onChange([colorId]);
      hasBasketCtrl.field.onChange(result.hasBasket);
      isLockedCtrl.field.onChange(result.isLocked);
    },
  });
  // アシスト機能を使用する
  const handleClickAssist = async () => {
    const images = watch('images');
    const image = images.find((image) => isLocalImage(image));
    if (image === undefined) return;
    mutate({ dataUrl: image.base64 });
  };
  if (readOnly) return;
  return (
    <LoadingButton size="small" loading={isPending} onClick={handleClickAssist}>
      AIアシストを使用する
    </LoadingButton>
  );
};
