import { Box, Grid, Stack } from '@mui/material';
import type { Control, UseFormWatch } from 'react-hook-form';
import { z } from 'zod';

import type { BicycleLocation } from 'lambda-api';
import { type Address, toAddress } from 'models';
import { ReadOnlyField, RhfCheckbox, RhfSingleSelect } from 'mui-ex';

import { RhfMapPointOnLocation } from '@/components/map/rhf-map-point/_RhfMapPointOnLocation';
import { useAppContext } from '@/contexts/app-context';
import { closest } from 'common';
import { P, match } from 'ts-pattern';

export const LocationStateSchema = z.object({
  isNoParkingArea: z.boolean(),
  landmarkId: z.string().min(1),
  lat: z.number(),
  lng: z.number(),
  prefecture: z.string(),
  city: z.string(),
  town: z.string(),
  furtherAddress: z.string(),
});

export type LocationState = z.infer<typeof LocationStateSchema>;

export const useDefaultLocationState = (): LocationState => {
  const { map } = useAppContext();
  return {
    isNoParkingArea: true,
    landmarkId: '',
    lat: map.defaultLatitude,
    lng: map.defaultLongitude,
    prefecture: '',
    city: '',
    town: '',
    furtherAddress: '',
  };
};

export const useLocationOrCurrentToState = (
  location: BicycleLocation | null | undefined,
  position: google.maps.LatLngLiteral,
  address: Address,
): LocationState => {
  const { landmarks } = useAppContext();
  return {
    isNoParkingArea: location?.isNoParkingArea ?? true,
    landmarkId: location?.landmarkId ?? closest(position, landmarks)?.id ?? '',
    lat: location?.lat ?? position.lat,
    lng: location?.lng ?? position.lng,
    prefecture: location?.prefecture ?? address.prefecture ?? '',
    city: location?.city ?? address.city ?? '',
    town: location?.town ?? address.town ?? '',
    furtherAddress: location?.furtherAddress ?? address.furtherAddress ?? '',
  };
};

export const locationToState = (
  location: BicycleLocation | null | undefined,
  readOnly?: boolean,
): LocationState => {
  return {
    isNoParkingArea: match({ value: location?.isNoParkingArea, readOnly: Boolean(readOnly) })
      .with({ value: P.boolean }, ({ value }) => value)
      .with({ readOnly: false }, () => true)
      .with({ readOnly: true }, () => undefined)
      .exhaustive() as boolean,
    landmarkId: location?.landmarkId ?? '',
    lat: location?.lat ?? (undefined as unknown as number),
    lng: location?.lng ?? (undefined as unknown as number),
    prefecture: location?.prefecture ?? '',
    city: location?.city ?? '',
    town: location?.town ?? '',
    furtherAddress: location?.furtherAddress ?? '',
  };
};

type CommonProps = { readOnly?: boolean };
type State = { location: LocationState };
type AnyState = { location?: any };
type Props<T extends State | AnyState> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<State>;
  watch: UseFormWatch<State>;
};

export const RhfBicycleLocationForm = <T extends State | AnyState>(props: Props<T>) => {
  const { control, watch, readOnly } = props as unknown as InnerProps;
  const { labels, landmarks } = useAppContext();

  const address: Address = {
    prefecture: watch('location.prefecture'),
    city: watch('location.city'),
    town: watch('location.town'),
    furtherAddress: watch('location.furtherAddress'),
  };
  const addressString = toAddress(address);

  return (
    <Grid container spacing={2}>
      {!readOnly && (
        <Grid size={{ xs: 12 }}>
          <Box sx={{ width: '100%', height: 300 }}>
            <RhfMapPointOnLocation control={control} watch={watch} readOnly={readOnly} />
          </Box>
        </Grid>
      )}
      <Grid size={{ xs: 12, md: readOnly ? 6 : 12 }}>
        <Stack spacing={2}>
          <ReadOnlyField label="住所" value={addressString} disabled={!readOnly} />
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, xl: 6 }}>
              <RhfSingleSelect
                control={control}
                name="location.landmarkId"
                label={labels.domain.landmark}
                options={landmarks.map((l) => ({
                  label: l.name,
                  value: l.id,
                }))}
                readOnly={readOnly}
              />
            </Grid>
            <Grid size={{ xs: 12, xl: 6 }}>
              <RhfCheckbox
                control={control}
                name="location.isNoParkingArea"
                label={labels.domain.noParkingArea}
                readOnly={readOnly}
              />
            </Grid>
          </Grid>
        </Stack>
      </Grid>
      {readOnly && (
        <Grid size={{ xs: 12, md: 6 }}>
          <Box sx={{ width: '100%', height: 1, minHeight: 220 }}>
            <RhfMapPointOnLocation control={control} watch={watch} readOnly={true} />
          </Box>
        </Grid>
      )}
    </Grid>
  );
};
