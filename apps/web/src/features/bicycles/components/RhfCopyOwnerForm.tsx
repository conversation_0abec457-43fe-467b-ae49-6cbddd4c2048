import { Box, LinearProgress, Typography } from '@mui/material';
import { type Control, type UseFormWatch, useController, useForm } from 'react-hook-form';
import { z } from 'zod';

import { isNullish } from 'common';
import type { BicycleOwner } from 'lambda-api';
import { RhfCheckbox } from 'mui-ex';

import { trpc, trpcClient } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { useQuery } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import type { BodyState } from './RhfBicycleBodyForm';
import { RhfBicycleOwnerForm, ownerToState } from './RhfBicycleOwnerForm';

export const CopyOwnerStateSchema = z.object({
  isCopy: z.boolean(),
  ownerInfoId: z.string().optional(),
});
export type CopyOwnerState = z.infer<typeof CopyOwnerStateSchema>;

const BicycleOwnerForm = ({ owner }: { owner: BicycleOwner }) => {
  const { labels } = useAppContext();
  const { control, watch } = useForm({
    defaultValues: {
      owner: ownerToState(owner),
    },
  });

  return (
    <>
      <Typography color="textSecondary">{labels.domain.owner}情報</Typography>
      <RhfBicycleOwnerForm control={control} watch={watch} readOnly />
    </>
  );
};

export const useOwnerInfoFromRegistrationNumber = (
  bicycleId: string | undefined,
  registrationNumber: string | undefined,
) =>
  useQuery({
    queryKey: getQueryKey(trpc.registrationNumbers.get, { bicycleId, registrationNumber }),
    queryFn: async () => {
      if (!registrationNumber) return null;
      return trpcClient.registrationNumbers.get.query({ bicycleId, registrationNumber });
    },
  });

type CommonProps = {
  readOnly?: boolean;
  type?: 'checkbox' | 'label';
  bicycleId: string | undefined;
  copied?: boolean;
};
type State = { body: BodyState; copyOwner: CopyOwnerState };
type AnyState = { body?: any; copyOwner?: any };
type Props<T extends State | AnyState> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & { control: Control<State>; watch: UseFormWatch<State> };

export const RhfCopyOwnerForm = <T extends State | AnyState>(props: Props<T>) => {
  const {
    control,
    watch,
    readOnly,
    bicycleId,
    copied,
    type = 'checkbox',
  } = props as unknown as InnerProps;
  const { labels } = useAppContext();

  const registrationNumber = watch('body.registrationNumber');
  const { data: ownerInfo, isPending } = useOwnerInfoFromRegistrationNumber(
    bicycleId,
    registrationNumber,
  );

  const copyOwnerInfoIdCtrl = useController({ control, name: 'copyOwner.ownerInfoId' });

  const onCopyOwner = (isCopy: boolean) => {
    if (isCopy) {
      if (isNullish(ownerInfo)) throw new Error('Owner info not found');
      copyOwnerInfoIdCtrl.field.onChange(ownerInfo.id);
    } else {
      copyOwnerInfoIdCtrl.field.onChange(undefined);
    }
  };
  if (readOnly || copied || !ownerInfo) return;
  const label = `この${labels.domain.bicycle}には過去のデータがあります。${labels.domain.owner}情報を${labels.action.copy}しますか？`;
  return (
    <Box>
      {isPending && <LinearProgress />}
      {type === 'checkbox' && (
        <RhfCheckbox
          control={control}
          name="copyOwner.isCopy"
          label={`${labels.domain.owner}情報${labels.action.copy}`}
          labelTrue={label}
          onChange={onCopyOwner}
        />
      )}
      {type === 'label' && <Typography>{label}</Typography>}
      <Box
        sx={{
          border: 1,
          borderColor: 'grey.300',
          borderRadius: 1,
          p: 2,
          mt: 1,
          mb: 2,
        }}
      >
        <BicycleOwnerForm owner={ownerInfo} />
      </Box>
    </Box>
  );
};
