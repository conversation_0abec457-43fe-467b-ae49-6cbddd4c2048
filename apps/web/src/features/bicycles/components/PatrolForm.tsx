import { zodResolver } from '@hookform/resolvers/zod';
import { FirstPage } from '@mui/icons-material';
import { Alert, Container, Paper, Stack, Typography } from '@mui/material';
import Divider from '@mui/material/Divider';
import { useNavigate } from '@tanstack/react-router';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { ImageSchema, type LocalImage } from 'common';
import type { BicycleBody, BicycleLocation } from 'lambda-api';
import { RhfTextField } from 'mui-ex';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import { eventTypeToIcon } from '@/models/bicycle';

import type { Address, PatrolEventType } from 'models';
import { uploadBicycleImagesToS3 } from '../funcs';
import {
  BodyStateSchema,
  RhfBicycleBodyForm,
  bodyStateToInput,
  bodyToState,
  useRequireBody,
} from './RhfBicycleBodyForm';
import {
  LocationStateSchema,
  RhfBicycleLocationForm,
  useLocationOrCurrentToState,
} from './RhfBicycleLocationForm';
import { CopyOwnerStateSchema, RhfCopyOwnerForm } from './RhfCopyOwnerForm';

import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import {
  RhfBicycleStorageLocationForm,
  useStorageLocationStateSchema,
} from './RhfBicycleStorageLocationForm';
import { RhfBicycleImages } from './rhf-bicycle-images/_RhfBicycleImages';

const useStateSchema = () =>
  z.object({
    images: z.array(ImageSchema),
    location: LocationStateSchema,
    body: BodyStateSchema,
    copyOwner: CopyOwnerStateSchema,
    storageLocation: useStorageLocationStateSchema().optional(),
    memo: z.string(),
  });
type State = z.infer<ReturnType<typeof useStateSchema>>;

type Props = {
  bicycleId: string;
  eventType: PatrolEventType;
  localImages: LocalImage[];
  location: BicycleLocation | null | undefined;
  body: BicycleBody | null | undefined;
  position: google.maps.LatLngLiteral;
  address: Address;
  copied: boolean;
  reset: () => void;
};

export const PatrolForm = ({
  bicycleId,
  eventType,
  localImages,
  location,
  body,
  position,
  address,
  copied,
  reset,
}: Props) => {
  const { tenant, labels, policeStations } = useAppContext();
  const StateSchema = useStateSchema();
  const { control, handleSubmit, watch, formState } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      images: localImages,
      location: useLocationOrCurrentToState(location, position, address),
      body: bodyToState(body, policeStations, tenant),
      copyOwner: { isCopy: false, ownerInfoId: undefined },
      storageLocation: eventType === 'remove' ? { storageId: '', memo: null } : undefined,
      memo: '',
    } as const satisfies State,
    resolver: zodResolver(StateSchema),
  });
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { mutateAsync, error } = trpc.bicycles.patrol.useMutation({
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: getQueryKey(trpc.bicycles.list) });
      await queryClient.invalidateQueries({
        queryKey: getQueryKey(trpc.bicycles.get, { id: bicycleId }),
      });
      navigate({ to: '/bicycles' });
    },
  });

  const { image } = tenant[eventType];

  const submit = async (state: State) => {
    const images = await uploadBicycleImagesToS3(bicycleId, state.images);
    await mutateAsync({
      eventType,
      bicycleId,
      images,
      location: state.location,
      body: bodyStateToInput(state.body, policeStations),
      ownerInfoId: state.copyOwner.ownerInfoId,
      storageLocation: eventType === 'remove' ? state.storageLocation : undefined,
      memo: state.memo,
    });
  };

  const requiredBody = useRequireBody(eventType);
  const bodyType = watch('body.type');
  const bicycleType = bodyType !== '' ? bodyType : undefined;
  const isBicycle = bicycleType === 'bicycle';
  const landmarkId = watch('location.landmarkId') || undefined;

  return (
    <Container maxWidth="md">
      <Paper sx={{ p: 2, m: 2 }}>
        <Stack
          component="form"
          noValidate
          spacing={2}
          sx={{ height: 'max-content', overflow: 'auto' }}
          onSubmit={handleSubmit(submit)}
        >
          <Typography variant="h5">{`${labels.domain.bicycle}の${labels.et[eventType]}`}</Typography>
          <Divider sx={{ my: 3 }} />
          <RhfBicycleImages control={control} watch={watch} settings={image} />
          <RhfBicycleLocationForm control={control} watch={watch} />
          {requiredBody && (
            <>
              <RhfBicycleBodyForm control={control} watch={watch} eventType={eventType} />
              {isBicycle && (
                <RhfCopyOwnerForm
                  control={control}
                  watch={watch}
                  bicycleId={bicycleId}
                  copied={copied}
                />
              )}
            </>
          )}
          {eventType === 'remove' && (
            <RhfBicycleStorageLocationForm
              control={control}
              requiredMemo={false}
              bicycleType={bicycleType}
              landmarkId={landmarkId}
            />
          )}
          <Stack spacing={2} sx={{ mt: 2 }}>
            <RhfTextField
              control={control}
              name="memo"
              label={labels.system.memo}
              multiline
              rows={4}
            />
          </Stack>

          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
          <TwoActionButtons
            primary={{
              type: 'submit',
              icon: eventTypeToIcon(eventType),
              label: labels.et[eventType],
              loading: formState.isSubmitting,
            }}
            secondary={{
              icon: FirstPage,
              label: '最初からやり直す',
              onClick: reset,
            }}
            fullWidth
            spacing={2}
          />
        </Stack>
      </Paper>
    </Container>
  );
};
