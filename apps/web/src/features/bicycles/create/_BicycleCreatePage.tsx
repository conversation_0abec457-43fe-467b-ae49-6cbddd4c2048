import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Close } from '@mui/icons-material';
import { Alert, Collapse, Paper, Stack, Typography } from '@mui/material';
import { useNavigate } from '@tanstack/react-router';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { BicycleStatusSchema, ImageSchema } from 'common';
import { RhfCheckbox, RhfSingleFreeSolo, RhfSingleSelect, RhfTextField } from 'mui-ex';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';
import { eventTypeToIcon, useInvalidateBicycles } from '@/models/bicycle';
import { bicycleCreateMeta } from '@/router/routes/keep/create-route';
import {
  BodyStateSchema,
  RhfBicycleBodyForm,
  bodyStateToInput,
  defaultBodyState,
} from '../components/RhfBicycleBodyForm';
import {
  LocationStateSchema,
  RhfBicycleLocationForm,
  useDefaultLocationState,
} from '../components/RhfBicycleLocationForm';
import {
  OwnerStateSchema,
  RhfBicycleOwnerForm,
  defaultOwnerState,
} from '../components/RhfBicycleOwnerForm';
import {
  RhfBicycleStorageLocationForm,
  defaultStorageLocationState,
  useStorageLocationStateSchema,
} from '../components/RhfBicycleStorageLocationForm';
import { CopyOwnerStateSchema, RhfCopyOwnerForm } from '../components/RhfCopyOwnerForm';
import { RhfBicycleImages } from '../components/rhf-bicycle-images/_RhfBicycleImages';
import { uploadBicycleImagesToS3 } from '../funcs';

// チェックボックスがtrueのときのみ、各状態をチェックする
const CheckableLocationSchema = z.discriminatedUnion('checkLocation', [
  z.object({ checkLocation: z.literal(true), location: LocationStateSchema }),
  z.object({ checkLocation: z.literal(false), location: z.any() }),
]);
const useCheckableStorageLocationSchema = () =>
  z.discriminatedUnion('checkStorageLocation', [
    z.object({
      checkStorageLocation: z.literal(true),
      storageLocation: useStorageLocationStateSchema(),
    }),
    z.object({ checkStorageLocation: z.literal(false), storageLocation: z.any() }),
  ]);
const CheckableBodySchema = z.discriminatedUnion('checkBody', [
  z.object({ checkBody: z.literal(true), body: BodyStateSchema, copyOwner: CopyOwnerStateSchema }),
  z.object({ checkBody: z.literal(false), body: z.any(), copyOwner: z.any() }),
]);
const CheckableOwnerSchema = z.discriminatedUnion('checkOwner', [
  z.object({ checkOwner: z.literal(true), owner: OwnerStateSchema }),
  z.object({ checkOwner: z.literal(false), owner: z.any() }),
]);
const CheckableAssessmentSchema = z.discriminatedUnion('checkAssessment', [
  z.object({
    checkAssessment: z.literal(true),
    assessment: z.object({ price: z.number() }),
  }),
  z.object({ checkAssessment: z.literal(false), assessment: z.any() }),
]);

const useStateSchema = () =>
  z
    .object({
      status: BicycleStatusSchema,
      images: z.array(ImageSchema),
      memo: z.string(),
    })
    .and(CheckableLocationSchema)
    .and(useCheckableStorageLocationSchema())
    .and(CheckableBodySchema)
    .and(CheckableOwnerSchema)
    .and(CheckableAssessmentSchema);
type State = z.infer<ReturnType<typeof useStateSchema>>;

export const BicycleCreatePage = () => {
  const { labels, priceSuggestions, policeStations } = useAppContext();
  const StateSchema = useStateSchema();
  const { control, handleSubmit, watch, formState } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      status: 'store',

      checkLocation: true,
      location: useDefaultLocationState(),

      checkStorageLocation: true,
      storageLocation: defaultStorageLocationState,

      checkBody: true,
      body: defaultBodyState,
      copyOwner: { isCopy: false, ownerInfoId: undefined },

      checkOwner: true,
      owner: defaultOwnerState,

      checkAssessment: true,
      assessment: { price: 0 },
      images: [],
      memo: '',
    } satisfies State,
    resolver: zodResolver(StateSchema),
  });
  const invalidate = useInvalidateBicycles();
  const navigate = useNavigate();
  const { mutate, error } = trpc.bicycles.create.useMutation({
    onSuccess: () => {
      invalidate();
      navigate({ to: '/' });
    },
  });
  const title = bicycleCreateMeta.useTitle();
  const submit = async (state: State) => {
    console.log(state);
    const bicycleId = crypto.randomUUID();
    const images = await uploadBicycleImagesToS3(bicycleId, state.images);

    mutate({
      bicycleId,
      status: state.status,
      location: state.checkLocation ? state.location : undefined,
      storageLocation: state.checkStorageLocation ? state.storageLocation : undefined,
      body: state.checkBody ? bodyStateToInput(state.body, policeStations) : undefined,
      ownerInfoId: state.copyOwner.ownerInfoId,
      owner: state.checkOwner ? state.owner : undefined,
      assessment: state.checkAssessment ? state.assessment : undefined,
      images,
      memo: state.memo,
    });
  };

  const isBicycle = watch('body.type') === 'bicycle';
  return (
    <MainLayout title={title} scrollable>
      <form noValidate onSubmit={handleSubmit(submit)}>
        <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
          <Typography variant="h6">ステータス</Typography>
          <RhfSingleSelect
            control={control}
            name="status"
            label="ステータス"
            options={BicycleStatusSchema.options.map((s) => ({ label: labels.et[s], value: s }))}
          />

          <Typography variant="h6">位置情報</Typography>
          <RhfCheckbox control={control} name="checkLocation" label="位置情報を入力する" />
          <Collapse in={watch('checkLocation')}>
            <RhfBicycleLocationForm control={control} watch={watch} />
          </Collapse>

          <Typography variant="h6">保管場所</Typography>
          <RhfCheckbox control={control} name="checkStorageLocation" label="保管場所を入力する" />
          <Collapse in={watch('checkStorageLocation')}>
            <RhfBicycleStorageLocationForm control={control} />
          </Collapse>

          <Typography variant="h6">車体情報</Typography>
          <RhfCheckbox control={control} name="checkBody" label="車体情報を入力する" />
          <Collapse in={watch('checkBody')}>
            <RhfBicycleBodyForm control={control} watch={watch} />
            {isBicycle && (
              <RhfCopyOwnerForm control={control} watch={watch} bicycleId={undefined} />
            )}
          </Collapse>

          <Typography variant="h6">所有者情報</Typography>
          <RhfCheckbox control={control} name="checkOwner" label="所有者情報を入力する" />
          <Collapse in={watch('checkOwner')}>
            <RhfBicycleOwnerForm control={control} watch={watch} />
          </Collapse>

          <Typography variant="h6">査定情報</Typography>
          <RhfCheckbox control={control} name="checkAssessment" label="査定情報を入力する" />
          <Collapse in={watch('checkAssessment')}>
            <Stack spacing={2}>
              <RhfSingleFreeSolo
                control={control}
                name="assessment.price"
                label="価格"
                options={priceSuggestions.map((g) => String(g.price))}
              />
              <RhfTextField control={control} name="assessment.price" label="価格" number />
            </Stack>
          </Collapse>

          <RhfBicycleImages control={control} watch={watch} />

          <RhfTextField
            control={control}
            name="memo"
            label={labels.system.memo}
            multiline
            rows={4}
          />

          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
          <TwoActionButtons
            primary={{
              type: 'submit',
              icon: eventTypeToIcon('create'),
              label: labels.action.create,
              loading: formState.isSubmitting,
            }}
            secondary={{
              icon: Close,
              label: labels.action.cancel,
              onClick: () => navigate({ to: '/' }),
            }}
            fullWidth
            spacing={2}
          />
        </Stack>
      </form>
    </MainLayout>
  );
};
