import { Paper } from '@mui/material';
import type { GridRowParams } from '@mui/x-data-grid';
import { useNavigate } from '@tanstack/react-router';

import { DataGrid } from 'mui-ex';

import { trpc } from '@/api';

import {
  announcementStatusCol,
  bicycleTypeCol,
  bodySerialNumberCol,
  colorCol,
  conditionCol,
  createdAtCol,
  hasBasketCol,
  imagesCol,
  isLockedCol,
  isNoParkingAreaCol,
  landmarkCol,
  numberPlateCol,
  ownerStatusCol,
  registrationNumberCol,
  serialNoCol,
  statusCol,
} from '@/components/bicycles/columns';

import { MainLayout } from '@/components/core/layout/MainLayout';
import { createUseColumns } from '@/hooks/useColumns';
import { DataGridToolbar } from '@/libs/mui-x/grid/DataGridToolbar';
import { useGridExport } from '@/libs/mui-x/grid/utils';
import { returnToOwnerListMeta } from '@/router/routes/keep/return-to-owner-list-route';
import { getQueryKey } from '@trpc/react-query';

const useColumns = createUseColumns([
  imagesCol,
  createdAtCol,
  serialNoCol,
  statusCol,
  announcementStatusCol,
  ownerStatusCol,
  landmarkCol,
  bicycleTypeCol,
  registrationNumberCol,
  bodySerialNumberCol,
  numberPlateCol,
  isNoParkingAreaCol,
  hasBasketCol,
  isLockedCol,
  colorCol,
  conditionCol,
]);

export const BicycleReturnToOwnerListPage = () => {
  const title = returnToOwnerListMeta.useTitle();
  const { data: bicycles = [], isPending } = trpc.bicycles.list.useQuery({ type: 'returnable' });
  const columns = useColumns(bicycles);

  const useBicycleReturnExportFormatter = () => {
    const cols = useColumns([]);
    return useGridExport(cols, title);
  };

  const Toolbar = () => {
    const bicycleReturnExportFormatter = useBicycleReturnExportFormatter();
    return (
      <DataGridToolbar
        queryKey={getQueryKey(trpc.bicycles.list, { type: 'returnable' })}
        exportFormatter={bicycleReturnExportFormatter}
      />
    );
  };

  const navigate = useNavigate();
  const handleRowClick = (params: GridRowParams) =>
    navigate({
      to: '/bicycles/$id/return',
      params: { id: params.id.toString() },
    });

  return (
    <MainLayout title={title} maxWidth="xl">
      <Paper sx={{ p: 2, height: 1, overflow: 'auto' }}>
        <DataGrid
          persistent="/bicycles/return-to-owner-list"
          columns={columns}
          rows={bicycles}
          onRowClick={handleRowClick}
          disableRowSelectionOnClick
          slots={{ toolbar: Toolbar }}
          loading={isPending}
          sx={{
            // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
            '&.MuiDataGrid-columnHeader:focus-within': { outline: 'none' },
            '&.MuiDataGrid-cell:focus-within': { outline: 'none' },
            '&.MuiTablePagination-select': { mr: 2 },
          }}
        />
      </Paper>
    </MainLayout>
  );
};
