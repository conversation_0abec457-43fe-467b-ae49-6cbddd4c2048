import { Alert, Dialog, DialogActions, DialogContent, DialogTitle, Stack } from '@mui/material';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import {
  deadlineDateCol,
  deadlineStatusCol,
  useBaseBicycleColumns,
} from '@/components/bicycles/columns';
import { useAppContext } from '@/contexts/app-context';
import { createUseColumns } from '@/hooks/useColumns';
import { zodResolver } from '@hookform/resolvers/zod';
import { Close, Delete } from '@mui/icons-material';
import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import { stringOrNull } from 'common';
import type { Bicycles } from 'lambda-api';
import { DataGrid, RhfTextField } from 'mui-ex';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

type Props = {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  bicycles: Bicycles;
};

const StateSchema = z.object({
  memo: z.string(),
});
type State = z.infer<typeof StateSchema>;

const useAdditionalColumns = createUseColumns([deadlineStatusCol, deadlineDateCol]);

export const CancelDeadlinesDialog = (props: Props) => {
  const { open, onClose, onSuccess, bicycles } = props;
  const { labels } = useAppContext();
  const { control, handleSubmit } = useForm<State>({
    mode: 'onChange',
    defaultValues: { memo: '' },
    resolver: zodResolver(StateSchema),
  });
  const queryClient = useQueryClient();
  const { mutateAsync, error, isPending } = trpc.bicycles.deadline.cancel.useMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getQueryKey(trpc.bicycles.list, { type: 'stored' }),
      });
      onClose();
      onSuccess();
    },
  });

  const submit = async ({ memo }: State) => {
    const bicycleIds = bicycles.map((b) => b.id);
    await mutateAsync({ bicycleIds, memo: stringOrNull(memo) });
  };
  const baseColumns = useBaseBicycleColumns(bicycles);
  const additionalColumns = useAdditionalColumns(bicycles);
  const columns = [...baseColumns, ...additionalColumns];

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      scroll="paper"
      slotProps={{
        paper: {
          component: 'form',
          onSubmit: handleSubmit(submit),
        },
      }}
    >
      <DialogTitle>
        {labels.p.storeDeadline}の{labels.action.cancel}
      </DialogTitle>
      <DialogContent dividers>
        <Stack spacing={2} sx={{ height: 1 }}>
          <Stack sx={{ flexGrow: 1, overflow: 'auto' }}>
            <DataGrid
              columns={columns}
              rows={bicycles}
              sx={{
                // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
                '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
                '& .MuiTablePagination-select': { mr: 2 },
              }}
            />
          </Stack>
          <RhfTextField
            control={control}
            name="memo"
            label={labels.system.memo}
            multiline
            rows={4}
          />
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
        </Stack>
      </DialogContent>
      <DialogActions>
        <TwoActionButtons
          primary={{
            type: 'submit',
            icon: Delete,
            label: `${labels.p.storeDeadline}を${labels.action.cancel}`,
            loading: isPending,
          }}
          secondary={{
            icon: Close,
            end: true,
            label: labels.action.cancel,
            onClick: onClose,
          }}
          spacing={1}
        />
      </DialogActions>
    </Dialog>
  );
};
