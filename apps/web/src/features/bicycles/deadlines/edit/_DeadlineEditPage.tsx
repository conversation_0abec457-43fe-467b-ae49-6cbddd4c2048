import { Button, Paper, Stack } from '@mui/material';

import { trpc } from '@/api';
import { RouterButton } from '@/components/RouterButton';
import {
  deadlineDateCol,
  deadlineStatusCol,
  useBaseBicycleColumns,
} from '@/components/bicycles/columns';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';
import { createUseColumns } from '@/hooks/useColumns';
import { deadlineEdit } from '@/router/routes/keep/deadlines';
import { DataGrid } from 'mui-ex';
import React from 'react';
import { CancelDeadlinesDialog } from './CancelDeadlinesDialog';
import { UpdateDeadlinesDialog } from './UpdateDeadlinesDialog';

const useAdditionalColumns = createUseColumns([deadlineStatusCol, deadlineDateCol]);

export const DeadlineEditPage = () => {
  const { labels } = useAppContext();
  const title = deadlineEdit.meta.useTitle();
  const [ids, setIds] = React.useState<Set<string>>(new Set());
  const { data: bicycles = [], isPending } = trpc.bicycles.list.useQuery({ type: 'stored' });
  const baseColumns = useBaseBicycleColumns(bicycles);
  const additionalColumns = useAdditionalColumns(bicycles);
  const columns = [...baseColumns, ...additionalColumns];
  const disabled = ids.size === 0;

  const [updateDialog, setUpdateDialog] = React.useState(false);
  const [cancelDialog, setCancelDialog] = React.useState(false);

  return (
    <MainLayout title={title}>
      <Stack component={Paper} spacing={2} sx={{ p: 2, height: 1 }}>
        <Stack sx={{ flexGrow: 1, overflow: 'auto' }}>
          <DataGrid
            columns={columns}
            rows={bicycles}
            checkboxSelection
            rowSelectionModel={{ type: 'include', ids }}
            onRowSelectionModelChange={(model) => setIds(model.ids as Set<string>)}
            disableRowSelectionOnClick
            loading={isPending}
            sx={{
              // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
              '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
              '& .MuiTablePagination-select': { mr: 2 },
            }}
          />
        </Stack>
        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <RouterButton to="/deadlines">キャンセル</RouterButton>
          <Button variant="outlined" disabled={disabled} onClick={() => setCancelDialog(true)}>
            {labels.p.storeDeadline}を取り消す
          </Button>
          <CancelDeadlinesDialog
            open={cancelDialog}
            onClose={() => setCancelDialog(false)}
            onSuccess={() => setIds(new Set())}
            bicycles={bicycles.filter((b) => ids.has(b.id))}
          />
          <Button variant="contained" disabled={disabled} onClick={() => setUpdateDialog(true)}>
            {labels.p.storeDeadline}を更新する
          </Button>
          <UpdateDeadlinesDialog
            open={updateDialog}
            onClose={() => setUpdateDialog(false)}
            onSuccess={() => setIds(new Set())}
            bicycles={bicycles.filter((b) => ids.has(b.id))}
          />
        </Stack>
      </Stack>
    </MainLayout>
  );
};
