import {
  Al<PERSON>,
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Stack,
  type Theme,
  ThemeProvider,
} from '@mui/material';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import {
  deadlineDateCol,
  deadlineStatusCol,
  useBaseBicycleColumns,
} from '@/components/bicycles/columns';
import { useAppContext } from '@/contexts/app-context';
import { dateFormatter } from '@/funcs/date';
import { createUseColumns } from '@/hooks/useColumns';
import { zodResolver } from '@hookform/resolvers/zod';
import { Close, Edit } from '@mui/icons-material';
import type { GridColDef } from '@mui/x-data-grid';
import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import { stringOrNull } from 'common';
import { startOfDay } from 'date-fns';
import type { Bicycles } from 'lambda-api';
import { DataGrid, RhfDatePicker, RhfTextField } from 'mui-ex';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

type Props = {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  bicycles: Bicycles;
};

const StateSchema = z.object({
  date: z.date(),
  memo: z.string(),
});
type State = z.infer<typeof StateSchema>;

const useAdditionalColumns = createUseColumns([deadlineStatusCol, deadlineDateCol]);

export const UpdateDeadlinesDialog = (props: Props) => {
  const { open, onClose, onSuccess, bicycles } = props;
  const { labels } = useAppContext();
  const { control, watch, handleSubmit } = useForm<State>({
    mode: 'onChange',
    defaultValues: {
      date: startOfDay(new Date()),
      memo: '',
    },
    resolver: zodResolver(StateSchema),
  });
  const queryClient = useQueryClient();
  const { mutateAsync, error, isPending } = trpc.bicycles.deadline.update.useMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getQueryKey(trpc.bicycles.list, { type: 'stored' }),
      });
      onClose();
      onSuccess();
    },
  });

  const submit = async ({ date, memo }: State) => {
    const bicycleIds = bicycles.map((b) => b.id);
    await mutateAsync({ date, bicycleIds, memo: stringOrNull(memo) });
  };
  const baseColumns = useBaseBicycleColumns(bicycles);
  const additionalColumns = useAdditionalColumns(bicycles);
  const columns = [
    ...baseColumns,
    ...additionalColumns,
    {
      field: 'updateDate',
      type: 'date',
      headerName: '変更後',
      valueGetter: (_, row) => row.updateDate,
      valueFormatter: (value: Date | undefined) => dateFormatter(value),
    } as const satisfies GridColDef,
  ];
  const updateDate = watch('date');
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      scroll="paper"
      slotProps={{
        paper: {
          component: 'form',
          onSubmit: handleSubmit(submit),
        },
      }}
    >
      <DialogTitle>
        {labels.p.storeDeadline}の{labels.action.edit}
      </DialogTitle>
      <DialogContent dividers>
        <Stack spacing={2} sx={{ height: 1 }}>
          <Box>
            <ThemeProvider<Theme>
              theme={(t) => ({
                ...t,
                components: { ...t.components, MuiButton: { defaultProps: { variant: 'text' } } },
              })}
            >
              <RhfDatePicker control={control} name="date" label={labels.p.storeDeadline} />
            </ThemeProvider>
          </Box>
          <Stack sx={{ flexGrow: 1, overflow: 'auto' }}>
            <DataGrid
              columns={columns}
              rows={bicycles.map((b) => ({ ...b, updateDate }))}
              sx={{
                // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
                '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
                '& .MuiTablePagination-select': { mr: 2 },
              }}
            />
          </Stack>
          <RhfTextField
            control={control}
            name="memo"
            label={labels.system.memo}
            multiline
            rows={4}
          />
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
        </Stack>
      </DialogContent>
      <DialogActions>
        <TwoActionButtons
          primary={{
            type: 'submit',
            icon: Edit,
            label: `${labels.p.storeDeadline}を${labels.doing.edit}`,
            loading: isPending,
          }}
          secondary={{
            icon: Close,
            end: true,
            label: labels.doing.cancel,
            onClick: onClose,
          }}
          spacing={1}
        />
      </DialogActions>
    </Dialog>
  );
};
