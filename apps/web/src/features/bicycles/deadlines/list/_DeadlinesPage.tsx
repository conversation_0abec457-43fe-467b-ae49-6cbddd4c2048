import { trpc } from '@/api';
import { Box, Paper } from '@mui/material';
import type { GridRowParams } from '@mui/x-data-grid';
import { useNavigate } from '@tanstack/react-router';
import { DataGrid } from 'mui-ex';

import { DataGridToolbar } from '@/libs/mui-x/grid/DataGridToolbar';

import { RouterButton } from '@/components/RouterButton';

import {
  deadlineDateCol,
  deadlineStatusCol,
  useBaseBicycleColumns,
} from '@/components/bicycles/columns';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { createUseColumns } from '@/hooks/useColumns';
import { deadlineList } from '@/router/routes/keep/deadlines';
import { Edit } from '@mui/icons-material';
import { getQueryKey } from '@trpc/react-query';

const Toolbar = () => {
  return (
    <DataGridToolbar
      to="/deadlines/create"
      queryKey={getQueryKey(trpc.bicycles.list, { type: 'stored' })}
    />
  );
};

const useAdditionalColumns = createUseColumns([deadlineStatusCol, deadlineDateCol]);

export const DeadlinesPage = () => {
  const title = deadlineList.meta.useTitle();
  const navigate = useNavigate();

  const { data: bicycles = [], isPending } = trpc.bicycles.list.useQuery({ type: 'stored' });
  const baseColumns = useBaseBicycleColumns(bicycles);
  const additionalColumns = useAdditionalColumns(bicycles);
  const columns = [...baseColumns, ...additionalColumns];

  const handleRowClick = (params: GridRowParams) => {
    navigate({ to: '/bicycles/$id', params: { id: params.id as string } });
  };

  return (
    <MainLayout
      title={title}
      firstRight={
        <>
          <Box sx={{ flexGrow: 1 }} />
          <RouterButton
            variant="contained"
            to="/deadlines/edit"
            color="primary"
            startIcon={<Edit />}
          >
            編集
          </RouterButton>
        </>
      }
    >
      <Paper sx={{ height: 1 }}>
        <DataGrid
          persistent="/deadlines"
          columns={columns}
          rows={bicycles}
          onRowClick={handleRowClick}
          disableRowSelectionOnClick
          slots={{ toolbar: Toolbar }}
          loading={isPending}
          sx={{
            // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
            '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
            '& .MuiTablePagination-select': { mr: 2 },
          }}
        />
      </Paper>
    </MainLayout>
  );
};
