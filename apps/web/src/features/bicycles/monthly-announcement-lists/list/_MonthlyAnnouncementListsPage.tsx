import { Paper, Stack, Typography } from '@mui/material';
import type { GridRowParams } from '@mui/x-data-grid';
import type { GridColDef } from '@mui/x-data-grid';
import { useNavigate } from '@tanstack/react-router';

import type { MonthlyAnnouncementLists } from 'lambda-api';
import { DataGrid } from 'mui-ex';

import { trpc } from '@/api';
import { useAppContext } from '@/contexts/app-context';
import { datetimeFormat, datetimeFormatter } from '@/funcs/date';
import { getQueryKey } from '@trpc/react-query';

import { MainLayout } from '@/components/core/layout/MainLayout';
import { useTextToPx } from '@/hooks/useTextToPx';
import { DataGridToolbar } from '@/libs/mui-x/grid/DataGridToolbar';
import { policeReferenceListRoute } from '@/router/routes/keep/police-references';
import { addDays } from 'date-fns';
import { MonthlyAnnouncementListStatusChip } from '../common/MonthlyAnnouncementListStatusChip';

type MonthlyAnnouncementList = MonthlyAnnouncementLists[number];
type ColDef = GridColDef<MonthlyAnnouncementList> & {
  field: keyof MonthlyAnnouncementList | keyof MonthlyAnnouncementList['_count'];
};

export const useColumns = () => {
  const { labels } = useAppContext();
  const fit = useTextToPx();

  const statusCol: ColDef = {
    field: 'status',
    headerName: `${labels.da.announce}${labels.system.status}`,
    width: 160,
    valueGetter: (_, row) => labels.monthlyAnnouncementListStatus[row.status],
    renderCell: ({ row }) => <MonthlyAnnouncementListStatusChip status={row.status} />,
  };
  const startedAtCol: ColDef = {
    field: 'startedAt',
    type: 'dateTime',
    headerName: `${labels.da.announce}開始日時`,
    width: fit(datetimeFormat),
    valueGetter: (_, row) => row.startedAt ?? '',
    valueFormatter: (value: Date) => datetimeFormatter(value),
  };
  const endedAtCol: ColDef = {
    field: 'days',
    type: 'dateTime',
    headerName: `${labels.da.announce}終了日時`,
    width: fit(datetimeFormat),
    valueGetter: (_, row) => (row.startedAt && row.days ? addDays(row.startedAt, row.days) : ''),
    valueFormatter: (value: Date) => datetimeFormatter(value),
  };
  const countCol: ColDef = {
    field: '_count',
    type: 'number',
    headerName: '回答日時',
    width: fit(datetimeFormat),
    valueGetter: (_, row) => row._count,
  };

  const memoCol: ColDef = {
    field: 'memo',
    headerName: labels.system.memo,
    width: 200,
  };

  return [statusCol, startedAtCol, endedAtCol, countCol, memoCol];
};

const Toolbar = () => {
  return (
    <DataGridToolbar
      // to="/police-references/create"
      queryKey={getQueryKey(trpc.monthlyAnnouncementList.list)}
    />
  );
};

export const MonthlyAnnouncementListsPage = () => {
  const { labels } = useAppContext();
  const columns = useColumns();
  const { data: agg } = trpc.bicycles.count.useQuery({ type: 'monthlyAnnouncementListAppendable' });
  const { data: lists = [], isPending } = trpc.monthlyAnnouncementList.list.useQuery();
  const navigate = useNavigate();
  const title = policeReferenceListRoute.meta.useTitle();
  const handleRowClick = (params: GridRowParams) => {
    navigate({ to: '/police-references/$id', params: { id: params.id.toString() } });
  };
  const count = agg?._count ?? 0;
  const countText = count === 0 ? 'ありません。' : `${agg?._count}台です。`;
  const statusText = `現在${labels.domain.announcementSheet}に${labels.action.append}可能な${labels.domain.bicycle}は${countText}`;

  return (
    <MainLayout scrollable title={title} maxWidth="lg">
      <Stack spacing={2} sx={{ height: 1 }}>
        <Typography variant="body2">{statusText}</Typography>
        <Stack component={Paper} sx={{ width: 1, flexGrow: 1, overflow: 'auto' }}>
          <DataGrid
            persistent="/police-references"
            columns={columns}
            rows={lists}
            onRowClick={handleRowClick}
            disableRowSelectionOnClick
            slots={{ toolbar: Toolbar }}
            loading={isPending}
            sx={{
              // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
              '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
              '& .MuiTablePagination-select': { mr: 2 },
            }}
          />
        </Stack>
      </Stack>
    </MainLayout>
  );
};
