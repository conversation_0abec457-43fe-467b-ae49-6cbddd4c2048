import { useAppContext } from '@/contexts/app-context';
import { DateRange, EventAvailable, HourglassTop } from '@mui/icons-material';
import { Chip, type ChipProps } from '@mui/material';
import { blueGrey, green, lightBlue } from '@mui/material/colors';
import type { MonthlyAnnouncementListStatus } from 'lambda-api';
import { match } from 'ts-pattern';

const statusToIcon = (status: MonthlyAnnouncementListStatus) =>
  match(status)
    .with('waiting', () => HourglassTop)
    .with('doing', () => DateRange)
    .with('done', () => EventAvailable)
    .exhaustive();

const statusToColor = (status: MonthlyAnnouncementListStatus) =>
  match(status)
    .with('waiting', () => lightBlue)
    .with('doing', () => green)
    .with('done', () => blueGrey)
    .exhaustive();

type Props = ChipProps & { status: MonthlyAnnouncementListStatus };

export const MonthlyAnnouncementListStatusChip = ({ status, ...props }: Props) => {
  const { labels } = useAppContext();
  const label = labels.monthlyAnnouncementListStatus[status];
  const Icon = statusToIcon(status);
  const color = statusToColor(status);
  return (
    <Chip
      {...props}
      label={label}
      icon={<Icon style={{ color: color['300'] }} />}
      sx={{ ...props.sx, bgcolor: color['50'] }}
    />
  );
};
