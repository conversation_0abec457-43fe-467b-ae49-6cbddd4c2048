import { Paper } from '@mui/material';
import { Box } from '@mui/system';
import type { GridRowParams } from '@mui/x-data-grid';
import { useNavigate } from '@tanstack/react-router';

import {
  DataGrid,
  FilterModelContext,
  type FilterModelContextValue,
  useFilterModelState,
} from 'mui-ex';

import { trpc } from '@/api';
import { useGridExport } from '@/libs/mui-x/grid/utils';

import {
  abandonedAddressCol,
  announcementStatusCol,
  bicycleTypeCol,
  bodySerialNumberCol,
  colorCol,
  conditionCol,
  createdAtCol,
  hasBasketCol,
  imagesCol,
  isCollectedStorageFeeCol,
  isLockedCol,
  isNoParkingAreaCol,
  landmarkCol,
  numberPlateCol,
  ownerAddressCol,
  ownerNameCol,
  ownerPostalCodeCol,
  ownerStatusCol,
  referenceStatusCol,
  registrationNumberCol,
  releaseStatusCol,
  serialNoCol,
  statusCol,
  storageNameCol,
} from '@/components/bicycles/columns';
import { ToolbarWithRichFilter } from '@/components/bicycles/toolbar-with-rich-filter/_ToolbarWithRichFilter';
import { createUseColumns } from '@/hooks/useColumns';
import { bicyclesMeta } from '@/router/routes/bicycles-list-route';
import { getQueryKey } from '@trpc/react-query';
import React from 'react';

const useColumns = createUseColumns([
  imagesCol,
  createdAtCol,
  serialNoCol,
  statusCol,
  announcementStatusCol,
  referenceStatusCol,
  ownerStatusCol,
  releaseStatusCol,
  landmarkCol,
  bicycleTypeCol,
  registrationNumberCol,
  bodySerialNumberCol,
  numberPlateCol,
  isNoParkingAreaCol,
  hasBasketCol,
  isLockedCol,
  isCollectedStorageFeeCol,
  colorCol,
  conditionCol,
  ownerNameCol,
  ownerPostalCodeCol,
  ownerAddressCol,
  abandonedAddressCol,
  storageNameCol,
]);

const Toolbar = () => {
  const columns = useColumns([]);
  const bicycleExportFormatter = useGridExport(columns, bicyclesMeta.useTitle());

  return (
    <ToolbarWithRichFilter
      to="/bicycles/create"
      queryKey={getQueryKey(trpc.bicycles.list)}
      persistent="bicycles-search-set"
      exportFormatter={bicycleExportFormatter}
    />
  );
};

export const BicyclesPage = () => {
  const { data: bicycles = [], isPending } = trpc.bicycles.list.useQuery();
  const columns = useColumns(bicycles);

  const { filterModel, setFilterModel, setFilterItems } = useFilterModelState({
    persistent: 'bicycles',
  });
  const filterModelContextValue: FilterModelContextValue = React.useMemo(
    () => ({ rows: bicycles, columns, filterModel, setFilterModel, setFilterItems }),
    [bicycles, columns, filterModel, setFilterModel, setFilterItems],
  );

  const navigate = useNavigate();
  const handleRowClick = (params: GridRowParams) =>
    navigate({
      to: '/bicycles/$id',
      params: { id: params.id.toString() },
    });

  return (
    <FilterModelContext.Provider value={filterModelContextValue}>
      <Box sx={{ p: { xs: 1, sm: 3 }, height: 1 }}>
        <Paper sx={{ display: 'flex', width: 1, height: 1 }}>
          <DataGrid
            persistent="/bicycles"
            columns={columns}
            rows={bicycles}
            filter={{ filterModel, setFilterModel }}
            onRowClick={handleRowClick}
            disableRowSelectionOnClick
            slots={{ toolbar: Toolbar }}
            loading={isPending}
            sx={{
              // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
              '&.MuiDataGrid-columnHeader:focus-within': { outline: 'none' },
              '&.MuiDataGrid-cell:focus-within': { outline: 'none' },
              '&.MuiTablePagination-select': { mr: 2 },
            }}
          />
        </Paper>
      </Box>
    </FilterModelContext.Provider>
  );
};
