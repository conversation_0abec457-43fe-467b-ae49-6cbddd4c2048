import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Close, Delete, Edit } from '@mui/icons-material';
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Paper,
  Stack,
} from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { enqueueSnackbar } from 'notistack';
import React from 'react';
import { useForm } from 'react-hook-form';

import { useAppContext } from '@/contexts/app-context';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { transferContractRouters } from '@/router/routes/release/transfer-contracts';
import { getQueryKey } from '@trpc/react-query';
import type { Bicycles, TransferContract } from 'lambda-api';
import {
  RhfTransferContract,
  type TransferContractState,
  TransferContractStateSchema,
  transferContractToState,
} from '../common/RhfTransferContract';

type Props = {
  contract: TransferContract;
  bicycles: Bicycles;
};

export const TransferContractEdit = ({ contract: { id, ...contract }, bicycles }: Props) => {
  const { labels } = useAppContext();
  const { control, watch, handleSubmit } = useForm<TransferContractState>({
    mode: 'onChange',
    defaultValues: transferContractToState(contract),
    resolver: zodResolver(TransferContractStateSchema),
  });

  const queryClient = useQueryClient();
  const onSuccess = async () => {
    const queryKeys = [
      getQueryKey(trpc.contracts.transfer.list),
      getQueryKey(trpc.contracts.transfer.get, { id }),
    ];
    await Promise.all(queryKeys.map((queryKey) => queryClient.invalidateQueries({ queryKey })));
    navigate({ to: '/transfer-contracts' });
  };
  const updateMutation = trpc.contracts.transfer.update.useMutation({ onSuccess });
  const cancelMutation = trpc.contracts.transfer.cancel.useMutation({ onSuccess });
  const navigate = useNavigate();
  const [cancelDialog, setCancelDialog] = React.useState(false);
  const title = transferContractRouters.meta.edit.useTitle();
  const submit = ({ status, ...input }: TransferContractState) => {
    if (status !== 'canceled')
      return updateMutation.mutate(
        { id, status, ...input },
        { onSuccess: () => enqueueSnackbar('譲渡契約を変更しました', { variant: 'success' }) },
      );
    return setCancelDialog(true);
  };

  const submitCancel = () => {
    cancelMutation.mutate(
      { id },
      { onSuccess: () => enqueueSnackbar('譲渡契約をキャンセルしました', { variant: 'success' }) },
    );
  };

  return (
    <MainLayout scrollable title={title}>
      <>
        <Stack component="form" noValidate spacing={2} onSubmit={handleSubmit(submit)}>
          <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
            <RhfTransferContract control={control} watch={watch} bicycles={bicycles} mode="edit" />
          </Stack>
          <Stack alignItems="end" sx={{ mt: 2 }}>
            <TwoActionButtons
              primary={{
                type: 'submit',
                icon: Edit,
                label: '保存',
              }}
              secondary={{
                icon: Close,
                label: 'キャンセル',
                onClick: () => navigate({ to: '/transfer-contracts/$id', params: { id } }),
                end: true,
              }}
            />
          </Stack>
        </Stack>
        <Dialog open={cancelDialog} onClose={() => setCancelDialog(false)}>
          <DialogTitle>{`${labels.da.transfer}${labels.business.contract}の${labels.action.cancel}`}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {`${labels.da.transfer}${labels.p.contractCannotCancel}`}
            </DialogContentText>
          </DialogContent>
          <Box component="form" noValidate onSubmit={handleSubmit(submitCancel)}>
            <DialogActions>
              <TwoActionButtons
                primary={{
                  type: 'submit',
                  icon: Delete,
                  label: `契約を${labels.doing.cancel}`,
                }}
                secondary={{
                  icon: Close,
                  label: labels.doing.close,
                  onClick: () => setCancelDialog(false),
                  end: true,
                }}
              />
            </DialogActions>
          </Box>
        </Dialog>
      </>
    </MainLayout>
  );
};
