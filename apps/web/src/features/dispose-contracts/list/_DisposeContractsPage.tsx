import { trpc } from '@/api';
import { Paper, Stack, Typography } from '@mui/material';
import type { GridRowParams } from '@mui/x-data-grid';
import type { GridColDef } from '@mui/x-data-grid';
import { useNavigate } from '@tanstack/react-router';
import { DataGrid } from 'mui-ex';

import { useAppContext } from '@/contexts/app-context';
import { useTextToPx } from '@/hooks/useTextToPx';
import { DataGridToolbar } from '@/libs/mui-x/grid/DataGridToolbar';
import { useGridExport } from '@/libs/mui-x/grid/utils';
import { contractStatusToColor, contractStatusToIcon } from '@/models/contract';
import { disposeContractRouters } from '@/router/routes/release/dispose-contracts';
import { Chip } from '@mui/material';
import { getQueryKey } from '@trpc/react-query';
import type { DisposeContracts } from 'lambda-api';

type DisposeContract = DisposeContracts[number];
type ColDef = GridColDef<DisposeContract> & {
  field: keyof DisposeContract | `dealer.${keyof NonNullable<DisposeContract['dealer']>}`;
};

export const useColumns = () => {
  const { labels } = useAppContext();
  const fit = useTextToPx();

  const statusCol: ColDef = {
    field: 'status',
    headerName: '契約ステータス',
    width: fit('契約ステータス'),
    valueGetter: (_, row) => labels.contractStatus[row.status],
    renderCell: ({ row }) => {
      const label = labels.contractStatus[row.status];
      const Icon = contractStatusToIcon(row.status);
      const color = contractStatusToColor(row.status);
      return (
        <Chip
          label={label}
          icon={<Icon style={{ color: color['300'] }} />}
          sx={{ bgcolor: color['50'] }}
        />
      );
    },
  };
  const dealerNameCol: ColDef = {
    field: 'dealer.name',
    headerName: '購入者',
    width: fit('●●●●＠●●●●＠'),
    valueGetter: (_, row) => row.dealer?.name,
  };
  const countCol: ColDef = {
    headerName: '台数',
    field: '_count',
    type: 'number',
    valueGetter: (_, row) => `${row._count.events.toLocaleString()} 台`,
  };
  const priceCol: ColDef = {
    field: 'cost',
    headerName: labels.business.cost,
    width: fit('100,000 円'),
    type: 'number',
    valueFormatter: (value: number | null) => (value ? `${value.toLocaleString()} 円` : '-'),
  };

  return [statusCol, dealerNameCol, countCol, priceCol];
};

const useDisposeContractExport = () => {
  const columns = useColumns();
  return useGridExport(columns, disposeContractRouters.meta.list.useTitle());
};

const Toolbar = () => {
  const exportFormatter = useDisposeContractExport();
  return (
    <DataGridToolbar
      to="/dispose-contracts/create"
      queryKey={getQueryKey(trpc.contracts.dispose.list)}
      exportFormatter={exportFormatter}
    />
  );
};

export const DisposeContractsPage = () => {
  const columns = useColumns();
  const { data: contracts = [], isPending } = trpc.contracts.dispose.list.useQuery();
  const navigate = useNavigate();
  const handleRowClick = (params: GridRowParams) => {
    navigate({ to: '/dispose-contracts/$id', params: { id: params.id.toString() } });
  };
  return (
    <Stack spacing={1} sx={{ p: 2, width: 1, height: 1 }}>
      <Typography variant="h4">{disposeContractRouters.meta.list.useTitle()}</Typography>
      <Paper sx={{ display: 'flex', width: 1, flexGrow: 1 }}>
        <DataGrid
          persistent="/dispose-contracts"
          columns={columns}
          rows={contracts}
          onRowClick={handleRowClick}
          disableRowSelectionOnClick
          slots={{ toolbar: Toolbar }}
          loading={isPending}
          sx={{
            // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
            '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
            '& .MuiTablePagination-select': { mr: 2 },
          }}
        />
      </Paper>
    </Stack>
  );
};
