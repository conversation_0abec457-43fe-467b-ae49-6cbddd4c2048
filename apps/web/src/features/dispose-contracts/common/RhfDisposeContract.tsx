import {
  Box,
  FormControl,
  FormControlLabel,
  Grid,
  InputAdornment,
  Switch,
  TextField,
  type Theme,
  ThemeProvider,
} from '@mui/material';

import { trpc } from '@/api';
import { RhfBicycleDataGrid } from '@/components/bicycles/react-hook-form/RhfBicycleDataGrid';
import { useAppContext } from '@/contexts/app-context';
import { BicycleContractStatusSchema } from 'common';
import type { Bicycles, DisposeContract } from 'lambda-api';
import { RhfDatePicker, RhfSingleSelect, RhfTextField } from 'mui-ex';
import React from 'react';
import type { Control, UseFormWatch } from 'react-hook-form';
import { z } from 'zod';

import {
  colorCol,
  conditionCol,
  createdAtCol,
  imagesCol,
  statusCol,
} from '@/components/bicycles/columns';
import { createUseColumns } from '@/hooks/useColumns';

export const DisposeContractStateSchema = z
  .object({
    dealerId: z
      .string()
      .nullable()
      .transform((v) => (v !== '' ? v : null)),
    date: z.coerce.date(),
    status: BicycleContractStatusSchema,
    cost: z.coerce.number(),
    bicycleIds: z.array(z.string()).min(1, { message: '1台以上選択してください' }),
  })
  .superRefine((arg, ctx) => {
    if (arg.status === 'done') {
      if (arg.dealerId === null || arg.dealerId === '') {
        ctx.addIssue({
          message: '購入者が未選択です',
          code: z.ZodIssueCode.custom,
          path: ['dealerId'],
        });
      }
      if (arg.date === null) {
        ctx.addIssue({
          message: '廃棄日が未入力です',
          code: z.ZodIssueCode.custom,
          path: ['date'],
        });
      }
      if (arg.cost === null || arg.cost === 0) {
        ctx.addIssue({
          message: '金額が未入力です',
          code: z.ZodIssueCode.custom,
          path: ['price'],
        });
      }
    }
  });

export type DisposeContractState = z.infer<typeof DisposeContractStateSchema>;

export const disposeContractToState = (
  contract: Omit<DisposeContract, 'id'>,
): DisposeContractState => {
  return {
    dealerId: contract.dealerId ?? '',
    date: new Date(contract.date),
    status: contract.status,
    cost: contract.cost,
    bicycleIds: contract.bicycles.map((b) => b.id),
  };
};

type Mode = 'create' | 'edit';
type CommonProps = { readOnly?: boolean; bicycles: Bicycles; mode?: Mode };
type Props<T extends DisposeContractState> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<DisposeContractState>;
  watch: UseFormWatch<DisposeContractState>;
};

const useColumns = createUseColumns([imagesCol, createdAtCol, statusCol, colorCol, conditionCol]);

const collapsedSize = 400;

export const RhfDisposeContract = <T extends DisposeContractState>(props: Props<T>) => {
  const { control, watch, readOnly, bicycles, mode } = props as unknown as InnerProps;
  const { labels } = useAppContext();
  const { data: dealers = [] } = trpc.dealers.list.useQuery({ dealTypes: ['dispose'] });
  const [filter, setFilter] = React.useState(false);

  const status = watch('status');
  const bicycleIds = watch('bicycleIds');
  const rows = filter ? bicycles.filter((b) => bicycleIds.includes(b.id)) : bicycles;
  const columns = useColumns(rows);
  const selectable = !filter && !readOnly;
  const options = BicycleContractStatusSchema.options
    .filter((o) => mode !== 'create' || o !== 'canceled')
    .map((value) => ({
      label: labels.contractStatus[value],
      value,
    }));
  return (
    <>
      <RhfSingleSelect
        control={control}
        name="dealerId"
        label="購入者"
        options={dealers.map((dealer) => ({ label: dealer.name, value: dealer.id }))}
        readOnly={readOnly}
      />
      <Grid container spacing={2}>
        <Grid size={{ xs: 12, sm: 6 }}>
          <RhfSingleSelect
            control={control}
            name="status"
            label={labels.business.contract}
            options={options}
            readOnly={readOnly}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <ThemeProvider<Theme>
            theme={(t) => ({
              ...t,
              components: { ...t.components, MuiButton: { defaultProps: { variant: 'text' } } },
            })}
          >
            <RhfDatePicker
              control={control}
              name="date"
              label={status === 'scheduled' ? '廃棄予定日' : '廃棄日'}
              clearable={status === 'scheduled'}
              readOnly={readOnly}
            />
          </ThemeProvider>
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <TextField
            label="台数"
            value={bicycleIds.length}
            slotProps={{
              input: {
                endAdornment: <InputAdornment position="end">台</InputAdornment>,
              },
              htmlInput: { readOnly: true, style: { textAlign: 'right' } },
            }}
            focused={readOnly}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <RhfTextField
            control={control}
            name="cost"
            number
            unit="円"
            label={labels.business.cost}
            readOnly={readOnly}
          />
        </Grid>
      </Grid>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxHeight: selectable ? collapsedSize : undefined,
        }}
      >
        <RhfBicycleDataGrid
          control={control}
          columns={columns}
          rows={rows}
          readOnly={!selectable}
          hideFooterSelectedRowCount
        />
      </Box>
      {!readOnly && (
        <FormControl fullWidth>
          <FormControlLabel
            label="選択した車両のみを表示する"
            control={
              <Switch
                onChange={(_, checked) => setFilter(checked)}
                checked={filter}
                disabled={bicycleIds.length === 0}
              />
            }
          />
        </FormControl>
      )}
    </>
  );
};
