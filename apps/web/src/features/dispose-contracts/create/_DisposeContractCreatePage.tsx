import { Alert, Paper, Stack } from '@mui/material';
import { enqueueSnackbar } from 'notistack';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { zodResolver } from '@hookform/resolvers/zod';
import { Add, Close } from '@mui/icons-material';
import { useNavigate } from '@tanstack/react-router';
import { useForm } from 'react-hook-form';

import { MainLayout } from '@/components/core/layout/MainLayout';
import { disposeContractRouters } from '@/router/routes/release/dispose-contracts';
import {
  type DisposeContractState,
  DisposeContractStateSchema,
  RhfDisposeContract,
} from '../common/RhfDisposeContract';

export const DisposeContractCreatePage = () => {
  const title = disposeContractRouters.meta.create.useTitle();
  const { control, handleSubmit, watch, formState } = useForm<DisposeContractState>({
    mode: 'onChange',
    defaultValues: {
      bicycleIds: [],
      dealerId: '',
      status: 'scheduled',
      cost: 0,
      date: new Date(),
    } as const satisfies DisposeContractState,
    resolver: zodResolver(DisposeContractStateSchema),
  });

  const { data: bicycles = [] } = trpc.bicycles.list.useQuery({ type: 'release' });
  const { error, mutate } = trpc.contracts.dispose.create.useMutation();

  const navigate = useNavigate();
  const submit = async (input: DisposeContractState) =>
    mutate(input, {
      onSuccess: () => {
        navigate({ to: '/dispose-contracts' });
        enqueueSnackbar(
          `新規の廃棄契約${input.status === 'scheduled' ? '(予定)' : ''}を作成しました`,
          {
            variant: 'success',
          },
        );
      },
    });
  return (
    <MainLayout title={title} scrollable>
      <Stack component="form" spacing={2} noValidate onSubmit={handleSubmit(submit)}>
        <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
          <RhfDisposeContract control={control} watch={watch} bicycles={bicycles} mode="create" />
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
        </Stack>
        <Stack alignItems="end" sx={{ mt: 2 }}>
          <TwoActionButtons
            primary={{
              type: 'submit',
              icon: Add,
              label: '新規作成',
              loading: formState.isSubmitting,
            }}
            secondary={{
              icon: Close,
              end: true,
              label: 'キャンセル',
              onClick: () => navigate({ to: '/dispose-contracts' }),
            }}
          />
        </Stack>
      </Stack>
    </MainLayout>
  );
};
