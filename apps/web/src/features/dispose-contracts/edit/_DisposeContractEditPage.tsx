import { trpc } from '@/api';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { useParams } from '@tanstack/react-router';
import { isNullish } from 'common';
import { DisposeContractEdit } from './DisposeContractEdit';

export const DisposeContractEditPage = () => {
  const { id } = useParams({ from: '/app/dispose-contracts/$id/edit' });
  const { data: contract } = trpc.contracts.dispose.get.useQuery({ id });
  const { data: bicycles } = trpc.bicycles.list.useQuery();

  if (isNullish(contract) || isNullish(bicycles)) return <LoadingSpinner />;

  return <DisposeContractEdit contract={contract} bicycles={bicycles} />;
};
