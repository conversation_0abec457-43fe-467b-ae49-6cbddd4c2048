import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Close, Delete, Edit } from '@mui/icons-material';
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Paper,
  Stack,
} from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { enqueueSnackbar } from 'notistack';
import React from 'react';
import { useForm } from 'react-hook-form';

import { useAppContext } from '@/contexts/app-context';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { disposeContractRouters } from '@/router/routes/release/dispose-contracts';
import { getQueryKey } from '@trpc/react-query';
import type { Bicycles, DisposeContract } from 'lambda-api';
import {
  type DisposeContractState,
  DisposeContractStateSchema,
  RhfDisposeContract,
  disposeContractToState,
} from '../common/RhfDisposeContract';

type Props = {
  contract: DisposeContract;
  bicycles: Bicycles;
};

export const DisposeContractEdit = ({ contract: { id, ...contract }, bicycles }: Props) => {
  const { labels } = useAppContext();
  const title = disposeContractRouters.meta.edit.useTitle();
  const { control, watch, handleSubmit } = useForm<DisposeContractState>({
    mode: 'onChange',
    defaultValues: disposeContractToState(contract),
    resolver: zodResolver(DisposeContractStateSchema),
  });

  const queryClient = useQueryClient();
  const onSuccess = async () => {
    const queryKeys = [
      getQueryKey(trpc.contracts.dispose.list),
      getQueryKey(trpc.contracts.dispose.get, { id }),
    ];
    await Promise.all(queryKeys.map((queryKey) => queryClient.invalidateQueries({ queryKey })));
    navigate({ to: '/dispose-contracts' });
  };
  const updateMutation = trpc.contracts.dispose.update.useMutation({ onSuccess });
  const cancelMutation = trpc.contracts.dispose.cancel.useMutation({ onSuccess });
  const navigate = useNavigate();
  const [cancelDialog, setCancelDialog] = React.useState(false);
  const submit = ({ status, ...input }: DisposeContractState) => {
    if (status !== 'canceled')
      return updateMutation.mutate(
        { id, status, ...input },
        { onSuccess: () => enqueueSnackbar('廃棄契約を変更しました', { variant: 'success' }) },
      );
    return setCancelDialog(true);
  };

  const submitCancel = () => {
    cancelMutation.mutate(
      { id },
      { onSuccess: () => enqueueSnackbar('廃棄契約をキャンセルしました', { variant: 'success' }) },
    );
  };

  return (
    <MainLayout scrollable title={title}>
      <>
        <Stack component="form" noValidate spacing={2} onSubmit={handleSubmit(submit)}>
          <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
            <RhfDisposeContract control={control} watch={watch} bicycles={bicycles} mode="edit" />
          </Stack>
          <Stack alignItems="end" sx={{ mt: 2 }}>
            <TwoActionButtons
              primary={{
                type: 'submit',
                icon: Edit,
                label: '保存',
              }}
              secondary={{
                icon: Close,
                label: 'キャンセル',
                onClick: () => navigate({ to: '/dispose-contracts/$id', params: { id } }),
                end: true,
              }}
            />
          </Stack>
        </Stack>
        <Dialog open={cancelDialog} onClose={() => setCancelDialog(false)}>
          <DialogTitle>{`${labels.et.dispose}${labels.business.contract}の${labels.action.cancel}`}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {`${labels.et.dispose}${labels.p.contractCannotCancel}`}
            </DialogContentText>
          </DialogContent>
          <Box component="form" noValidate onSubmit={handleSubmit(submitCancel)}>
            <DialogActions>
              <TwoActionButtons
                primary={{
                  type: 'submit',
                  icon: Delete,
                  label: `${labels.business.contract}を${labels.action.cancel}する`,
                }}
                secondary={{
                  icon: Close,
                  label: `${labels.business.contract}を${labels.action.cancel}しない`,
                  onClick: () => setCancelDialog(false),
                  end: true,
                }}
              />
            </DialogActions>
          </Box>
        </Dialog>
      </>
    </MainLayout>
  );
};
