import { Box, FormHelperText, Paper, Stack } from '@mui/material';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import { RouterButton } from '@/components/RouterButton';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { disposeContractRouters } from '@/router/routes/release/dispose-contracts';
import { Edit } from '@mui/icons-material';
import type { DisposeContract } from 'lambda-api';
import {
  type DisposeContractState,
  DisposeContractStateSchema,
  RhfDisposeContract,
  disposeContractToState,
} from '../common/RhfDisposeContract';

type Props = {
  contract: DisposeContract;
};

export const DisposeContractDetail = ({ contract }: Props) => {
  const title = disposeContractRouters.meta.details.useTitle();
  const { control, watch } = useForm<DisposeContractState>({
    mode: 'onChange',
    defaultValues: disposeContractToState(contract),
    resolver: zodResolver(DisposeContractStateSchema),
  });

  const { bicycles } = contract;

  return (
    <MainLayout scrollable title={title}>
      <>
        <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
          <RhfDisposeContract control={control} watch={watch} bicycles={bicycles} readOnly />
        </Stack>
        <Stack alignItems="end" sx={{ mt: 2 }}>
          <Box>
            <RouterButton
              to="/dispose-contracts/$id/edit"
              params={{ id: contract.id }}
              startIcon={<Edit />}
              disabled={contract.status === 'canceled'}
              fullWidth
            >
              編集
            </RouterButton>
            {contract.status === 'canceled' && (
              <FormHelperText>キャンセルされた契約は編集できません</FormHelperText>
            )}
          </Box>
        </Stack>
      </>
    </MainLayout>
  );
};
