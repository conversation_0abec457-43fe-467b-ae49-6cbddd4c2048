import { trpc } from '@/api';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { useParams } from '@tanstack/react-router';
import { isNullish } from 'common';
import { DisposeContractDetail } from './DisposeContractDetail';

export const DisposeContractDetailPage = () => {
  const { id } = useParams({ from: '/app/dispose-contracts/$id' });
  const { data: contract } = trpc.contracts.dispose.get.useQuery({ id });

  if (isNullish(contract)) return <LoadingSpinner />;

  return <DisposeContractDetail contract={contract} />;
};
