import { useParams } from '@tanstack/react-router';

import { trpc } from '@/api';

import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { inquiryRouters } from '@/router/routes/inquiry';
import InquiryEdit from './InquiryEdit';

export default function InquiryEditPage() {
  const { id } = useParams({ from: '/app/inquiries/$id/edit' });
  const { data: inquiry } = trpc.inquiries.get.useQuery({ id });

  const title = inquiryRouters.meta.edit.useTitle();

  const content = inquiry === undefined ? <LoadingSpinner /> : <InquiryEdit inquiry={inquiry} />;

  return (
    <MainLayout scrollable back={{ to: '/inquiries/$id', params: { id } }} title={title}>
      {content}
    </MainLayout>
  );
}
