import { trpcClient } from '@/api';
import { type Image, base64ToBlob, base64ToContentType, isLocalImage } from 'common';
import zip from 'just-zip-it';

export const uploadInquiryCommentImagesToS3 = async (
  inquiryId: string,
  commentId: string,
  images: Image[],
): Promise<{ key: string; description: string; sortOrder: number }[]> => {
  const localImages = images.filter(isLocalImage);
  const res = await trpcClient.presignedUrls.inquiry.comment.list.query({
    inquiryId,
    commentId,
    files: localImages.map(({ filename, base64 }) => ({
      contentType: base64ToContentType(base64),
      filename,
    })),
  });

  const uploaded = await Promise.all(
    zip(localImages, res).map(async ([{ id, base64, description }, { key, url, fields }]) => {
      const body = new FormData();
      for (const [key, value] of Object.entries(fields)) {
        body.append(key, value);
      }
      body.append('file', base64ToBlob(base64));
      const response = await fetch(url, { method: 'POST', body });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Upload failed: ${errorText}`);
      }
      return { id, key, description, base64 };
    }),
  );

  return images.map((image, sortOrder) => {
    if (isLocalImage(image)) {
      const found = uploaded.find((u) => u.base64 === image.base64);
      if (found === undefined) throw new Error('Upload failed');
      const { key, description } = found;
      return { key, description, sortOrder };
    }
    if ('key' in image) {
      return { key: image.key, description: image.description, sortOrder };
    }
    throw new Error('Image must have key for upload');
  });
};
