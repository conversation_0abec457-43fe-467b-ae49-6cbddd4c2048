import { ImageAvatar } from '@/components/ImageAvatar';
import { Thumbnails } from '@/components/images/Thumbnails';
import { useAppContext } from '@/contexts/app-context';
import { datetimeFormatter } from '@/funcs/date';
import { Comment, Delete, Edit, History, MoreVert } from '@mui/icons-material';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Typography,
} from '@mui/material';
import type { InquiryComment, InquiryComments } from 'lambda-api';
import { useState } from 'react';

const NoComments = () => (
  <Card>
    <CardContent>
      <Box display="flex" alignItems="center" gap={1} color="text.secondary">
        <Comment />
        <Typography variant="body2">まだコメントはありません</Typography>
      </Box>
    </CardContent>
  </Card>
);

const CommentActionMenu = ({
  onEdit,
  onDelete,
  onViewHistory,
  isEdited,
  isOwnComment,
}: {
  onEdit: () => void;
  onDelete: () => void;
  onViewHistory: () => void;
  isEdited: boolean;
  isOwnComment: boolean;
}) => {
  const { labels, user } = useAppContext();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const canViewHistory = user.role.features.includes('/inquiries/$id#view-comment-history');
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    onEdit();
    handleClose();
  };

  const handleDelete = () => {
    onDelete();
    handleClose();
  };

  const handleViewHistory = () => {
    onViewHistory();
    handleClose();
  };

  return (
    <>
      <IconButton size="small" onClick={handleClick}>
        <MoreVert />
      </IconButton>
      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        {isOwnComment && [
          <MenuItem key="edit" onClick={handleEdit}>
            <Edit sx={{ mr: 1 }} />
            {labels.action.edit}
          </MenuItem>,
          <MenuItem key="delete" onClick={handleDelete}>
            <Delete sx={{ mr: 1 }} />
            {labels.action.delete}
          </MenuItem>,
        ]}

        {isEdited && canViewHistory && (
          <MenuItem onClick={handleViewHistory}>
            <History sx={{ mr: 1 }} />
            履歴
          </MenuItem>
        )}
      </Menu>
    </>
  );
};

type Props = {
  comments: InquiryComments;
  onEditComment: (comment: InquiryComment) => void;
  onDeleteComment: (comment: InquiryComment) => void;
  onViewCommentHistory: (comment: InquiryComment) => void;
};

export default function InquiryCommentList({
  comments,
  onEditComment,
  onDeleteComment,
  onViewCommentHistory,
}: Props) {
  const { user, labels } = useAppContext();
  if (!comments || comments.length === 0) {
    return <NoComments />;
  }
  const canViewHistory = user.role.features.includes('/inquiries/$id#view-comment-history');

  return (
    <Stack spacing={2}>
      <Typography variant="h6" component="h3">
        {labels.action.comment} ({comments.length})
      </Typography>
      {comments.map((comment) => {
        const isOwnComment = comment.userId === user.id;
        const isEdited = comment.updatedAt.getTime() !== comment.createdAt.getTime();

        // 自分のコメントか、（編集済み+観覧可能）か　どちらかの条件を満たすと MoreVert ボタンを表示する
        const showMoreVertButton = (canViewHistory && isEdited) || isOwnComment;

        return (
          <Card key={comment.id} variant="outlined">
            <CardHeader
              avatar={<ImageAvatar alt={comment.user.name} />}
              title={<Typography variant="subtitle2">{comment.user.name}</Typography>}
              subheader={
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    {datetimeFormatter(new Date(comment.createdAt))}
                  </Typography>
                  {isEdited && (
                    <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                      {`(${labels.action.edit}${labels.action.completed})`}
                    </Typography>
                  )}
                </Box>
              }
              action={
                showMoreVertButton && (
                  <CommentActionMenu
                    onEdit={() => onEditComment(comment)}
                    onDelete={() => onDeleteComment(comment)}
                    onViewHistory={() => onViewCommentHistory(comment)}
                    isEdited={isEdited}
                    isOwnComment={isOwnComment}
                  />
                )
              }
            />
            <CardContent sx={{ pt: 0 }}>
              <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                {comment.body}
              </Typography>
              {comment.images && comment.images.length > 0 && (
                <Box mt={2}>
                  <Thumbnails images={comment.images} size="small" minCols={3} />
                </Box>
              )}
            </CardContent>
          </Card>
        );
      })}
    </Stack>
  );
}
