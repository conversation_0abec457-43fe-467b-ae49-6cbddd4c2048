import { trpc } from '@/api';
import { ImageAvatar } from '@/components/ImageAvatar';
import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { Thumbnails } from '@/components/images/Thumbnails';
import { useAppContext } from '@/contexts/app-context';
import { datetimeFormatter } from '@/funcs/date';
import { History } from '@mui/icons-material';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Dialog,
  DialogContent,
  DialogTitle,
  Stack,
  Typography,
} from '@mui/material';
import type { InquiryComment } from 'lambda-api';

type Props = {
  open: boolean;
  onClose: () => void;
  comment: InquiryComment;
};

export default function CommentHistories({ open, onClose, comment }: Props) {
  const { labels } = useAppContext();
  const { data: commentVersions, isLoading } = trpc.inquiries.comments.histories.useQuery(
    { commentId: comment.id },
    { enabled: open },
  );
  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <History />
        <Typography variant="h6" component="span">
          {`${labels.action.comment}${labels.system.history}`}
        </Typography>
      </DialogTitle>
      <DialogContent>
        {isLoading ? (
          <LoadingSpinner />
        ) : (
          <Stack spacing={2}>
            <Typography variant="body2" color="text.secondary">
              このコメントの編集履歴を表示しています。
            </Typography>

            {commentVersions?.map((version, index) => {
              const isLatestVersion = index === 0;
              return (
                <Card
                  key={version.id}
                  sx={{
                    opacity: isLatestVersion ? 1 : 0.8,
                    border: isLatestVersion ? 2 : 1,
                    borderColor: (t) => (isLatestVersion ? t.palette.primary.main : 'divider'),
                  }}
                >
                  <CardHeader
                    avatar={<ImageAvatar alt={version.user.name} />}
                    title={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="subtitle2">{version.user.name}</Typography>
                        {isLatestVersion && (
                          <Typography
                            variant="caption"
                            sx={{ fontWeight: 'bold', color: (t) => t.palette.primary.main }}
                          >
                            ({labels.system.latest})
                          </Typography>
                        )}
                      </Box>
                    }
                    subheader={
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          {labels.action.create}: {datetimeFormatter(new Date(version.createdAt))}
                        </Typography>
                      </Box>
                    }
                  />
                  <CardContent sx={{ pt: 0 }}>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                      {version.body}
                    </Typography>
                    {version.images && version.images.length > 0 && (
                      <Box mt={2}>
                        <Thumbnails images={version.images} size="small" minCols={3} />
                      </Box>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </Stack>
        )}
      </DialogContent>
    </Dialog>
  );
}
