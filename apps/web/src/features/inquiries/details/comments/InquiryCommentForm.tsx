import { trpc } from '@/api';
import { RhfMultiImageSelector } from '@/components/react-hook-form/image-selector/RhfMultiImageSelector';
import { useAppContext } from '@/contexts/app-context';
import { useErrorMessageCreator } from '@/hooks/useErrorMessageCreator';
import { zodResolver } from '@hookform/resolvers/zod';
import { Send } from '@mui/icons-material';
import { LoadingButton } from '@mui/lab';
import { Box, Card, CardContent, FormHelperText, Stack, Typography } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import { ImageSchema } from 'common';
import { RhfTextField } from 'mui-ex';
import { enqueueSnackbar } from 'notistack';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { uploadInquiryCommentImagesToS3 } from './funcs';

const CommentFormSchema = z
  .object({
    body: z
      .string()
      .min(1, 'コメントを入力してください')
      .max(1000, 'コメントは1000文字以内で入力してください'),
    images: z.array(ImageSchema),
  })
  .strict();

type CommentFormState = z.infer<typeof CommentFormSchema>;

type Props = {
  inquiryId: string;
};

export default function InquiryCommentForm({ inquiryId }: Props) {
  const { labels } = useAppContext();
  const queryClient = useQueryClient();
  const createErrorMessage = useErrorMessageCreator();

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { isSubmitting },
  } = useForm<CommentFormState>({
    resolver: zodResolver(CommentFormSchema),
    defaultValues: {
      body: '',
      images: [],
    },
  });

  const { mutateAsync } = trpc.inquiries.comments.create.useMutation({
    onSuccess: () => {
      const queryKey = getQueryKey(trpc.inquiries.comments.list, { inquiryId });
      queryClient.invalidateQueries({ queryKey });
      reset();
    },
    onError: (error) => {
      console.error(error);
      enqueueSnackbar(createErrorMessage(error.message), { variant: 'error' });
    },
  });

  const onSubmit = async (state: CommentFormState) => {
    const commentId = crypto.randomUUID();
    const uploadedImages = await uploadInquiryCommentImagesToS3(inquiryId, commentId, state.images);

    await mutateAsync({
      inquiryId,
      commentId,
      body: state.body,
      images: uploadedImages,
    });
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" component="h3" gutterBottom>
          {`${labels.action.comment}を${labels.action.append}`}
        </Typography>

        <Stack component="form" onSubmit={handleSubmit(onSubmit)} gap={2}>
          <RhfTextField
            control={control}
            name="body"
            label={labels.action.comment}
            multiline
            rows={4}
            disabled={isSubmitting}
          />

          <RhfMultiImageSelector
            control={control}
            watch={watch}
            imagesPropName="images"
            readOnly={isSubmitting}
            max={5}
            newButtonLabel="写真を追加"
            limitHelperText={
              <FormHelperText sx={{ textAlign: 'center' }}>写真は最大5枚です</FormHelperText>
            }
          />

          <Box display="flex" justifyContent="flex-end">
            <LoadingButton
              type="submit"
              variant="contained"
              startIcon={<Send />}
              loading={isSubmitting}
            >
              {`${labels.action.comment}を${labels.action.post}`}
            </LoadingButton>
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );
}
