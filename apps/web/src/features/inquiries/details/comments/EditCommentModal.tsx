import { trpc } from '@/api';
import { RhfMultiImageSelector } from '@/components/react-hook-form/image-selector/RhfMultiImageSelector';
import { useAppContext } from '@/contexts/app-context';
import { useErrorMessageCreator } from '@/hooks/useErrorMessageCreator';
import { zodResolver } from '@hookform/resolvers/zod';
import { Save } from '@mui/icons-material';
import { LoadingButton } from '@mui/lab';
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormHelperText,
  Stack,
} from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import { ImageSchema } from 'common';
import type { InquiryComment } from 'lambda-api';
import { RhfTextField } from 'mui-ex';
import { enqueueSnackbar } from 'notistack';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { uploadInquiryCommentImagesToS3 } from './funcs';

const EditCommentFormSchema = z
  .object({
    body: z
      .string()
      .min(1, 'コメントを入力してください')
      .max(1000, 'コメントは1000文字以内で入力してください'),
    images: z.array(ImageSchema),
  })
  .strict();

type EditCommentFormState = z.infer<typeof EditCommentFormSchema>;

type Props = {
  open: boolean;
  onClose: () => void;
  comment: InquiryComment;
  inquiryId: string;
};

export default function EditCommentModal({ open, onClose, comment, inquiryId }: Props) {
  const { labels } = useAppContext();
  const queryClient = useQueryClient();
  const createErrorMessage = useErrorMessageCreator();

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { isSubmitting },
  } = useForm<EditCommentFormState>({
    resolver: zodResolver(EditCommentFormSchema),
    defaultValues: {
      body: comment.body,
      images: comment.images.map((img) => ({
        id: img.id,
        key: img.key,
        description: img.description,
        sortOrder: img.sortOrder,
      })),
    },
  });

  const { mutateAsync } = trpc.inquiries.comments.update.useMutation({
    onSuccess: () => {
      const queryKey = getQueryKey(trpc.inquiries.comments.list, { inquiryId });
      queryClient.invalidateQueries({ queryKey });
      onClose();
    },
    onError: (error) => {
      console.error(error);
      enqueueSnackbar(createErrorMessage(error.message), { variant: 'error' });
    },
  });

  const onSubmit = async (state: EditCommentFormState) => {
    const uploadedImages = await uploadInquiryCommentImagesToS3(
      inquiryId,
      comment.id,
      state.images,
    );

    await mutateAsync({
      commentId: comment.id,
      body: state.body,
      images: uploadedImages,
    });
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>{`${labels.action.comment}を${labels.action.edit}`}</DialogTitle>
      <DialogContent>
        <Stack component="form" onSubmit={handleSubmit(onSubmit)} gap={2} sx={{ mt: 1 }}>
          <RhfTextField
            control={control}
            name="body"
            label={labels.action.comment}
            multiline
            rows={4}
            disabled={isSubmitting}
          />

          <RhfMultiImageSelector
            control={control}
            watch={watch}
            imagesPropName="images"
            readOnly={isSubmitting}
            max={5}
            newButtonLabel="写真を追加"
            limitHelperText={
              <FormHelperText sx={{ textAlign: 'center' }}>写真は最大5枚です</FormHelperText>
            }
          />
        </Stack>
      </DialogContent>

      <DialogActions>
        <Box display="flex" gap={1}>
          <LoadingButton variant="outlined" onClick={handleClose} disabled={isSubmitting}>
            {labels.action.cancel}
          </LoadingButton>
          <LoadingButton
            variant="contained"
            startIcon={<Save />}
            loading={isSubmitting}
            onClick={handleSubmit(onSubmit)}
          >
            {labels.action.update}
          </LoadingButton>
        </Box>
      </DialogActions>
    </Dialog>
  );
}
