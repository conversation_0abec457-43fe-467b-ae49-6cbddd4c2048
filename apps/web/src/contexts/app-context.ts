import React, { createContext } from 'react';

import { useQuery } from '@tanstack/react-query';

import { type Dict, getDict } from 'models';

import { trpc, trpcClient } from '@/api';
import { getQueryKey } from '@trpc/react-query';
import type { Tenant } from 'lambda-api';
import type { StrictOmit } from 'ts-essentials';

export type ContextTenant = StrictOmit<
  Tenant,
  | 'user'
  | 'map'
  | 'landmarks'
  | 'storages'
  | 'colors'
  | 'bicycleTypes'
  | 'styles'
  | 'conditions'
  | 'policeStations'
  | 'priceSuggestions'
  | 'searchSets'
  | 'numberPlateLocations'
>;

export type AppContextValue = {
  tenant: ContextTenant;
  user: Tenant['user'];
  map: Tenant['map'] & { defaultCenter: { lat: number; lng: number }; defaultZoom: number };
  labels: Dict;
  colors: Tenant['colors'];
  bicycleTypes: Tenant['bicycleTypes'];
  styles: Tenant['styles'];
  landmarks: Tenant['landmarks'];
  storages: Tenant['storages'];
  conditions: Tenant['conditions'];
  policeStations: Tenant['policeStations'];
  priceSuggestions: Tenant['priceSuggestions'];
  numberPlateLocations: Tenant['numberPlateLocations'];
  searchSets: Tenant['searchSets'];
};

const getFromStorage =
  <T>(storageKey: string) =>
  () => {
    const value = window.localStorage.getItem(storageKey);
    if (value !== null) return JSON.parse(value) as T;
    return null;
  };

const calcReturnValue = <T>(data: T | null | undefined, fromStorage: T | null): T | undefined => {
  // データが取得できた場合は取得した最新データを使用
  if (data) return data;
  // データ取得中の場合は undefined
  if (data === undefined) return undefined;
  // ストレージにデータがある場合はストレージのデータを使用
  if (fromStorage) return fromStorage;
  throw new Error('データの取得に失敗しました');
};
export const tenantQueryKey = getQueryKey(trpc.tenant.get);
const useTenant = () => {
  const queryKey = tenantQueryKey;
  const storageKey = JSON.stringify(queryKey);
  const { data } = useQuery({
    queryKey,
    queryFn: async (): Promise<Tenant> => {
      const res = await trpcClient.tenant.get.query();
      window.localStorage.setItem(storageKey, JSON.stringify(res));
      return res as Tenant;
    },
  });
  const fromStorage = React.useMemo(getFromStorage<Tenant>(storageKey), []);
  return calcReturnValue(data, fromStorage);
};

export const useAppContextValue = (): AppContextValue | undefined => {
  const tenant = useTenant();
  if (tenant === undefined) return undefined;
  const {
    user,
    dict,
    bicycleTypes,
    styles,
    landmarks,
    storages,
    colors,
    conditions,
    policeStations,
    priceSuggestions,
    numberPlateLocations,
    searchSets,
  } = tenant;
  const map = {
    ...tenant.map,
    defaultCenter: { lat: tenant.map.defaultLatitude, lng: tenant.map.defaultLongitude },
    defaultZoom: 15,
  };
  const labels = getDict(dict);
  return {
    tenant,
    user,
    map,
    labels,
    bicycleTypes,
    styles,
    landmarks,
    storages,
    colors,
    conditions,
    policeStations,
    priceSuggestions,
    numberPlateLocations,
    searchSets,
  };
};

export const AppContext = createContext<AppContextValue>(
  // デフォルトは使用するケースがないため undefined としている
  undefined as unknown as AppContextValue,
);

export const useAppContext = () => React.useContext(AppContext);
