import React, { createContext } from 'react';

import { LoadingSpinner } from '@/components/core/LoadingSpinner';
import { useQuery } from '@tanstack/react-query';

type DevicesContextValue = {
  devices: MediaDeviceInfo[];
};

const useDevicesContextValue = (): DevicesContextValue | undefined => {
  const { data: devices } = useQuery({
    queryKey: ['devices'],
    queryFn: async () => {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      for (const track of stream.getVideoTracks()) track.stop();
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(({ kind }) => kind === 'videoinput');
    },
  });
  if (devices === undefined) return undefined;
  return { devices };
};

const DevicesContext = createContext<DevicesContextValue>(
  undefined as unknown as DevicesContextValue,
);

export const useDevicesContext = () => React.useContext(DevicesContext);

export const DevicesProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const context = useDevicesContextValue();
  if (context === undefined)
    return <LoadingSpinner height="100dvh" message="カメラの情報を取得中..." />;
  return <DevicesContext.Provider value={context}>{children}</DevicesContext.Provider>;
};
