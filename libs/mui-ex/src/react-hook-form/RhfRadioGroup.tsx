import {
  FormControl,
  FormControlLabel,
  type FormControlLabelProps,
  type FormControlProps,
  FormHelperText,
  FormLabel,
  Radio,
  RadioGroup,
  type RadioGroupProps,
} from '@mui/material';
import { type FieldValues, type UseControllerProps, useController } from 'react-hook-form';
import { match } from 'ts-pattern';

type Props<T extends FieldValues> = UseControllerProps<T> & {
  mui?: {
    formControl?: FormControlProps;
    radioGroup?: Omit<RadioGroupProps, 'row'>;
    formControlLabel?: Omit<FormControlLabelProps, 'disabled'>;
  };
  label?: string;
  options: { label: string; value: string }[];
  row?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
};

export const RhfRadioGroup = <T extends FieldValues>(props: Props<T>) => {
  const { name, control, mui = {}, label, row, options, readOnly = false } = props;
  const {
    field,
    fieldState: { error },
  } = useController({ name, control });

  const cursor: NonNullable<React.CSSProperties['cursor']> = readOnly ? 'not-allowed' : 'pointer';
  return (
    <FormControl {...mui.formControl} error={Boolean(error)}>
      {label && <FormLabel>{label}</FormLabel>}
      <RadioGroup {...mui.radioGroup} {...field} row={row}>
        {options.map(({ label, value }) => {
          const checked = field.value === value;
          const disabled = match({ disabled: props.disabled, readOnly })
            .with({ disabled: true }, () => true)
            .with({ readOnly: false }, () => false)
            .with({ readOnly: true }, () => !checked)
            .exhaustive();
          return (
            <FormControlLabel
              key={`${label}:${value}`}
              {...mui.formControlLabel}
              disabled={disabled}
              label={label}
              value={value}
              control={<Radio sx={{ pointerEvents: readOnly ? 'none' : undefined }} />}
              sx={{ cursor }}
            />
          );
        })}
      </RadioGroup>
      <FormHelperText error sx={{ mx: 0 }}>
        {error?.message}
      </FormHelperText>
    </FormControl>
  );
};
