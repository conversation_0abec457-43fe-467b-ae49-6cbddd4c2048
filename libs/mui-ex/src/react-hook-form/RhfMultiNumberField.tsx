import { Autocomplete, Chip, TextField } from '@mui/material';
import type React from 'react';
import { type FieldValues, type UseControllerProps, useController } from 'react-hook-form';

type Props<T extends FieldValues> = UseControllerProps<T> & {
  label?: string;
  helperText?: string;
  disabled?: boolean;
  readOnly?: boolean;
  placeholder?: string;
  max?: number; // 最大入力可能数
  minValue?: number; // 数値の最小値
  maxValue?: number; // 数値の最大値
};

export const RhfMultiNumberField = <T extends FieldValues>(props: Props<T>) => {
  const {
    control,
    name,
    label,
    helperText,
    disabled,
    readOnly,
    placeholder,
    max,
    minValue,
    maxValue,
  } = props;

  const {
    field,
    fieldState: { error },
  } = useController({ name, control });

  if (!Array.isArray(field.value))
    throw new Error(`Field value for ${name} must be an array of numbers.`);
  // number[] として管理している値をstring[]に変換してAutocompleteに渡す
  const displayValues = field.value.map((num: number) => String(num));

  const handleChange = (_: React.SyntheticEvent, value: string[]) => {
    // string[]をnumber[]に変換、無効な値はフィルタリング
    const numberValues = value
      .map((v) => Number(v.trim()))
      .filter((num) => {
        // 基本的な数値チェック
        if (Number.isNaN(num) || !Number.isFinite(num)) return false;
        // 範囲チェック
        if (minValue !== undefined && num < minValue) return false;
        if (maxValue !== undefined && num > maxValue) return false;
        return true;
      });

    // 最大数制限
    const finalValues = max !== undefined ? numberValues.slice(0, max) : numberValues;
    field.onChange(finalValues);
  };

  return (
    <Autocomplete
      multiple
      freeSolo
      options={[]}
      value={displayValues}
      onChange={handleChange}
      readOnly={readOnly}
      fullWidth
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          placeholder={placeholder}
          error={Boolean(error)}
          helperText={helperText}
          disabled={disabled}
          focused={readOnly}
        />
      )}
      renderValue={(value: readonly string[], getItemProps) =>
        value.map((option: string, index: number) => {
          const { key, ...itemProps } = getItemProps({ index });
          return <Chip key={key} label={option} variant="outlined" size="small" {...itemProps} />;
        })
      }
    />
  );
};
