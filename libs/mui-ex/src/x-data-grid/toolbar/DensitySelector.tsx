import React from 'react';

import { type SvgIconComponent, TableRows, ViewHeadline, ViewStream } from '@mui/icons-material';
import { ListItemIcon, ListItemText, Menu, MenuItem } from '@mui/material';
import Button from '@mui/material/Button';
import { type GridDensity, useGridApiContext } from '@mui/x-data-grid';

import { capitalize } from '../../funcs';

const densities: { density: GridDensity; Icon: SvgIconComponent }[] = [
  { density: 'compact', Icon: ViewHeadline },
  { density: 'standard', Icon: TableRows },
  { density: 'comfortable', Icon: ViewStream },
];

type Props = {
  type: 'button' | 'menu-item';
  onChangeDensity?: (density: GridDensity) => void;
};

export const ToolbarDensitySelector = ({ type, onChangeDensity }: Props) => {
  const api = useGridApiContext();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClickButton = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClickMenuItem = (event: React.MouseEvent<HTMLLIElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleSelect = (density: GridDensity) => () => {
    if (onChangeDensity) onChangeDensity(density);
    setAnchorEl(null);
    api.current.setDensity(density);
  };

  const Element =
    type === 'button' ? (
      <Button
        id="density-selector-button"
        aria-controls={open ? 'density-selector-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        startIcon={<TableRows />}
        onClick={handleClickButton}
      >
        {api.current.getLocaleText('toolbarDensityLabel')}
      </Button>
    ) : (
      <MenuItem
        id="density-selector-button"
        aria-controls={open ? 'density-selector-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClickMenuItem}
      >
        <ListItemIcon>
          <TableRows />
        </ListItemIcon>
        <ListItemText>{api.current.getLocaleText('toolbarDensityLabel')}</ListItemText>
      </MenuItem>
    );
  return (
    <>
      {Element}
      <Menu
        id="density-selector-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'density-selector-button',
        }}
      >
        {densities.map(({ density, Icon }) => (
          <MenuItem
            key={density}
            onClick={handleSelect(density)}
            selected={api.current.state.density === density}
          >
            <ListItemIcon>
              <Icon />
            </ListItemIcon>
            <ListItemText>
              {api.current.getLocaleText(`toolbarDensity${capitalize(density)}`)}
            </ListItemText>
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};
