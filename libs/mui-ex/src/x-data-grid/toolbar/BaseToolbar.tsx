import { useState } from 'react';

import { MoreVert } from '@mui/icons-material';
import { Box, IconButton, Menu, type Theme, useMediaQuery } from '@mui/material';
import { Toolbar } from '@mui/x-data-grid';

import { ToolbarColumnsButton, ToolbarDensitySelector, ToolbarFilterButton } from 'mui-ex';

type Props = {
  primaryActions?: React.ReactNode;
  secondaryActions?: React.ReactNode;
};

export const BaseToolbar = ({ primaryActions, secondaryActions }: Props) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleOpen = (event: React.MouseEvent<HTMLButtonElement>) =>
    setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);

  const mdDown = useMediaQuery<Theme>((theme) => theme.breakpoints.down('md'));

  if (mdDown) {
    return (
      <Toolbar>
        <>
          {primaryActions}
          <IconButton onClick={handleOpen} sx={{ ml: 'auto' }}>
            {/* TODO: フィルターされているときだけバッジを表示 */}
            {/* <Badge variant="dot" color="secondary">
            <MoreVert />
          </Badge> */}
            <MoreVert />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
          >
            <>
              {secondaryActions}
              <ToolbarFilterButton type="menu-item" />
              <ToolbarColumnsButton type="menu-item" />
              <ToolbarDensitySelector type="menu-item" />
            </>
          </Menu>
        </>
      </Toolbar>
    );
  }
  return (
    <Toolbar>
      <>
        {primaryActions}
        <Box sx={{ flexGrow: 1 }} />
        {secondaryActions}
        <ToolbarColumnsButton type="button" />
        <ToolbarDensitySelector type="button" />
        <ToolbarFilterButton type="button" />
      </>
    </Toolbar>
  );
};
