import { FilterList } from '@mui/icons-material';
import { Badge, ListItemIcon, ListItemText, MenuItem } from '@mui/material';
import Button from '@mui/material/Button';

import { useFilterModelContext } from '../../grid-filter';
import { isActiveFilterItem } from '../../grid-filter/filter';
import { getLocaleText } from '../../locale-text';

type Props = {
  type: 'button' | 'menu-item';
  onClick?: () => void;
};

export const FilterButton = ({ type, onClick }: Props) => {
  const { filterModel } = useFilterModelContext();
  const handleClick = () => onClick?.();
  const count = filterModel.items.filter(isActiveFilterItem).length;
  const StartIcon = (
    <Badge badgeContent={count} color="secondary">
      <FilterList />
    </Badge>
  );
  if (type === 'button')
    return (
      <Button startIcon={StartIcon} onClick={handleClick}>
        {getLocaleText('toolbarFiltersLabel')}
      </Button>
    );
  return (
    <MenuItem onClick={handleClick}>
      <ListItemIcon>{StartIcon}</ListItemIcon>
      <ListItemText>{getLocaleText('toolbarFiltersLabel')}</ListItemText>
    </MenuItem>
  );
};
