import { ViewColumn } from '@mui/icons-material';
import { Button, ListItemIcon, ListItemText, MenuItem } from '@mui/material';
import { GridPreferencePanelsValue, useGridApiContext } from '@mui/x-data-grid';

type Props = {
  type: 'button' | 'menu-item';
  onClick?: () => void;
};

export const ToolbarColumnsButton = ({ type, onClick }: Props) => {
  const api = useGridApiContext();
  const handleClick = () => {
    if (onClick) onClick();
    api.current.showPreferences(GridPreferencePanelsValue.columns);
  };
  const Icon = <ViewColumn />;
  const localeText = api.current.getLocaleText('toolbarColumns');
  if (type === 'button')
    return (
      <Button startIcon={Icon} onClick={handleClick}>
        {localeText}
      </Button>
    );

  return (
    <MenuItem onClick={handleClick}>
      <ListItemIcon>{Icon}</ListItemIcon>
      <ListItemText>{localeText}</ListItemText>
    </MenuItem>
  );
};
