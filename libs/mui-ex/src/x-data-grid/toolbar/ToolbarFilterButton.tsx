import { useGridApiContext } from '@mui/x-data-grid';
import { FilterButton } from './FilterButton';

type Props = {
  type: 'button' | 'menu-item';
  onClick?: () => void;
};

export const ToolbarFilterButton = ({ type, onClick }: Props) => {
  const api = useGridApiContext();
  const handleClick = () => {
    onClick?.();
    api.current.showFilterPanel();
  };
  return <FilterButton type={type} onClick={handleClick} />;
};
