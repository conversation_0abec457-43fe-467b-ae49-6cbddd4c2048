# 【富士通】放置自転車管理システム ER図

## 概要

品川区の放置自転車管理システムのデータベース構造を示すER図です。
主要なエンティティとその関係を表現しています。

## ER図

```mermaid
erDiagram
    %% 地理・行政情報
    TM_都道府県 {
        string 都道府県コード PK
        string 都道府県名
        string かな
        int 優先順位
        string ザウルス区分
    }

    TM_市区町村 {
        string 都道府県コード FK
        string 市区町村コード PK
        string 市区町村名
        string かな
        int 優先順位
        string 陸運局番号
    }

    TM_町 {
        string 都道府県コード FK
        string 市区町村コード FK
        string 町コード PK
        string 町名
        string 郵便番号
        string かな
        int 優先順位
        string 住所
        string 住所カナ
    }

    TM_行政区 {
        string 行政区コード PK
        string 行政区名
        string 略称
        string 連絡先
        string 郵便番号
        string 住所
        string 電話
        string 持込先
    }

    %% 施設・場所情報
    TM_保管所 {
        string 保管所コード PK
        string 保管所名
        int 収容台数
        string 略称
        string 住所
        string 電話
        string 曜日1
        string 曜日2
        string 曜日3
        string 曜日4
        string 曜日5
        string 曜日6
        string 曜日7
        string 行政区コード FK
        string 都道府県コード FK
        string 所轄コード FK
    }

    TM_駅 {
        string 駅コード PK
        string 保管所コード FK
        string 保管所コード2
        string 駅名
        string 路線コード FK
        int 優先順位
        string タブレット区分
        string 市区町村コード FK
        string 町コード FK
        string 町
        string 街区番号
    }

    TM_撤去場所 {
        string 駅コード FK
        string 撤去場所コード PK
        string 撤去場所名
        string 市区町村コード FK
        string 町コード FK
        string 町
        string 街区番号
    }

    TM_駐輪場 {
        string 駐輪場コード PK
        string 駐輪場名
        string 駅コード FK
        string 連携用駐輪場名
    }

    TM_鉄道路線名 {
        string 路線コード PK
        string 路線名
    }

    %% 車両情報
    TM_車輌区分 {
        string 車輌区分 PK
        string 内容
    }

    TM_色 {
        string 色コード PK
        string 色名
    }

    TM_メーカ {
        string メーカコード PK
        string メーカ名
    }

    TM_メーカマスタ {
        string メーカコード PK
        string メーカ名
    }

    TM_車種 {
        string 車種コード PK
        string 車種名
    }

    TM_車名 {
        string 車名コード PK
        string 車名
    }

    %% システム・運用情報
    TM_利用者 {
        string 利用者コード PK
        string パスワード
        string 利用者氏名
        date 最終使用日
        string 管理者
        string 所属
    }

    TM_状態 {
        string 状態コード PK
        string 状態
    }

    TM_状態2 {
        string 状態コード PK
        string 状態
        string 車輌区分 FK
    }

    TM_処分 {
        string 処分コード PK
        string 内容
        string 区分
    }

    TM_処分詳細 {
        string 処分コード FK
        string 詳細コード PK
        string 内容
    }

    TM_返還 {
        string 返還コード PK
        string 有料
        string 内容
        string 統計区分
    }

    TM_再生詳細 {
        string 詳細コード PK
        string タイヤの大きさ
        string スタンドの形態
        string 変速機の有無
        string 搬入先
    }

    %% 告示・料金管理
    TM_告示日 {
        date 撤去開始日
        date 撤去終了日
        date 保管告示日
        date 処分告示日
        date 保管期限
    }

    TM_撤去料 {
        string ID PK
        date 開始日
        date 終了日
        string 車輌区分 FK
        int 撤去料
    }

    TM_祝日 {
        date 祝日 PK
    }

    %% 防犯登録・警察関連
    TM_防犯登録 {
        string 車輌区分 FK
        string 都道府県コード FK
        string 所轄コード PK
        string 所轄名
        string かな
        string 英数字
        int 優先順位
        string 送付先所轄コード
        string リサイクル区分
    }

    TM_防犯登録_連携 {
        string 都道府県コード FK
        string 連携所轄コード
        string 連携所轄名
        string 所轄コード FK
        string 連携
    }

    TM_警察住所 {
        string 都道府県コード FK
        string 所轄コード PK
        string 郵便番号
        string 住所
        string 警察名
        string 担当課
        string 担当名
        string 送付者コード FK
    }

    %% 送付・文書管理
    TM_送付者 {
        string 送付者コード PK
        string 車輌区分 FK
        string 送付者略称
        string 発信11
        string 発信12
        string 発信13
        string 発信14
        string 連絡11
        string 連絡12
        string 連絡13
        string 連絡14
        string 連絡15
        string イメージファイル
        int 横位置
        int 縦位置
        int 横幅
        int 縦高さ
    }

    TM_文書 {
        string 文書コード PK
        string 文書種別名
        string 本文1
        string 本文2
        string 本文3
        string 本文4
        string 本文5
        string 本文6
        string 本文7
        string 本文8
        string 本文9
        string 本文10
        int 上限行数
    }

    TM_市区町村住所 {
        string 都道府県コード FK
        string 市区町村コード FK
        string 市区町村名
        string かな
        string 郵便番号
        string 住所
        string 電話番号
        string 担当課
        string 担当名
        string 送付者コード FK
    }

    TM_陸運局住所 {
        string 陸運局番号 PK
        string 名称
        string 郵便番号
        string 住所
        string 担当課
        string 担当名
        string 送付者コード FK
    }

    %% その他マスタ
    TM_本人確認区分 {
        string 本人確認区分 PK
        string 本人確認区分名
    }

    TM_ひらがな変換表 {
        string 変換元 PK
        string 変換先
    }

    TM_自転車商 {
        string 詳細コード PK
        string 支部
        string 組合員名
    }

    TM_駐輪場シート {
        string シートNo PK
        string シート名
    }

    TM_駐輪場連携 {
        string 番号 PK
        string 承認置場
        string 区画
        string 氏名
        string 住所１
        string 住所２
        string 郵便番号
        string 申請年度
        string 駐輪場コード FK
    }

    %% ログ・システム管理
    TM_ACCESS_LOG {
        string ID PK
        string 利用者コード FK
        datetime 接続時刻
        string 結果
    }

    TM_ログ {
        string ID PK
        string 利用者コード FK
        date 使用日
        time 開始時間
        time 終了時間
    }

    TM_初期値 {
        string ID PK
        string 保管所コード FK
        string 駅コード FK
        string 都道府県コード FK
        string 市区町村コード FK
        string AMPM
        string 撤去時間
        string ザウルス
        string 所轄コード FK
        string ナンバー
        string 自転車必須入力
        string 原付必須入力
        string 車体番号不明
        string 防犯登録不明
        string ナンバー不明
        string 防犯登録パレット1
        string 防犯登録パレット2
        string 鍵必須入力
        string 自転車領収番号
        string 原付領収番号
        string 整理番号形態
        string 整理番号保持
        string 連番桁数
        string 撤去時間表示
        string 利用者マスタ格納場所
        string 市区町村マスタ格納場所
        string 町マスタ格納場所
        string データ連動方法
        string データ送信ボタン
        string 業務ボタン
        string 電源切断ボタン
        string 縦横切替ボタン
        string 前画面ボタン
        string 次画面ボタン
        string 駐輪場日付区分
        string 撤去場所パレット
        string 置場入力制限
        string 本体ボタン使用制限
        string データ連携方法
        string カメラ機能有無
        string 修正機能使用有無
        string ログイン画面有無
        string 検索結果表示
        string サーバ転送機能有無
        string サーバユーザ名
        string パスワード
        string 転送先フォルダパス
        string 返還申請書自動出力
    }

    TM_運用 {
        string ID PK
        string 保管所コード FK
        string ザウルス
        string 所轄都道府県コード FK
        string 所轄コード FK
        string 利用者マスタ格納場所
        string マスタ格納場所
        string 行政区コード FK
        string 発信番号１
        string サーバ判定
    }

    TM_変数 {
        string ID PK
        string 部名
        string 課名
        string 職名
        string 区長名
        string 連絡先
        string 連絡先電話番号
        string 発信番号１
        string 発信番号２
        string 発信番号３
        string 郵便番号
        string 住所
        string 役所名
        string 担当名
        string 発信11
        string 発信12
        string 発信13
        string 発信14
        string 発信21
        string 発信22
        string 発信23
        string 発信24
        string 連絡11
        string 連絡12
        string 連絡13
        string 連絡14
        string 連絡15
        string 連絡21
        string 連絡22
        string 連絡23
        string 連絡24
        string 連絡25
    }

    TM_納付書 {
        string 行政区コード FK
        string 保管所コード FK
        string 会計
        string 会計名
        string 記帳区分
        string 款
        string 項
        string 目
        string 節1
        string 節2
        string 科目名1
        string 科目名2
        string ID
        string 番号
        string 収入区分
        string 予算種別
        string 納入者1
        string 納入者2
        string 保管所名
        string 部名
        string 部コード
        string 大事項
        string 中事項
        string 納人住所
        string 納人名称1
        string 納人名称2
        string 納人氏名
    }

    TM_イメージ {
        string 保管所コード FK
        string イメージファイル PK
    }

    TM_ナンバー {
        string 都道府県コード FK
        string 市区町村コード FK
        string 市区町村名
        string かな
        int 優先順位
        string 送付先市区町村コード
    }

    TM_パスワード1 {
        string 利用者コード PK
        string パスワード
        string 利用者氏名
        date 最終使用日
        string 管理者
        string 所属
    }

    %% リレーション
    TM_都道府県 ||--o{ TM_市区町村 : "都道府県コード"
    TM_市区町村 ||--o{ TM_町 : "市区町村コード"
    TM_市区町村 ||--o{ TM_駅 : "市区町村コード"
    TM_市区町村 ||--o{ TM_撤去場所 : "市区町村コード"
    TM_市区町村 ||--o{ TM_市区町村住所 : "市区町村コード"
    TM_市区町村 ||--o{ TM_ナンバー : "市区町村コード"
    TM_市区町村 ||--o{ TM_初期値 : "市区町村コード"

    TM_町 ||--o{ TM_駅 : "町コード"
    TM_町 ||--o{ TM_撤去場所 : "町コード"

    TM_行政区 ||--o{ TM_保管所 : "行政区コード"
    TM_行政区 ||--o{ TM_運用 : "行政区コード"
    TM_行政区 ||--o{ TM_納付書 : "行政区コード"

    TM_保管所 ||--o{ TM_駅 : "保管所コード"
    TM_保管所 ||--o{ TM_初期値 : "保管所コード"
    TM_保管所 ||--o{ TM_運用 : "保管所コード"
    TM_保管所 ||--o{ TM_納付書 : "保管所コード"
    TM_保管所 ||--o{ TM_イメージ : "保管所コード"

    TM_駅 ||--o{ TM_撤去場所 : "駅コード"
    TM_駅 ||--o{ TM_駐輪場 : "駅コード"
    TM_駅 ||--o{ TM_初期値 : "駅コード"

    TM_鉄道路線名 ||--o{ TM_駅 : "路線コード"

    TM_車輌区分 ||--o{ TM_撤去料 : "車輌区分"
    TM_車輌区分 ||--o{ TM_防犯登録 : "車輌区分"
    TM_車輌区分 ||--o{ TM_送付者 : "車輌区分"
    TM_車輌区分 ||--o{ TM_状態2 : "車輌区分"

    TM_利用者 ||--o{ TM_ACCESS_LOG : "利用者コード"
    TM_利用者 ||--o{ TM_ログ : "利用者コード"

    TM_処分 ||--o{ TM_処分詳細 : "処分コード"

    TM_都道府県 ||--o{ TM_防犯登録 : "都道府県コード"
    TM_都道府県 ||--o{ TM_警察住所 : "都道府県コード"
    TM_都道府県 ||--o{ TM_防犯登録_連携 : "都道府県コード"
    TM_都道府県 ||--o{ TM_初期値 : "都道府県コード"

    TM_防犯登録 ||--o{ TM_防犯登録_連携 : "所轄コード"
    TM_防犯登録 ||--o{ TM_警察住所 : "所轄コード"
    TM_防犯登録 ||--o{ TM_保管所 : "所轄コード"
    TM_防犯登録 ||--o{ TM_初期値 : "所轄コード"
    TM_防犯登録 ||--o{ TM_運用 : "所轄コード"

    TM_送付者 ||--o{ TM_警察住所 : "送付者コード"
    TM_送付者 ||--o{ TM_市区町村住所 : "送付者コード"
    TM_送付者 ||--o{ TM_陸運局住所 : "送付者コード"

    TM_駐輪場 ||--o{ TM_駐輪場連携 : "駐輪場コード"

    TM_パスワード1 ||--|| TM_利用者 : "利用者コード"
```

## 主要なエンティティ説明

### 地理・行政系
- **TM_都道府県**: 都道府県マスタ
- **TM_市区町村**: 市区町村マスタ
- **TM_町**: 町名マスタ
- **TM_行政区**: 行政区マスタ

### 施設・場所系
- **TM_保管所**: 保管所マスタ
- **TM_駅**: 駅マスタ
- **TM_撤去場所**: 撤去場所マスタ
- **TM_駐輪場**: 駐輪場マスタ

### 車両系
- **TM_車輌区分**: 自転車・原付などの区分
- **TM_色**: 車両の色マスタ
- **TM_メーカ**: メーカマスタ
- **TM_車種**: 車種マスタ
- **TM_車名**: 車名マスタ

### システム・運用系
- **TM_初期値**: システム初期値設定
- **TM_運用**: 運用設定
- **TM_利用者**: システム利用者
- **TM_ACCESS_LOG**: アクセスログ
- **TM_ログ**: 使用ログ

### 業務系
- **TM_処分**: 処分マスタ
- **TM_返還**: 返還マスタ
- **TM_状態**: 状態マスタ
- **TM_告示日**: 告示日管理
- **TM_撤去料**: 撤去料金管理

### 防犯・警察系
- **TM_防犯登録**: 防犯登録マスタ
- **TM_警察住所**: 警察住所マスタ

### 送付・文書系
- **TM_送付者**: 送付者マスタ
- **TM_文書**: 文書マスタ
- **TM_市区町村住所**: 市区町村住所マスタ
- **TM_陸運局住所**: 陸運局住所マスタ

## 独立したテーブル

他のテーブルとの関係性が少ない、もしくは自己完結している性質を持つテーブル群です。

### 基本マスタ系
- **TM_色**: 車両の色マスタ（独立した値セット）
- **TM_車種**: 車種マスタ（独立した値セット）
- **TM_車名**: 車名マスタ（独立した値セット）
- **TM_本人確認区分**: 本人確認区分マスタ（独立した値セット）

### 変換・参照系
- **TM_ひらがな変換表**: 文字変換用テーブル（独立した変換ルール）
- **TM_祝日**: 祝日マスタ（独立した日付セット）
- **TM_文書**: 文書テンプレートマスタ（独立したテンプレート）

### 業務マスタ系
- **TM_処分**: 処分マスタ（独立した処分種別）
- **TM_返還**: 返還マスタ（独立した返還種別）
- **TM_状態**: 状態マスタ（独立した状態種別）
- **TM_再生詳細**: 再生詳細マスタ（独立した詳細分類）

### 特殊マスタ系
- **TM_自転車商**: 自転車商マスタ（独立した事業者情報）
- **TM_駐輪場シート**: 駐輪場シート管理（独立したシート管理）

### システム固有設定
- **TM_変数**: システム変数（独立した設定値群）
- **TM_イメージ**: イメージファイル管理（独立したファイル管理）

## エンティティの詳細

### 地理・行政系

#### **TM_都道府県**: 都道府県マスタ
- **概要**: 全国の都道府県の基本情報を管理するマスタテーブル。都道府県コード、名称、かな表記、優先順位、ザウルス区分を含む。
- **関係エンティティ**: TM_市区町村、TM_防犯登録、TM_警察住所、TM_防犯登録_連携、TM_初期値との関係を持つ。地理的階層の最上位として機能。
- **データ移行についての判断**: **移行不要**。現在のシステムでは`Prefecture`というenumで都道府県を管理しており、コード体系が異なるため移行不要。

#### **TM_市区町村**: 市区町村マスタ
- **概要**: 都道府県配下の市区町村の基本情報を管理。市区町村コード、名称、かな表記、優先順位、陸運局番号を含む。
- **関係エンティティ**: TM_都道府県の子として、TM_町、TM_駅、TM_撤去場所、TM_市区町村住所、TM_ナンバー、TM_初期値との関係を持つ。
- **データ移行についての判断**: **移行不要**。現在のシステムでは`PostalData`で市区町村情報を管理している。また陸運局番号は意味を持たない。移行不要。

#### **TM_町**: 町名マスタ
- **概要**: 市区町村配下の町名の詳細情報を管理。町コード、町名、郵便番号、かな表記、住所情報を含む。
- **関係エンティティ**: TM_市区町村の子として、TM_駅、TM_撤去場所との関係を持つ。住所階層の最下位として機能。
- **データ移行についての判断**: **移行不要**。現在のシステムでは`PostalData`で町域情報を管理しているため移行不要。

#### **TM_行政区**: 行政区マスタ
- **概要**: 行政区の基本情報と連絡先を管理。行政区コード、名称、略称、連絡先、住所、電話番号、持込先を含む。
- **関係エンティティ**: TM_保管所、TM_運用、TM_納付書との関係を持つ。業務運用の基本単位として機能。
- **データ移行についての判断**: **移行不要**。行政区マスタにはデータが存在せずカラム定義のみ。

### 施設・場所系

#### **TM_保管所**: 保管所マスタ
- **概要**: 自転車の保管所の詳細情報を管理。保管所コード、名称、収容台数、住所、電話番号、曜日別運用情報を含む。
- **関係エンティティ**: TM_行政区、TM_駅、TM_初期値、TM_運用、TM_納付書、TM_イメージとの関係を持つ。保管業務の中核として機能。
- **データ移行についての判断**: **要検討**。現在のシステムの`Parking`、富士通のシステムに登録されているカラムを用意するか検討する。

#### **TM_駅**: 駅マスタ
- **概要**: 鉄道駅の基本情報と撤去場所との関係を管理。駅コード、駅名、路線、保管所との関係、住所情報を含む。
- **関係エンティティ**: TM_保管所、TM_鉄道路線名、TM_市区町村、TM_町、TM_撤去場所、TM_駐輪場、TM_初期値との関係を持つ。
- **データ移行についての判断**: **移行が必要**。駅コードの移行する。

#### **TM_撤去場所**: 撤去場所マスタ
- **概要**: 自転車の撤去場所の詳細情報を管理。駅よりも詳細な場所の情報を持つ。撤去場所コード、名称、住所情報を含む。
- **関係エンティティ**: TM_駅、TM_市区町村、TM_町との関係を持つ。撤去業務の基本単位として機能。
- **データ移行についての判断**: **移行が必要**。現在のシステムには対応するエンティティがないため作成して移行する。

#### **TM_駐輪場**: 駐輪場マスタ
- **概要**: 認可駐輪場の基本情報を管理。駐輪場コード、名称、連携用駐輪場名を含む。
- **関係エンティティ**: TM_駅、TM_駐輪場連携との関係を持つ。正規駐輪場の管理として機能。
- **データ移行についての判断**: **移行不要**。駐輪場マスタにはデータが存在せずカラム定義のみ。

### 車両系

#### **TM_車輌区分**: 自転車・原付などの区分
- **概要**: 車両の種別（自転車、原付等）を管理する基本マスタ。車輌区分コード、内容を含む。
- **関係エンティティ**: TM_撤去料、TM_防犯登録、TM_送付者、TM_状態2との関係を持つ。車両管理の基本分類として機能。
- **データ移行についての判断**: **移行が必要**。車両区分のみ移行する。

#### **TM_色**: 車両の色マスタ
- **概要**: 車両の色情報を管理する基本マスタ。色コード、色名を含む。
- **関係エンティティ**: 他のテーブルから参照される独立したマスタとして機能。
- **データ移行についての判断**: **移行が必要**。移行の内容は要検討する。Maphinが用意している色にどのようにマッピングするか。

#### **TM_メーカ**: メーカマスタ
- **概要**: 自転車メーカーの基本情報を管理。メーカコード、メーカ名を含む。
- **関係エンティティ**: 他のテーブルから参照される独立したマスタとして機能。
- **データ移行についての判断**: **移行が必要**。

#### **TM_車種**: 車種マスタ
- **概要**: 自転車の車種情報を管理する基本マスタ。車種コード、車種名を含む。
- **関係エンティティ**: 他のテーブルから参照される独立したマスタとして機能。
- **データ移行についての判断**: **移行が必要**。

#### **TM_車名**: 車名マスタ
- **概要**: 自転車の車名情報を管理する基本マスタ。車名コード、車名を含む。
- **関係エンティティ**: 他のテーブルから参照される独立したマスタとして機能。
- **データ移行についての判断**: **移行が必要**。

### システム・運用系

#### **TM_初期値**: システム初期値設定
- **概要**: システムの各種初期値や設定値を管理する重要なテーブル。保管所、駅、都道府県、市区町村、所轄等の初期値設定を含む。
- **関係エンティティ**: TM_保管所、TM_駅、TM_都道府県、TM_市区町村、TM_防犯登録との関係を持つ。システム動作の基本設定として機能。
- **データ移行についての判断**: **移行が必要**。現在のシステムには対応するエンティティがないため、テナント別設定として移行を検討。

#### **TM_運用**: 運用設定
- **概要**: システムの運用に関する設定を管理。保管所、所轄、利用者マスタ格納場所等の運用設定を含む。
- **関係エンティティ**: TM_保管所、TM_防犯登録、TM_行政区との関係を持つ。システム運用の基本設定として機能。
- **データ移行についての判断**: **移行不要**。システムがインストールされた環境についての情報を持つのみ。

#### **TM_利用者**: システム利用者
- **概要**: システムを利用するユーザーの基本情報を管理。利用者コード、パスワード、氏名、最終使用日、管理者権限、所属を含む。
- **関係エンティティ**: TM_ACCESS_LOG、TM_ログ、TM_パスワード1との関係を持つ。システム利用の基本として機能。
- **データ移行についての判断**: **移行不要**。現在のシステムでは`User`で同等機能を提供しており、移行は不要。

#### **TM_ACCESS_LOG**: アクセスログ
- **概要**: システムへのアクセス履歴を記録。利用者コード、接続時刻、結果を含む。
- **関係エンティティ**: TM_利用者との関係を持つ。システム監査の基本として機能。
- **データ移行についての判断**: **移行不要**。現在のシステムでは`BicycleEventLog`等で類似機能を提供しており、移行は不要。

#### **TM_ログ**: 使用ログ
- **概要**: システムの使用履歴を記録。利用者コード、使用日、開始時間、終了時間を含む。
- **関係エンティティ**: TM_利用者との関係を持つ。システム使用状況の把握として機能。
- **データ移行についての判断**: **移行不要**。現在のシステムでは`BicycleReadLog`等で類似機能を提供しており、移行は不要。

### 業務系

#### **TM_処分**: 処分マスタ
- **概要**: 自転車の処分方法を管理する基本マスタ。処分コード、内容、区分を含む。
- **関係エンティティ**: TM_処分詳細との関係を持つ。処分業務の基本分類として機能。
- **データ移行についての判断**: **移行が必要**。現在のMaphinとは設計が異なるが、富士通の設計に合わせて移行を検討。

#### **TM_返還**: 返還マスタ
- **概要**: 自転車の返還方法を管理する基本マスタ。返還コード、有料/無料、内容、統計区分を含む。
- **関係エンティティ**: 他のテーブルから参照される独立したマスタとして機能。
- **データ移行についての判断**: **移行不要**。新しい設計に合わせて、車両の返還方法をマッピングする。

#### **TM_状態**: 状態マスタ
- **概要**: 自転車の状態を管理する基本マスタ。状態コード、状態名を含む。
- **関係エンティティ**: 他のテーブルから参照される独立したマスタとして機能。
- **データ移行についての判断**: **移行が必要**。データ全体を移行するか、コードのみ移行するか、ご相談させていただく。

#### **TM_告示日**: 告示日管理
- **概要**: 撤去から処分までの各種告示日を管理。撤去開始日、撤去終了日、保管告示日、処分告示日、保管期限を含む。
- **関係エンティティ**: 他のテーブルから参照される独立したマスタとして機能。
- **データ移行についての判断**: **移行不要**。現在のシステムでは告示はマスターとして扱わず、車両ごとにデータを持つ。

#### **TM_撤去料**: 撤去料金管理
- **概要**: 車輌区分別の撤去料金を管理。開始日、終了日、車輌区分、撤去料を含む。
- **関係エンティティ**: TM_車輌区分との関係を持つ。料金管理の基本として機能。
- **データ移行についての判断**: **移行が必要**。現在のMaphinの設計では新料金の予定登録ができないため、登録できるよう対応する。

### 防犯・警察系

#### **TM_防犯登録**: 防犯登録マスタ
- **概要**: 防犯登録に関する所轄警察署の情報を管理。車輌区分、都道府県、所轄コード、所轄名等を含む。
- **関係エンティティ**: TM_車輌区分、TM_都道府県、TM_防犯登録_連携、TM_警察住所、TM_保管所、TM_初期値、TM_運用との関係を持つ。
- **データ移行についての判断**: **移行が必要**。

#### **TM_警察住所**: 警察住所マスタ
- **概要**: 警察署の住所や連絡先を管理。都道府県、所轄コード、郵便番号、住所、警察名、担当課等を含む。
- **関係エンティティ**: TM_都道府県、TM_防犯登録、TM_送付者との関係を持つ。警察との連携の基本として機能。
- **データ移行についての判断**: **移行が必要**。

### 送付・文書系

#### **TM_送付者**: 送付者マスタ
- **概要**: 各種文書の送付者情報を管理。車輌区分、送付者略称、発信番号、連絡先、イメージファイル等を含む。
- **関係エンティティ**: TM_車輌区分、TM_警察住所、TM_市区町村住所、TM_陸運局住所との関係を持つ。
- **データ移行についての判断**: **移行不要**。サンプルのようなデータのみであるため、該当するデータはMaphinにご登録頂きたい。

#### **TM_文書**: 文書マスタ
- **概要**: 各種文書のテンプレートを管理。文書コード、文書種別名、本文1-10、上限行数を含む。
- **関係エンティティ**: 他のテーブルから参照される独立したマスタとして機能。
- **データ移行についての判断**: **移行が必要**。データをマスターとして登録するのではなく、帳票のテンプレートファイル（.docx）に組み込む。

#### **TM_市区町村住所**: 市区町村住所マスタ
- **概要**: 市区町村の住所や連絡先を管理。都道府県、市区町村、郵便番号、住所、電話番号、担当課等を含む。
- **関係エンティティ**: TM_都道府県、TM_市区町村、TM_送付者との関係を持つ。市区町村との連携の基本として機能。
- **データ移行についての判断**: **移行が必要**。自治体税務課への問い合わせ先情報としてデータ移行する。

#### **TM_陸運局住所**: 陸運局住所マスタ
- **概要**: 陸運局の住所や連絡先を管理。陸運局番号、名称、郵便番号、住所、担当課等を含む。
- **関係エンティティ**: TM_送付者との関係を持つ。陸運局との連携の基本として機能。
- **データ移行についての判断**: **移行が必要**。
