# セルフ・リレーション・バージョニング

[Self-relations](https://www.prisma.io/docs/orm/prisma-schema/data-model/relations/self-relations)機能を使用してバージョン管理のための設計を考案しました。

```prisma
model VersionControllable {
  id String @id @default(uuid())

  originalId String?
  original   VersionControllable?  @relation("versions", fields: [originalId], references: [id])
  versions   VersionControllable[] @relation("versions")
}
```

このモデルはオリジナルのレコードを最新として更新しつつ、
すべてのバージョンは別レコードとして複製することを前提としています。

~~更新するレコードは**Mutableレコード**、更新しないレコードを**Immutableレコード**と呼ぶこととします。
実際にはどちらも変更可能ですが設計方針としてImmutableレコードは変更しません。~~

更新するレコードは**オリジナルレコード**、更新しないレコードを**ログレコード**と呼ぶこととします。
Mutableレコード、Immutableレコードという呼び方でも役割は同じですが、
`original` という単語選択に合わせて呼び方を変更しました。
コーディングにおいても**オリジナル**、**ログ**という呼び方を考慮した命名をお願いします。

以下、 `VersionControllable` が `value` というフィールドも持つモデルとして説明します。

オリジナルレコードの`versions`にログレコードは格納されます。
原則としてログレコードは変更されないため、`versions` は `createdAt` でソートします。

## 新しいデータの作成

`value` フィールドに `foo` という値を持つレコードを作成するケースを考えます。
新しいデータを作成する場合、オリジナルレコードとログレコードを作成します。

| タイプ     | id  | originalId | value | versions          |
| ---------- | --- | ---------- | ----- | ----------------- |
| オリジナル | 1   | null       | foo   | `[{ id: 2,... }]` |
| ログ       | 2   | 1          | foo   | -                 |

オリジナルレコードには`originalId`に`null`を設定します。
一方、ログレコードには`originalId`にオリジナルレコードの`id`を設定します。

## データの更新

次に、`value` フィールドを `bar` という値で更新します。
作成済みのオリジナルレコードは更新します。そして更新後のレコードからログレコードを作成します。
ログレコードには`originalId`にオリジナルレコードの`id`を設定します。

| タイプ     | id  | originalId | value | versions                        |
| ---------- | --- | ---------- | ----- | ------------------------------- |
| オリジナル | 1   | null       | bar   | `[{ id: 2,... },{ id: 3,... }]` |
| ログ       | 2   | 1          | foo   | -                               |
| ログ       | 3   | 1          | bar   | -                               |

つまりデータの更新ごとにログレコードが作成されます。
そしてオリジナルレコードの`versions`からすべてのログレコードを参照できます。

```typescript
  const body = await prisma.bicycleBody.findFirstOrThrow({ include: { versions: true } });
  const { versions } = body;
```

[サンプルコードはこちら](../prisma-for-doc/samples/bicycle-core/client.ts)
