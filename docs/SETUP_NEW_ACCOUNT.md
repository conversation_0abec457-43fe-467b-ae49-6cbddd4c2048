# 新しいAWSアカウント上の環境構築手順 <!-- omit in toc -->

※以下の手順にはCDの構築が含まれていません。
別途、CDの構築手順はドキュメント化予定です。

## 旧アカウント→新アカウントへのドメインの移管手続き

https://docs.aws.amazon.com/ja_jp/Route53/latest/DeveloperGuide/domain-transfer-between-aws-accounts.html

## MFAの設定

アカウントをセキュアに保つため、MFA（多要素認証）を設定してください。
AWSのコンソールにログインし、IAMユーザーのセキュリティ認証情報からMFAデバイスを追加します。

## AWS CLIの設定

AWSコンソールからアクセスキーを取得してください。
そしてAWS CLIを使用するときにプロフィールが指定できるように、
以下のコマンドを実行して新しいプロファイルを設定します。

```sh
aws configure --profile maphin
```

## cdk.json, cdk.context.jsonの設定

`cdk.json`と`cdk.context.json`は、CDKの設定ファイルです。

`cdk.json` は、`context.env` の `account` と `region` を設定してください。

`cdk.context.json`は旧環境の情報をクリアするため次のコマンドを実行します。

```sh
npm run clear-context
```

## `pedal-secret`スタックのデプロイ

SecretStackは、AWS Secrets Managerを使用して機密情報を管理するためのスタックです。
SecretStackをデプロイするには、以下のコマンドを実行します。

```sh
npm run cdk deploy pedal-secret
```

## 各種シークレットの設定

SecretStackのデプロイ後、
シークレットとして`CommonSecretKey` に宣言されている `code_connection_arn` 以外を設定します。

- slack_webhook_url
- first_username
- first_user_password
- google_map_api_key
- google_map_id

## `pedal-common`スタックのデプロイ

現在の設計では`pedal-common`にRoute53のホストゾーンが含まれています。
新しくホストゾーンには、新しいNS(ネームサーバー)レコードとSOAレコードが自動で作成されます。

## 登録済みドメインのネームサーバーの値を更新

登録済みドメインのネームサーバーの値を、新しいNSレコードの値で更新する必要があります。
ネームサーバーの設定がDNSプロバイダーに反映されるまで、最大24時間かかることがあります。

## Env系スタックのデプロイ

以下のコマンドを実行して、環境ごとのスタックをデプロイします。

```sh
npm run deploy-all <env>
```
