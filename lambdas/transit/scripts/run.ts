#!/usr/bin/env tsx

import { handler } from '../index';
import type { TransitEvent } from '../src/types';

/**
 * Transit Lambda関数をローカルで実行するスクリプト
 *
 * 使用例:
 * npm run transit shinagawa stage ver20250708
 */
async function main() {
  const args = process.argv.slice(2);

  if (args.length !== 3) {
    console.error('Usage: npm run transit <tenant> <env> <version>');
    console.error('Example: npm run transit shinagawa stage ver20250708');
    process.exit(1);
  }

  const [tenantId, env, version] = args;

  console.log(`🚀 Starting transit process for tenant: ${tenantId}, env: ${env}`);
  console.log(`📅 Version: ${version}`);
  console.log('');

  // TransitEventを作成
  const event: TransitEvent = {
    tenantId,
    version,
  };

  try {
    // 環境変数を設定
    process.env.ENV = env;

    // Lambda handlerを実行
    await handler(event, {} as any, {} as any);

    console.log('');
    console.log('✅ Transit process completed successfully!');
  } catch (error) {
    console.error('');
    console.error('❌ Transit process failed:', error);
    process.exit(1);
  }
}

main().catch((error) => {
  console.error('❌ Script execution failed:', error);
  process.exit(1);
});
