#!/usr/bin/env tsx

import {
  CopyObjectCommand,
  DeleteObjectCommand,
  ListObjectsV2Command,
  S3Client,
  type _Object,
} from '@aws-sdk/client-s3';
import split from 'just-split';

interface MoveOptions {
  bucketName: string;
  srcPrefix: string;
  destPrefix: string;
}

/**
 * 単一のS3オブジェクトを移動する関数
 */
async function moveS3Object(
  s3Client: S3Client,
  bucketName: string,
  object: _Object,
  srcPrefix: string,
  destPrefix: string,
): Promise<string> {
  if (!object.Key) {
    throw new Error('Object key is undefined');
  }

  // 新しいキーを生成（srcPrefixをdestPrefixに置換）
  const newKey = object.Key.replace(srcPrefix, destPrefix);

  // コピー操作
  const copyCommand = new CopyObjectCommand({
    Bucket: bucketName,
    CopySource: `${bucketName}/${encodeURIComponent(object.Key)}`,
    Key: newKey,
  });
  await s3Client.send(copyCommand);

  // 削除操作
  const deleteCommand = new DeleteObjectCommand({
    Bucket: bucketName,
    Key: object.Key,
  });
  await s3Client.send(deleteCommand);

  return `${object.Key} -> ${newKey}`;
}

/**
 * S3オブジェクトを移動（コピー + 削除）する関数
 */
async function moveS3Objects({ bucketName, srcPrefix, destPrefix }: MoveOptions): Promise<void> {
  const s3Client = new S3Client({ region: 'ap-northeast-1' });

  console.log(
    `Starting to move objects from s3://${bucketName}/${srcPrefix} to s3://${bucketName}/${destPrefix}`,
  );
  console.log('Using parallel processing with 20 objects per batch');
  console.log('');

  let continuationToken: string | undefined;
  let totalObjects = 0;
  let movedObjects = 0;
  let skippedObjects = 0;

  do {
    // S3オブジェクト一覧を取得
    const listCommand = new ListObjectsV2Command({
      Bucket: bucketName,
      Prefix: srcPrefix,
      ContinuationToken: continuationToken,
      MaxKeys: 1000, // 一度に取得する最大数
    });

    const listResponse = await s3Client.send(listCommand);

    if (!listResponse.Contents || listResponse.Contents.length === 0) {
      console.log('No objects found to move.');
      break;
    }

    totalObjects += listResponse.Contents.length;
    console.log(`Found ${listResponse.Contents.length} objects in this batch.`);

    // 移動先プレフィックスを持つオブジェクトを除外
    const objectsToMove = listResponse.Contents.filter((object) => {
      if (!object.Key) return false;
      // 移動先プレフィックスで始まるオブジェクトはスキップ
      return !object.Key.startsWith(destPrefix);
    });

    const currentSkipped = listResponse.Contents.length - objectsToMove.length;
    skippedObjects += currentSkipped;

    if (objectsToMove.length === 0) {
      console.log('No objects to move (all objects are already at destination).');
      continuationToken = listResponse.NextContinuationToken;
      continue;
    }

    console.log(
      `Objects to move: ${objectsToMove.length} (excluding ${currentSkipped} already at destination)`,
    );

    // オブジェクトを20件ずつに分割
    const chunks = split(objectsToMove, 20);

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      console.log(`\nProcessing chunk ${i + 1}/${chunks.length} (${chunk.length} objects)`);

      // 20件を並列処理
      const results = await Promise.allSettled(
        chunk.map((object) => moveS3Object(s3Client, bucketName, object, srcPrefix, destPrefix)),
      );

      // 結果をチェックして、エラーがあれば即座に停止
      for (let j = 0; j < results.length; j++) {
        const result = results[j];
        const object = chunk[j];

        if (result.status === 'fulfilled') {
          movedObjects++;
          console.log(`✓ Moved: ${result.value}`);
        } else {
          console.error(`✗ Failed to move object: ${object.Key}`);
          console.error('Error details:', result.reason);
          throw new Error(`Object move operation failed for: ${object.Key}`);
        }
      }

      console.log(`Chunk ${i + 1} completed. Progress: ${movedObjects}/${totalObjects}`);
    }

    continuationToken = listResponse.NextContinuationToken;
  } while (continuationToken);

  console.log('\nMove operation completed successfully!');
  console.log(`Total objects found: ${totalObjects}`);
  console.log(`Objects moved: ${movedObjects}`);
  console.log(`Objects skipped (already at destination): ${skippedObjects}`);
}

/**
 * コマンドライン引数をパース
 */
function parseArguments(): MoveOptions {
  const args = process.argv.slice(2);

  if (args.length !== 3) {
    console.error('Usage: npm run move-s3-objects <bucketName> <srcPrefix> <destPrefix>');
    console.error('');
    console.error('Example:');
    console.error(
      '  npm run move-s3-objects maphin-transit-for-shinagawa-stage ver20250708/ ver20250708/raw/',
    );
    process.exit(1);
  }

  const [bucketName, srcPrefix, destPrefix] = args;

  return {
    bucketName,
    srcPrefix,
    destPrefix,
  };
}

/**
 * メイン処理
 */
async function main(): Promise<void> {
  try {
    const options = parseArguments();

    console.log('Move S3 Objects Script');
    console.log('====================');
    console.log(`Bucket: ${options.bucketName}`);
    console.log(`Source Prefix: ${options.srcPrefix}`);
    console.log(`Destination Prefix: ${options.destPrefix}`);
    console.log('');

    // 確認プロンプト
    console.log('⚠️  This operation will move objects from source to destination.');
    console.log('⚠️  Original objects will be deleted after successful copy.');
    console.log('');

    await moveS3Objects(options);
  } catch (error) {
    console.error('❌ Script execution failed:');
    console.error(error);
    process.exit(1);
  }
}

// スクリプト実行
main();
