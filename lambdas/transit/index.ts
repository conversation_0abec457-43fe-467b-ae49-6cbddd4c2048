import type { <PERSON><PERSON> } from 'aws-lambda';
import { envKeySchema, transitBucketName } from 'common';

import { tenants } from 'models';
import { createRlsClientProvider } from '../.prisma';
import { convertMasterXlsxToCsv } from './src/master-xlsx-to-csv';
import type { TransitContext, TransitEvent } from './src/types';

export const handler: Handler<TransitEvent> = async (event) => {
  console.log(`\nEVENT: ${JSON.stringify(event, null, 2)}`);
  const { tenantId, version } = event;
  const tenant = tenants.find((t) => t.id === tenantId);
  if (tenant === undefined) throw new Error(`Tenant not found: ${tenantId}`);
  const clients = createRlsClientProvider().setTenantId(tenant.id);
  const envKey = envKeySchema.parse(process.env.ENV);
  const bucketName = transitBucketName(envKey, tenant.id);
  console.log(`Bucket Name: ${bucketName}`);
  const context: TransitContext = { ...clients, envKey, tenant, version, bucketName };
  await convertMasterXlsxToCsv(context);
};
