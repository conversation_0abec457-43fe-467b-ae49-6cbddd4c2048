# データ移行の設計

## データ移行の全体的な手順

### 移行データをS3にアップロード

TransitStackが管理する`maphin-transit-for-xxx`というS3バケットに、移行対象のデータをアップロードします。

- `maphin-transit-for-xxx`: 本番環境バケット
- `maphin-transit-for-xxx-stage`: ステージング環境バケット

移行データは`verYYYYMMDD/raw/`というプレフィックスで保存します。
日付でバージョンを管理し、次のプレフィックスでデータの状態を示します。

- `raw/`: 元データ
- `csv/`: CSV形式のデータ（中間データの形式は増えるかも）
- `json/`: DB投入の準備が完了した最終データ

### xlsx → csv へ変換

`verYYYYMMDD/raw/`のExcelファイルをCSVファイルに変換して`verYYYYMMDD/csv/`に配置します。

### csv → json へ変換

csvファイルをJSON形式に変換して`verYYYYMMDD/json/`に配置します。
jsonはそのままPrismaでデータを投入できる構造にします。

### json → DB へ投入

`verYYYYMMDD/json/`のJSONファイルをレビューして問題がなければ、Prismaを使ってDBに投入します。
