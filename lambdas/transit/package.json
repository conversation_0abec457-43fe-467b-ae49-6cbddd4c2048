{"name": "transit", "version": "0.0.0", "private": true, "type": "module", "main": "./index.ts", "scripts": {"move-s3-objects": "tsx ./scripts/move-s3-objects.ts", "type": "tsc --noEmit"}, "dependencies": {"@aws-sdk/client-s3": "3.774.0", "common": "*", "date-fns": "3.6.0", "just-split": "3.2.0", "lambda-api": "*", "models": "*", "papaparse": "5.5.2", "ts-pattern": "5.6.2", "zod": "3.24.4"}, "devDependencies": {"tsconfig": "*", "tsx": "^4.19.4", "typescript": "5.8.3", "vitest": "3.0.9"}, "volta": {"node": "22.15.0", "npm": "11.3.0"}}