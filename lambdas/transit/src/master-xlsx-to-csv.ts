import { InvokeCommand, LambdaClient } from '@aws-sdk/client-lambda';
import { ListObjectsV2Command, type ListObjectsV2CommandOutput } from '@aws-sdk/client-s3';
import { xlsxToCsvLambdaName } from 'common';
import split from 'just-split';
import { s3 } from '../init';
import type { TransitContext } from './types';

const lambda = new LambdaClient();

/**
 * xlsx-to-csv Lambda関数名を取得
 */

/**
 * マスタデータをすべて xlsx → csv へ変換
 */
export const convertMasterXlsxToCsv = async ({ envKey, bucketName, version }: TransitContext) => {
  // マスタデータのオブジェクト一覧を取得
  const masterObjects = await s3.send(
    new ListObjectsV2Command({
      Bucket: bucketName,
      Prefix: `${version}/raw/マスタデータ/`,
    }),
  );

  if (!masterObjects.Contents) {
    console.warn('No master objects found.');
    return;
  }

  // .xlsxファイルのみをフィルタリング
  const xlsxFiles = masterObjects.Contents.filter((obj) => obj.Key?.endsWith('.xlsx'));

  if (xlsxFiles.length === 0) {
    console.warn('No xlsx files found in master data.');
    return;
  }

  console.log(`Found ${xlsxFiles.length} xlsx files to convert`);
  console.log(`Using Lambda function: ${xlsxToCsvLambdaName(envKey)}`);

  // 各xlsxファイルの変換関数を定義
  const convertXlsxFile = async (
    xlsxFile: Exclude<ListObjectsV2CommandOutput['Contents'], undefined>[number],
  ) => {
    if (!xlsxFile.Key) return;

    // CSVファイルのキーを生成（rawをcsvに置き換え、拡張子を.csvに変更）
    const csvKey = xlsxFile.Key.replace('/raw/', '/csv/').replace(/\.xlsx$/, '.csv');

    console.log(`Converting: ${xlsxFile.Key} -> ${csvKey}`);

    try {
      // xlsx-to-csv Lambda関数を呼び出し
      const response = await lambda.send(
        new InvokeCommand({
          FunctionName: xlsxToCsvLambdaName(envKey),
          InvocationType: 'RequestResponse', // 同期呼び出し
          Payload: JSON.stringify({
            bucketName,
            xlsxKey: xlsxFile.Key,
            csvKey,
            overwrite: false,
          }),
        }),
      );

      // レスポンスの確認
      if (response.Payload) {
        const payloadText = new TextDecoder().decode(response.Payload);
        console.log(`Lambda response payload for ${xlsxFile.Key}:`, payloadText);

        try {
          const result = JSON.parse(payloadText);
          if (result.status === 'success') {
            console.log(`✅ Successfully converted: ${xlsxFile.Key}`);
          } else {
            console.error(
              `❌ Failed to convert ${xlsxFile.Key}:`,
              result.message || 'Unknown error',
            );
          }
        } catch (parseError) {
          console.error(`❌ Failed to parse response for ${xlsxFile.Key}:`, parseError);
          console.error('Raw response:', payloadText);
        }
      } else {
        console.error(`❌ No payload received for ${xlsxFile.Key}`);
      }
    } catch (error) {
      console.error(`❌ Error invoking lambda for ${xlsxFile.Key}:`);
      console.error('Error details:', error);
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }
    }
  };

  // xlsxファイルを10件ずつに分割
  const chunks = split(xlsxFiles, 10);

  // 10件ずつ並列処理
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    console.log(`Processing chunk ${i + 1}/${chunks.length} (${chunk.length} files)`);

    await Promise.all(chunk.map(convertXlsxFile));

    console.log(`Chunk ${i + 1} completed`);
  }

  console.log('✨ Master data xlsx to csv conversion completed');
};
