import { z } from 'zod';

import type { Context, TRPCProcedure } from '../../../../types';

const InputSchema = z.object({ id: z.string() }).strict();
type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const removeSearchSetMutation = async ({
  input: { id },
  ctx: { asyncTx, tenantId },
}: MutationArgs) => {
  // この設定は例外的に物理削除する
  await asyncTx(async (tx) => {
    await tx.searchSet.delete({ where: { tenantId, id } });
  });
  return 'OK';
};

export const removeSearchSet = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(removeSearchSetMutation);
