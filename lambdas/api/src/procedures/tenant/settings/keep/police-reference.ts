import { z } from 'zod';

import { PoliceReferenceFileTypeSchema } from 'common';
import type { Context, TRPCProcedure } from '../../../../types';

const InputSchema = z
  .object({
    fileType: PoliceReferenceFileTypeSchema,
    csvIgnoreIndexes: z.array(z.number()),
    csvIgnoreLastIndexes: z.array(z.number()),
    xlsxIgnoreIndexes: z.array(z.number()),
    xlsxIgnoreLastIndexes: z.array(z.number()),
  })
  .strict();
type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const updatePoliceReferenceSettingsMutation = async ({
  input,
  ctx: { prisma, tenantId },
}: MutationArgs) => {
  await prisma.policeReferenceSettings.update({ where: { tenantId }, data: input });
  return 'OK';
};

export const updatePoliceReferenceSettings = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(updatePoliceReferenceSettingsMutation);
