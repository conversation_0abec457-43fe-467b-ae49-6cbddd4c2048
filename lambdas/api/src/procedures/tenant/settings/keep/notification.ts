import { z } from 'zod';

import type { Context, TRPCProcedure } from '../../../../types';

const InputSchema = z.object({
  deadlineDays: z.number().int(),
});
type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const updateNotificationSettingsMutation = async ({
  input,
  ctx: { prisma, tenantId },
}: MutationArgs) => {
  await prisma.notificationSettings.update({ where: { tenantId }, data: input });
  return 'OK';
};

export const updateNotificationSettings = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(updateNotificationSettingsMutation);
