import { z } from 'zod';

import { AnnouncementFlowSchema } from 'common';

import type { Context, TRPCProcedure } from '../../../../types';

const InputSchema = z.object({
  flow: AnnouncementFlowSchema,
  days: z.number().int().min(0),
});
type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const updateAnnouncementSettingsMutation = async ({
  input,
  ctx: { prisma, tenantId },
}: MutationArgs) => {
  await prisma.announcementSettings.update({ where: { tenantId }, data: input });
  return 'OK';
};

export const updateAnnouncementSettings = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(updateAnnouncementSettingsMutation);
