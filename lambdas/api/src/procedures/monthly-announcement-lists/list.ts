import type { TRPCProcedure } from '../../types';
import { calcMonthlyAnnouncementListStatus } from './types';

export const listMonthlyAnnouncementLists = (p: TRPCProcedure) =>
  p.query(async ({ ctx: { prisma, tenantId } }) => {
    const monthlyAnnouncementLists = await prisma.monthlyAnnouncementList.findMany({
      where: { tenantId, deleted: false },
      include: { _count: { select: { events: { where: { cancelBy: null } } } } },
      orderBy: { createdAt: 'desc' },
    });
    return monthlyAnnouncementLists.map(
      (list) =>
        ({
          ...list,
          status: calcMonthlyAnnouncementListStatus(list),
        }) as const,
    );
  });
