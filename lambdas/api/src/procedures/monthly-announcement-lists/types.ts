import { addDays, isBefore } from 'date-fns';

export const calcMonthlyAnnouncementListStatus = <
  T extends { startedAt: Date | null; days: number | null },
>(
  list: T,
  now = new Date(),
) => {
  if (list.startedAt === null) return 'waiting';
  if (list.days === null) throw new Error('days must not be null');
  if (isBefore(now, addDays(list.startedAt, list.days))) return 'doing';
  return 'done';
};

export type MonthlyAnnouncementListStatus = ReturnType<typeof calcMonthlyAnnouncementListStatus>;
