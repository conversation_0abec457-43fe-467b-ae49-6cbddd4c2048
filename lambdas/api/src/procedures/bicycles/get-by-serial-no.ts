import { z } from 'zod';

import { applyComputedProps, bicycleDeepInclude } from '../../bicycle-funcs';
import { type Context, type TRPCProcedure, internalTenantArgs } from '../../types';

const InputSchema = z
  .object({
    serialNo: z.string(),
  })
  .strict();

type QueryArgs = {
  input: z.infer<typeof InputSchema>;
  ctx: Context;
};

export const getBicycleBySerialNoQuery = async ({ input, ctx }: QueryArgs) => {
  const { serialNo } = input;
  const { sequentialTx, tenantId } = ctx;
  const [bicycle, tenant] = await sequentialTx((tx) => [
    tx.bicycle.findFirst({
      where: { tenantId, serialTag: { last: { serialNo } } },
      include: bicycleDeepInclude,
    }),
    tx.tenant.findUniqueOrThrow({ where: { id: tenantId }, ...internalTenantArgs }),
  ]);
  if (bicycle === null) return null;
  return applyComputedProps(tenant)(bicycle);
};

export const getBicycleBySerialNo = (p: TRPCProcedure) =>
  p.input(InputSchema).query(getBicycleBySerialNoQuery);
