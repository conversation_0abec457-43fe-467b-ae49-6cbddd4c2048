import { createPresignedPost } from '@aws-sdk/s3-presigned-post';
import { createInquiryCommentImagesKey } from 'models';
import { z } from 'zod';
import type { TRPCProcedure } from '../../types';
import { createArgs } from './funcs';

const InputSchema = z
  .object({
    inquiryId: z.string().uuid(),
    commentId: z.string().uuid(),
    files: z
      .array(
        z.object({
          contentType: z.string(),
          filename: z.string().regex(/^.+\..+$/),
        }),
      )
      .max(5),
  })
  .strict();

export const listInquiryCommentPresignedUrls = (p: TRPCProcedure) =>
  p.input(InputSchema).query(async ({ input, ctx }) => {
    const { inquiryId, commentId, files } = input;
    const { tenantId, userId } = ctx;
    const date = new Date();
    return Promise.all(
      files.map(async ({ filename, contentType }) => {
        const key = createInquiryCommentImagesKey({
          tenantId,
          inquiryId,
          commentId,
          userId,
          date,
          filename,
        });
        const presignedPost = await createPresignedPost(...createArgs({ key, contentType, ctx }));
        console.log('Presigned POST URL generated:', presignedPost.url);
        return { ...presignedPost, key, inquiryId, commentId };
      }),
    );
  });
