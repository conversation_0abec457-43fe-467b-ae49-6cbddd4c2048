import { TRPCError } from '@trpc/server';
import type { z } from 'zod';
import type { Context, TRPCProcedure } from '../../types';
import { PriceSuggestionInputSchema } from './type';

type MutationArgs = {
  input: z.infer<typeof PriceSuggestionInputSchema>;
  ctx: Context;
};

const createPriceSuggestionMutation = async ({ input, ctx }: MutationArgs) => {
  const { prisma } = ctx;
  const found = await prisma.priceSuggestion.findFirst({
    where: { price: input.price },
  });
  if (found) throw new TRPCError({ code: 'CONFLICT' });
  await prisma.priceSuggestion.create({ data: input });
  return 'OK';
};

export const createPriceSuggestion = (p: TRPCProcedure) =>
  p.input(PriceSuggestionInputSchema).mutation(createPriceSuggestionMutation);
