import type { z } from 'zod';

import { TRPCError } from '@trpc/server';
import type { Context, TRPCProcedure } from '../../types';
import { PriceSuggestionInputSchema } from './type';

type MutationArgs = {
  input: z.infer<typeof PriceSuggestionInputSchema>;
  ctx: Context;
};

const updatePriceSuggestionMutation = async ({ input, ctx }: MutationArgs) => {
  const { id } = input;
  const { prisma } = ctx;
  const found = await prisma.priceSuggestion.findFirst({
    where: { price: input.price, NOT: { id } },
  });
  if (found) throw new TRPCError({ code: 'CONFLICT' });
  await prisma.priceSuggestion.update({
    where: { id },
    data: input,
  });
  return 'OK';
};

export const updatePriceSuggestion = (p: TRPCProcedure) =>
  p.input(PriceSuggestionInputSchema).mutation(updatePriceSuggestionMutation);
