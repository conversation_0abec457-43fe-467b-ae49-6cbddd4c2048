import { z } from 'zod';

import type { Context, TRPCProcedure } from '../../types';

const InputSchema = z.object({
  id: z.string().uuid(),
});

type MutationArgs = {
  input: z.infer<typeof InputSchema>;
  ctx: Context;
};

export const deletePriceSuggestionMutation = async ({ input, ctx: { prisma } }: MutationArgs) => {
  const { id } = input;
  await prisma.priceSuggestion.delete({ where: { id } });
  return 'OK';
};

export const deletePriceSuggestion = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(deletePriceSuggestionMutation);
