import { BicycleTypeSchema } from 'common';
import { endOfDay, startOfDay } from 'date-fns';
import { z } from 'zod';
import { applyComputedProps, bicycleShallowInclude } from '../../bicycle-funcs';
import { type TRPCProcedure, internalTenantArgs } from '../../types';

const InputSchema = z
  .object({
    storageId: z.string().optional(),
    cycleType: BicycleTypeSchema.optional(),
    isInStorage: z.boolean().optional(),
    removeDate: z.object({
      from: z.date(),
      to: z.date(),
    }),
    policeRequestDate: z.object({
      from: z.date(),
      to: z.date(),
    }),
  })
  .strict();

/* 盗難車一覧 */
export const listTheftBicycles = (p: TRPCProcedure) =>
  p.input(InputSchema).query(async ({ input, ctx: { tenantId, prisma } }) => {
    const { storageId, cycleType, removeDate, policeRequestDate, isInStorage } = input;

    const removeFrom = startOfDay(removeDate.from);
    const removeTo = endOfDay(removeDate.to);
    const policeReferenceFrom = startOfDay(policeRequestDate.from);
    const policeReferenceTo = endOfDay(policeRequestDate.to);

    console.log(`removeDate: ${removeFrom} - ${removeTo}`);
    console.log(`policeRequestDate: ${policeReferenceFrom} - ${policeReferenceTo}`);

    const tenant = await prisma.tenant.findUniqueOrThrow({
      where: { id: tenantId },
      ...internalTenantArgs,
    });
    const bicycles = await prisma.bicycle.findMany({
      where: {
        tenantId,
        deleted: false,
        ...(storageId && { storageLocation: { last: { storageId } } }),
        ...(cycleType && { type: cycleType }),
        ...(isInStorage && { status: 'store' }),
        theftReport: { last: { reported: true } },
        AND: [
          {
            events: {
              some: {
                type: 'remove',
                date: { gte: removeFrom, lte: removeTo },
              },
            },
          },
          {
            events: {
              some: {
                type: 'requestPolice',
                date: { gte: policeReferenceFrom, lte: policeReferenceTo },
              },
            },
          },
        ],
      },
      include: bicycleShallowInclude,
    });

    return bicycles.map(applyComputedProps(tenant));
  });
