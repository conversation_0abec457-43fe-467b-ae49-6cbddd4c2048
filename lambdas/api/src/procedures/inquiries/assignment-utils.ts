import { getLabelDict } from 'models';
import type { Tx } from '../../../../.prisma';

export type AssignmentChangeType = 'assign' | 'unassign' | 'reassign' | 'selfAssign' | 'noChange';

export type AssignmentChange = {
  type: AssignmentChangeType;
  currentAssigneeId?: string;
  newAssigneeId?: string;
  shouldNotifyCurrentAssignee: boolean;
  shouldNotifyNewAssignee: boolean;
};

type NotificationData = {
  inquiryId: string;
  inquiryTitle: string;
  assigneeId: string;
  assignerId: string;
  type: 'assign' | 'unassign' | 'reassign';
  tenantId: string;
};

/**
 * Creates a notification for inquiry assignment changes
 */
export const createInquiryAssignmentNotification = async (tx: Tx, data: NotificationData) => {
  const items = await tx.label.findMany({
    where: { tenantId: data.tenantId },
  });
  const labels = getLabelDict(items);

  const messageTemplates = {
    assign: {
      title: `${labels.common.inquiry}の設定`,
      description: `${labels.common.inquiry}「${data.inquiryTitle}」の${labels.action.assigned}ました。`,
    },
    unassign: {
      title: `${labels.common.inquiry}の変更`,
      description: `${labels.common.inquiry}「${data.inquiryTitle}」の割り当てが解除されました。`,
    },
    reassign: {
      title: `${labels.common.inquiry}の変更`,
      description: `${labels.common.inquiry}「${data.inquiryTitle}」の担当者に再び設定されました。`,
    },
  };

  const message = messageTemplates[data.type];

  const notification = await tx.notification.create({
    data: {
      title: message.title,
      description: message.description,
      type: 'inquiryAssignment',
      scope: 'user',
      inquiryId: data.inquiryId,
    },
  });

  await tx.notificationsOnUsers.create({
    data: {
      notificationId: notification.id,
      userId: data.assigneeId,
    },
  });
};

/**
 * Handles all assignment notifications based on the change analysis
 */
export const notifyAssignment = async (
  tx: Tx,
  change: AssignmentChange,
  inquiryId: string,
  inquiryTitle: string,
  actorId: string,
  tenantId: string,
) => {
  // selfAssign と noChange の場合は通知を送信しない
  if (change.type === 'selfAssign' || change.type === 'noChange') {
    return;
  }

  const notifications: Promise<void>[] = [];

  if (change.shouldNotifyCurrentAssignee && change.currentAssigneeId) {
    const notificationType = change.type === 'unassign' ? 'unassign' : 'reassign';
    notifications.push(
      createInquiryAssignmentNotification(tx, {
        inquiryId,
        inquiryTitle,
        assigneeId: change.currentAssigneeId,
        assignerId: actorId,
        type: notificationType,
        tenantId,
      }),
    );
  }

  if (change.shouldNotifyNewAssignee && change.newAssigneeId) {
    const notificationType = change.type === 'assign' ? 'assign' : 'assign';
    notifications.push(
      createInquiryAssignmentNotification(tx, {
        inquiryId,
        inquiryTitle,
        assigneeId: change.newAssigneeId,
        assignerId: actorId,
        type: notificationType,
        tenantId,
      }),
    );
  }

  await Promise.all(notifications);
};
