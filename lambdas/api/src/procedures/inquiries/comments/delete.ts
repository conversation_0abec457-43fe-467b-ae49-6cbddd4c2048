import { z } from 'zod';
import type { Context, TRPCProcedure } from '../../../types';

const InputSchema = z
  .object({
    commentId: z.string().uuid(),
  })
  .strict();

type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const deleteInquiryCommentMutation = async ({
  input: { commentId },
  ctx: { prisma, asyncTx, userId: loginUserId, tenantId },
}: MutationArgs) => {
  const existed = await prisma.inquiryComment.findFirst({
    where: {
      id: commentId,
      tenantId,
      deleted: false,
      originalId: null,
    },
  });
  if (!existed) throw new Error('Comment not found');
  if (existed.userId !== loginUserId) {
    throw new Error("Permission denied: cannot delete other users' comments.");
  }

  await asyncTx(async (tx) => {
    // Soft delete the comment
    await tx.inquiryComment.update({
      where: { id: commentId },
      data: { deleted: true },
    });

    // Soft delete all associated images
    await tx.inquiryCommentImage.updateMany({
      where: {
        commentId,
        deleted: false,
      },
      data: { deleted: true },
    });
  });

  return 'OK';
};

export const deleteInquiryComment = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(deleteInquiryCommentMutation);
