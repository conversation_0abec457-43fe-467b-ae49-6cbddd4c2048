import { ImageInputSchema } from 'common';
import { z } from 'zod';
import type { Prisma } from '../../../../../.prisma';
import type { Context, TRPCProcedure } from '../../../types';

const InputSchema = z
  .object({
    inquiryId: z.string().uuid(),
    commentId: z.string().uuid(),
    body: z.string().min(1).max(1000),
    images: z.array(ImageInputSchema),
  })
  .strict();

type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const createInquiryCommentMutation = async ({ input, ctx }: MutationArgs) => {
  const { inquiryId, commentId, body, images } = input;
  const { asyncTx, userId: creatorId } = ctx;

  const data: Prisma.InquiryCommentCreateInput = {
    id: commentId,
    inquiry: { connect: { id: inquiryId } },
    user: { connect: { id: creatorId } },
    body,
    images: { createMany: { data: images } },
  };

  await asyncTx(async (tx) => {
    await tx.inquiryComment.create({ data: { ...data, id: commentId } });
    const { id: _, ...versionData } = data;
    await tx.inquiryComment.create({
      data: { ...versionData, original: { connect: { id: commentId } } },
    });
  });

  return 'OK';
};

export const createInquiryComment = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(createInquiryCommentMutation);
