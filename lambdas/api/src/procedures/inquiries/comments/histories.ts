import { z } from 'zod';
import type { Context, TRPCProcedure } from '../../../types';

const InputSchema = z
  .object({
    commentId: z.string().uuid(),
  })
  .strict();

type Input = z.infer<typeof InputSchema>;
type QueryArgs = { input: Input; ctx: Context };

export const getInquiryCommentHistoryQuery = async ({ input, ctx }: QueryArgs) => {
  const { prisma, tenantId } = ctx;
  const { commentId } = input;

  // get historical versioning only
  const histories = await prisma.inquiryComment.findMany({
    where: {
      originalId: commentId,
      tenantId,
    },
    include: {
      user: true,
      images: {
        where: { deleted: false },
        orderBy: { sortOrder: 'asc' },
      },
    },
    orderBy: { createdAt: 'desc' },
  });

  return histories;
};

export const getInquiryCommentHistories = (p: TRPCProcedure) =>
  p.input(InputSchema).query(getInquiryCommentHistoryQuery);
