import os
import tempfile
import boto3
import logging
from converter import convert_xlsx_to_csv

logger = logging.getLogger()
logger.setLevel(logging.INFO)
s3_client = boto3.client('s3')

def handler(event, context):
    """
    Lambda関数のエントリポイント

    Args:
        event: イベントオブジェクト（xlsxKey、csvKey、bucketNameを含む）
        context: Lambda実行コンテキスト

    Returns:
        dict: 処理結果を含むオブジェクト
    """
    try:
        # イベントからパラメータを取得
        xlsx_key = event.get('xlsxKey')
        csv_key = event.get('csvKey')
        bucket_name = event.get('bucketName')
        overwrite = event.get('overwrite', True)

        # 必須パラメータのチェック
        missing_params = []
        if not bucket_name:
            missing_params.append('bucketName')
        if not xlsx_key:
            missing_params.append('xlsxKey')
        if not csv_key:
            missing_params.append('csvKey')


        if missing_params:
            error_msg = f"Missing required parameters: {', '.join(missing_params)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}

        # skipDuplicatesがTrueの場合、CSVファイルが既に存在するかチェック
        if not overwrite:
            try:
                s3_client.head_object(Bucket=bucket_name, Key=csv_key)
                # オブジェクトが存在する場合、処理をスキップ
                logger.info(f"CSV file already exists and overwrite is False. Skipping: {csv_key}")
                return {"status": "skipped", "message": f"CSV file already exists: {csv_key}"}
            except s3_client.exceptions.NoSuchKey:
                # オブジェクトが存在しない場合、処理を続行
                logger.info(f"CSV file does not exist. Proceeding with conversion: {csv_key}")
            except Exception as e:
                # head_objectでその他のエラーが発生した場合
                logger.warning(f"Error checking if CSV file exists: {str(e)}. Proceeding with conversion.")

        # 一時ファイル用のディレクトリ
        with tempfile.TemporaryDirectory() as tmp_dir:
            # S3からExcelファイルをダウンロード
            xlsx_local_path = os.path.join(tmp_dir, os.path.basename(xlsx_key))
            logger.info(f"Downloading {xlsx_key} from S3 bucket {bucket_name}")
            s3_client.download_file(bucket_name, xlsx_key, xlsx_local_path)

            # CSVファイルの一時パス
            csv_local_path = os.path.join(tmp_dir, os.path.basename(csv_key))

            # 変換処理
            success = convert_xlsx_to_csv(xlsx_local_path, csv_local_path)
            if not success:
                return {"status": "error", "message": "Conversion failed"}

            # S3にアップロード
            logger.info(f"Uploading to S3: {csv_key}")
            s3_client.upload_file(csv_local_path, bucket_name, csv_key)

            return {"status": "success"}

    except Exception as e:
        error_msg = f"Error processing file: {str(e)}"
        logger.error(error_msg)
        return {"status": "error", "message": error_msg}
