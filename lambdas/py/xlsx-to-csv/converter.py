import pandas as pd
import logging

logger = logging.getLogger()
logger.setLevel(logging.INFO)

def convert_xlsx_to_csv(xlsx_path: str, csv_path: str) -> bool:
    """
    Excelファイルを読み込み、CSVファイルに変換する

    Args:
        xlsx_path: Excelファイルのパス
        csv_path: 出力するCSVファイルのパス

    Returns:
        bool: 変換が成功したかどうか
    """
    try:
        logger.info(f"Converting {xlsx_path} to {csv_path}")
        # Pandasを使用してExcelファイルを読み込み
        df = pd.read_excel(xlsx_path)

        # CSVとして保存（日本語対応）
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')

        logger.info(f"Successfully converted {xlsx_path} to {csv_path}")
        return True
    except Exception as e:
        logger.error(f"Error converting {xlsx_path}: {str(e)}")
        return False
