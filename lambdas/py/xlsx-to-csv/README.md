# xlsx-to-csv Lambda Function

S3バケット内のExcelファイル（xlsx）をCSVファイルに変換するAWS Lambda関数です。
このLambda関数は汎用的に設計されており、変換元と変換先のS3パスを指定するだけで使用できます。

## 機能概要

- S3バケット内の指定されたExcelファイルをダウンロード
- Pandasを使用してCSVに変換（日本語対応、UTF-8 with BOMで出力）
- 変換したCSVファイルを指定のS3パスにアップロード

## 使用方法

Lambdaのイベント入力として以下のJSONを指定します：

```json
{
  "xlsxKey": "data/source/example.xlsx",
  "csvKey": "data/output/example.csv"
}
```

## 開発環境構築

1. Python 3.13の仮想環境を作成
   ```bash
   python3.13 -m venv .venv
   source .venv/bin/activate  # Linux/macOS
   # または
   .\.venv\Scripts\activate  # Windows
   ```

2. 依存パッケージのインストール
   ```bash
   pip install -r requirements.txt
   ```

3. テストの実行
   ```bash
   pytest tests/
   ```

## デプロイ

このLambda関数はCDKを使用してデプロイされます。
詳細は`packages/cdk/lib/lambdas/xlsx-to-csv.ts`を参照してください。
