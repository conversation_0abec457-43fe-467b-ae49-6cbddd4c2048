# Excel(xlsx) → CSV 変換 Lambda

## 概要

S3バケット内のExcelファイル(*.xlsx)をCSV形式に変換し、指定されたS3パスにアップロードするLambda関数です。

Lambda関数は汎用的に設計されており、入力パラメータとして変換元のxlsxファイルのパス(`xlsxKey`)と変換先のcsvファイルのパス(`csvKey`)を受け取ります。

## 実装仕様

### Pythonバージョン

**Python 3.13** を使用しています。これはAWS Lambda環境で利用可能な最新バージョンであり、開発環境と本番環境で同一のバージョンを使用するためです。

### 利用ライブラリ

- **pandas**: Excel→CSV変換処理
- **openpyxl**: Excel読み込み
- **boto3**: AWS SDK for Python (S3操作)

### プロジェクト構成

```plaintext
lambdas/py/xlsx-to-csv/
├── src/                      # ソースコード
│   ├── __init__.py
│   ├── lambda_function.py    # Lambda関数のエントリポイント（S3操作含む）
│   └── converter.py          # Excel→CSV変換ロジック
├── tests/                    # テストコード
│   ├── __init__.py
│   └── test_converter.py     # 変換処理のユニットテスト
├── requirements.txt          # 依存パッケージ
├── .gitignore                # Git除外設定
├── README.md                 # プロジェクト説明
├── pyproject.toml            # Pythonプロジェクト設定
└── PLAN.md                   # 本ファイル
```

### Lambda関数のインターフェース

#### 入力

- `event` オブジェクトに以下のプロパティを含む（すべて必須）
  - `bucketName`: 操作対象のS3バケット名（例: `my-s3-bucket`）
  - `xlsxKey`: 変換対象のExcelファイルのS3キー（例: `data/source/example.xlsx`）
  - `csvKey`: 保存先のCSVファイルのS3キー（例: `data/output/example.csv`）

#### 出力

- 処理結果を含むJSONオブジェクト
- 成功時レスポンス例:
  ```json
  {
    "status": "success"
  }
  ```
- 失敗時レスポンス例:
  ```json
  {
    "status": "error",
    "message": "エラーメッセージ"
  }
  ```

#### 呼び出し例

```typescript
// CDK/TypeScriptからの呼び出し例
const lambdaClient = new LambdaClient();
const response = await lambdaClient.invoke({
  FunctionName: 'xlsx-to-csv-function',
  Payload: JSON.stringify({
    xlsxKey: 'data/source/sample.xlsx',
    csvKey: 'data/output/sample.csv',
    bucketName: 'my-s3-bucket'
  }),
});
const result = JSON.parse(Buffer.from(response.Payload).toString());
console.log(`変換ステータス: ${result.status}`);
```

### Lambda関数の処理フロー

1. イベントから必須パラメータ `xlsxKey`、`csvKey`、`bucketName` を取得
2. 必須パラメータが欠けている場合はエラーを返す
3. S3から対象のExcelファイルを一時ディレクトリにダウンロード
4. Pandasを使用してExcelファイルをCSVに変換
5. 変換したCSVファイルをS3の指定パス（`csvKey`）にアップロード
6. 一時ファイルを削除

## CDK 実装

Lambda関数は **TransitStack** でデプロイされ、Transit関連のS3バケットに対して動作します。

### CDKコード（`packages/cdk/lib/environment/transit-stack/lambdas/xlsx-to-csv.ts`）

```typescript
import * as path from 'node:path';
import * as cdk from 'aws-cdk-lib';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import type * as s3 from 'aws-cdk-lib/aws-s3';
import * as s3n from 'aws-cdk-lib/aws-s3-notifications';
import type { Construct } from 'constructs';

import { naming } from 'common';

import { rootDir } from '../../../../util/funcs';
import type { EnvStackProps } from '../../../../util/type';

type XlsxToCsvProps = EnvStackProps & {
  buckets: cdk.aws_s3.Bucket[];
};

/**
 * Excel(xlsx)ファイルをCSVに変換するLambda関数を作成
 */
export const createXlsxToCsv = (scope: Construct, props: XlsxToCsvProps): lambda.Function => {
  const { envKey, buckets } = props;
  const name = 'xlsx-to-csv';
  const functionName = naming(envKey, name);
  const policies: cdk.aws_iam.PolicyStatement[] = buckets.map(
    (bucket) =>
      new cdk.aws_iam.PolicyStatement({
        effect: cdk.aws_iam.Effect.ALLOW,
        resources: [`${bucket.bucketArn}/*`],
        actions: ['s3:GetObject', 's3:PutObject'],
      }),
  );

  // Lambda関数の作成
  const func = new lambda.Function(scope, `lambda-${name}`, {
    functionName,
    runtime: lambda.Runtime.PYTHON_3_13,
    handler: 'src.lambda_function.lambda_handler',
    code: lambda.Code.fromAsset(path.resolve(rootDir, 'lambdas/py/xlsx-to-csv')),
    memorySize: 512,
    timeout: cdk.Duration.seconds(60),
    initialPolicy: policies,
    environment: {
      ENV: envKey,
    },
    description: 'Excelファイル(xlsx)をCSV形式に変換するLambda関数',
  });

  return func;
};
```

### TransitStack内での使用（`packages/cdk/lib/environment/transit-stack/transit-stack.ts`）

```typescript
import * as cdk from 'aws-cdk-lib';
import type { Construct } from 'constructs';

import { tenants } from 'models';

import type { EnvStackProps } from '../../../util/type';
import { createTransitLambda } from './lambdas/transit';
import { createXlsxToCsv } from './lambdas/xlsx-to-csv';
import { createS3 } from './s3';

type Props = EnvStackProps;
export class TransitStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: Props) {
    super(scope, id, props);

    const buckets = tenants
      .filter((t) => t.transit)
      .map((t) => createS3(this, { ...props, tenant: t }));
    createTransitLambda(this, { ...props, buckets });
    createXlsxToCsv(this, { ...props, buckets });
  }
}
```

## 開発とテスト

### 環境セットアップ

1. **Python 3.13のインストール**
   ```bash
   pyenv install 3.13.0
   pyenv local 3.13.0
   ```

2. **仮想環境の作成と有効化**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # macOS/Linux
   ```

3. **依存パッケージのインストール**
   ```bash
   # 開発・テスト用
   pip install -r requirements.txt
   ```

### テスト

#### ユニットテスト

```bash
python -m pytest tests/test_converter.py -v
```

ユニットテストでは、変換処理（`converter.py`）のみをテストし、S3操作は含みません。
このアプローチにより、AWS認証情報やS3環境に依存せず、変換ロジックの正確性のみを検証できます。

初期段階では統合テスト（S3操作を含む）も検討しましたが、AWS認証情報の設定やmotoライブラリによるモックの複雑さを考慮し、ユニットテストのみに集中する方針としました。

## デプロイ

このLambda関数は、TransitStackの一部として自動的にデプロイされます。

1. **CDKによるデプロイ**
   ```bash
   cd packages/cdk
   npm run deploy:dev   # 開発環境へのデプロイ
   npm run deploy:stage # ステージング環境へのデプロイ
   npm run deploy:prod  # 本番環境へのデプロイ
   ```

デプロイ時、以下の内容が自動的に処理されます：

- Python 3.13ランタイムでのLambda関数の作成
- 必要なIAMポリシーの設定（S3バケットへのアクセス権）
- 環境変数の設定
- ライブラリを含むソースコードのパッケージング（requirements.txtに記載された依存ライブラリを含むZipファイルの作成）

## 利用上の注意点

1. **S3バケット名**
   - Lambda関数は、`event`の`bucketName`プロパティから操作対象のS3バケット名を取得します
   - `bucketName`は必須パラメータであり、指定されていない場合はエラーを返します

2. **メモリとタイムアウト設定**
   - メモリ: 512MB
   - タイムアウト: 60秒
   - 大きなExcelファイルを処理する場合は、CDK側でこれらの設定を調整することを検討してください

3. **エラーハンドリング**
   - Lambda関数は処理中のエラーを適切にキャッチし、CloudWatchログに記録します
   - エラーが発生した場合、レスポンスには `{ "status": "error", "message": "エラー詳細" }` が含まれます

## 今後の改善点

1. シート指定オプションの追加（シート名や番号を指定可能に）
2. ファイル形式オプションの拡張（CSV以外の出力形式対応）
3. エラー通知メカニズムの改善
4. バッチ処理対応（複数ファイルの一括変換）
5. 変換オプション設定の拡張（文字コード、区切り文字など）

## 主要コンポーネント

### lambda_function.py
- Lambdaのハンドラー関数
- eventからxlsxKey、csvKey、bucketNameの取得
- S3からのファイルダウンロード処理
- S3へのファイルアップロード処理
- 変換処理の呼び出し
- エラーハンドリングとログ出力

### converter.py
- ExcelファイルのCSV変換処理
- Pandasを使用したデータフレーム操作
- 日本語文字コード対応

1. 想定されるエラー
   - Excelファイルの形式不正
   - S3アクセス権限エラー
   - 処理タイムアウト
   - メモリ不足

2. エラー通知方法
   - CloudWatch Alarmsの設定
   - エラーログの出力形式の統一

## 実装手順

### 1. 開発環境構築

1. Pythonの仮想環境を作成
   ```bash
   # Python 3.13をインストール（まだインストールされていない場合）
   # macOS
   brew install python@3.13
   # または
   pyenv install 3.13.0

   # 仮想環境作成
   python3.13 -m venv .venv

   # 仮想環境有効化
   source .venv/bin/activate  # macOS/Linux
   .\venv\Scripts\activate    # Windows
   ```

2. プロジェクト構成を作成
   ```bash
   mkdir -p src tests
   touch src/__init__.py src/lambda_function.py src/converter.py
   touch tests/__init__.py tests/test_converter.py
   touch requirements.txt .gitignore README.md pyproject.toml
   ```

3. 必要なパッケージをインストール
   ```bash
   pip install pandas openpyxl boto3 pytest moto
   pip freeze > requirements.txt
   ```

4. .gitignore ファイルの設定
   ```bash
   cat << EOF > .gitignore
   .venv/
   __pycache__/
   *.py[cod]
   *$py.class
   .pytest_cache/
   .coverage
   htmlcov/
   .DS_Store
   EOF
   ```

5. pyproject.toml の作成
   ```bash
   cat << EOF > pyproject.toml
   [build-system]
   requires = ["setuptools>=61.0"]
   build-backend = "setuptools.build_meta"

   [project]
   name = "xlsx-to-csv"
   version = "0.1.0"
   description = "Excel to CSV converter Lambda function"
   requires-python = ">=3.13,<3.14"
   dependencies = [
       "pandas",
       "openpyxl",
       "boto3",
   ]

   [project.optional-dependencies]
   test = [
       "pytest",
       "moto",
   ]
   EOF
   ```

### 2. コード実装

1. `converter.py` の実装
   ```python
   import pandas as pd
   import logging

   logger = logging.getLogger()
   logger.setLevel(logging.INFO)

   def convert_xlsx_to_csv(xlsx_path: str, csv_path: str) -> bool:
       """
       Excelファイルを読み込み、CSVファイルに変換する

       Args:
           xlsx_path: Excelファイルのパス
           csv_path: 出力するCSVファイルのパス

       Returns:
           bool: 変換が成功したかどうか
       """
       try:
           logger.info(f"Converting {xlsx_path} to {csv_path}")
           # Pandasを使用してExcelファイルを読み込み
           df = pd.read_excel(xlsx_path)

           # CSVとして保存（日本語対応）
           df.to_csv(csv_path, index=False, encoding='utf-8-sig')

           logger.info(f"Successfully converted {xlsx_path} to {csv_path}")
           return True
       except Exception as e:
           logger.error(f"Error converting {xlsx_path}: {str(e)}")
           return False
   ```

2. `lambda_function.py` の実装
   ```python
   import os
   import tempfile
   import boto3
   import logging
   from .converter import convert_xlsx_to_csv

   logger = logging.getLogger()
   logger.setLevel(logging.INFO)
   s3_client = boto3.client('s3')

   def lambda_handler(event, context):
       """
       Lambda関数のエントリポイント

       Args:
           event: イベントオブジェクト（xlsxKey、csvKeyを含む）
           context: Lambda実行コンテキスト

       Returns:
           dict: 処理結果を含むオブジェクト
       """
       try:
           # イベントからキーを取得
           xlsx_key = event.get('xlsxKey')
           csv_key = event.get('csvKey')

           if not xlsx_key or not csv_key:
               error_msg = "Missing required parameters: xlsxKey and csvKey"
               logger.error(error_msg)
               return {"status": "error", "message": error_msg}

           # バケット名は環境変数から取得
           bucket_name = os.environ.get('S3_BUCKET_NAME', 'default-bucket-name')

           # 一時ファイル用のディレクトリ
           with tempfile.TemporaryDirectory() as tmp_dir:
               # S3からExcelファイルをダウンロード
               xlsx_local_path = os.path.join(tmp_dir, os.path.basename(xlsx_key))
               logger.info(f"Downloading {xlsx_key} from S3 bucket {bucket_name}")
               s3_client.download_file(bucket_name, xlsx_key, xlsx_local_path)

               # CSVファイルの一時パス
               csv_local_path = os.path.join(tmp_dir, os.path.basename(csv_key))

               # 変換処理
               success = convert_xlsx_to_csv(xlsx_local_path, csv_local_path)
               if not success:
                   return {"status": "error", "message": "Conversion failed"}

               # S3にアップロード
               logger.info(f"Uploading to S3: {csv_key}")
               s3_client.upload_file(csv_local_path, bucket_name, csv_key)

               return {"status": "success"}

       except Exception as e:
           error_msg = f"Error processing file: {str(e)}"
           logger.error(error_msg)
           return {"status": "error", "message": error_msg}
   ```

3. テストコードの実装
   ```python
   # tests/test_converter.py
   import os
   import pandas as pd
   import pytest
   from src.converter import convert_xlsx_to_csv

   @pytest.fixture
   def sample_xlsx():
       # テスト用のExcelファイルを作成
       df = pd.DataFrame({
           'ID': [1, 2, 3],
           '名前': ['田中', '鈴木', '佐藤'],
           '年齢': [25, 30, 35]
       })

       file_path = 'test_sample.xlsx'
       df.to_excel(file_path, index=False)

       yield file_path

       # テスト後にファイルを削除
       if os.path.exists(file_path):
           os.remove(file_path)

       if os.path.exists('test_output.csv'):
           os.remove('test_output.csv')

   def test_convert_xlsx_to_csv(sample_xlsx):
       # 変換処理を実行
       result = convert_xlsx_to_csv(sample_xlsx, 'test_output.csv')

       # 変換成功を確認
       assert result is True

       # 出力ファイルが存在することを確認
       assert os.path.exists('test_output.csv')

       # 内容が正しいことを確認
       df = pd.read_csv('test_output.csv')
       assert len(df) == 3
       assert '名前' in df.columns
       assert df['名前'][0] == '田中'
   ```

### 3. テスト実行

1. ユニットテストの実行
   ```bash
   cd lambdas/py/xlsx-to-csv
   source .venv/bin/activate
   pytest -v tests/
   ```

2. 統合テストの設定（S3操作を含む）
   ```python
   # tests/test_integration.py
   import os
   import boto3
   import pandas as pd
   import pytest
   from moto import mock_s3
   from src.lambda_function import lambda_handler

   @pytest.fixture
   def aws_credentials():
       """モックAWS認証情報を設定"""
       os.environ['AWS_ACCESS_KEY_ID'] = 'testing'
       os.environ['AWS_SECRET_ACCESS_KEY'] = 'testing'
       os.environ['AWS_SECURITY_TOKEN'] = 'testing'
       os.environ['AWS_SESSION_TOKEN'] = 'testing'
       os.environ['AWS_DEFAULT_REGION'] = 'ap-northeast-1'

   @pytest.fixture
   def s3(aws_credentials):
       """モックS3サービス"""
       with mock_s3():
           yield boto3.client('s3', region_name='ap-northeast-1')

   @pytest.fixture
   def s3_bucket(s3):
       """モックS3バケット作成"""
       bucket_name = 'maphin-transit-for-shinagawa-stage'
       s3.create_bucket(
           Bucket=bucket_name,
           CreateBucketConfiguration={'LocationConstraint': 'ap-northeast-1'}
       )
       return bucket_name

   @pytest.fixture
   def sample_xlsx():
       """サンプルExcelファイル作成"""
       df = pd.DataFrame({
           'ID': [1, 2, 3],
           '名前': ['田中', '鈴木', '佐藤'],
           '年齢': [25, 30, 35]
       })

       file_path = 'test_sample.xlsx'
       df.to_excel(file_path, index=False)

       yield file_path

       if os.path.exists(file_path):
           os.remove(file_path)

   def test_lambda_handler(s3, s3_bucket, sample_xlsx):
       """Lambda関数のエンドツーエンドテスト"""
       # S3にExcelファイルをアップロード
       xlsx_key = 'test/source/test_sample.xlsx'
       s3.upload_file(sample_xlsx, s3_bucket, xlsx_key)

       # CSV保存先のキー
       csv_key = 'test/output/test_sample.csv'

       # Lambda関数を呼び出し
       event = {
           'xlsxKey': xlsx_key,
           'csvKey': csv_key
       }
       response = lambda_handler(event, None)

       # 正常終了を確認
       assert response['status'] == 'success'

       # S3にCSVファイルが保存されていることを確認
       response = s3.list_objects_v2(Bucket=s3_bucket, Prefix=csv_key)
       assert 'Contents' in response
       assert response['Contents'][0]['Key'] == csv_key
   ```

3. 統合テスト実行
   ```bash
   pip install moto
   pytest -v tests/test_integration.py
   ```

### 4. CDK実装

1. CDKコードの実装
   ```typescript
   // packages/cdk/lib/lambdas/xlsx-to-csv.ts
   import * as cdk from 'aws-cdk-lib';
   import * as lambda from 'aws-cdk-lib/aws-lambda';
   import * as iam from 'aws-cdk-lib/aws-iam';
   import * as s3 from 'aws-cdk-lib/aws-s3';
   import * as s3n from 'aws-cdk-lib/aws-s3-notifications';
   import { Construct } from 'constructs';
   import * as path from 'path';

   export interface XlsxToCsvProps {
     transitBucket: s3.IBucket;
     stageName: string;
   }

   export class XlsxToCsvFunction extends Construct {
     public readonly lambdaFunction: lambda.Function;

     constructor(scope: Construct, id: string, props: XlsxToCsvProps) {
       super(scope, id);

       // Lambda関数の作成
       this.lambdaFunction = new lambda.Function(this, 'Function', {
         runtime: lambda.Runtime.PYTHON_3_13,
         handler: 'src.lambda_function.lambda_handler',
         code: lambda.Code.fromAsset(path.join(__dirname, '../../../../lambdas/py/xlsx-to-csv')),
         memorySize: 512,
         timeout: cdk.Duration.seconds(60),
         environment: {
           S3_BUCKET_NAME: props.transitBucket.bucketName,
         },
       });

       // S3アクセス権限付与
       props.transitBucket.grantReadWrite(this.lambdaFunction);

       // S3イベント通知の設定（オプション - 必要に応じてコメントアウトを解除）
       /*
       props.transitBucket.addEventNotification(
         s3.EventType.OBJECT_CREATED,
         new s3n.LambdaDestination(this.lambdaFunction),
         { suffix: '.xlsx' }
       );
       */
     }
   }
   ```

2. スタックへの組み込み
   ```typescript
   // packages/cdk/lib/stacks/transit-stack.ts
   import * as cdk from 'aws-cdk-lib';
   import * as s3 from 'aws-cdk-lib/aws-s3';
   import { Construct } from 'constructs';
   import { XlsxToCsvFunction } from '../lambdas/xlsx-to-csv';

   export class TransitStack extends cdk.Stack {
     constructor(scope: Construct, id: string, props?: cdk.StackProps) {
       super(scope, id, props);

       // 既存のS3バケットを参照
       const targetBucket = s3.Bucket.fromBucketName(
         this, 'TargetBucket', process.env.S3_BUCKET_NAME || 'your-bucket-name'
       );

       // XlsxToCsv Lambda関数を追加
       new XlsxToCsvFunction(this, 'XlsxToCsv', {
         transitBucket: targetBucket,
         stageName: process.env.STAGE_NAME || 'dev',
       });
     }
   }
   ```

### 5. デプロイ

1. Lambda関数のパッケージング
   ```bash
   cd lambdas/py/xlsx-to-csv
   pip install -r requirements.txt
   zip -r function.zip .
   ```

2. CDKデプロイ
   ```bash
   cd packages/cdk
   npm run build
   npm run deploy -- --require-approval never
   ```

3. デプロイ確認
   ```bash
   aws lambda list-functions --query "Functions[?FunctionName.contains(@, 'xlsx-to-csv')]"
   ```

### 6. 動作確認

1. テスト用Excelファイルを作成

2. Lambdaテスト実行
   ```bash
   aws lambda invoke \
     --function-name FUNCTION_NAME \
     --payload '{"xlsxKey": "data/source/test.xlsx", "csvKey": "data/output/test.csv"}' \
     response.json

   cat response.json
   ```

3. 結果確認
   ```bash
   aws s3 ls s3://YOUR_BUCKET_NAME/data/output/
   aws s3 cp s3://YOUR_BUCKET_NAME/data/output/test.csv ./
   cat test.csv
   ```
